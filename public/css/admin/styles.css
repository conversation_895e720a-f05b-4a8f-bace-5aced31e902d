.msg {
  display: flex;
  flex-direction: column;
  padding: 10px;
  gap: 10px;
  justify-content: space-between;
  width: 80%;
  border-radius: 6px;
}

.btn-delete-msg {
  text-align: right;
  font-size: 14px;
}
.header {
  width: 80px;
  height: 30px;
  display: grid;
  place-items: center;
}
.content {
  display: flex;
  justify-content: space-between;
}
.action:hover {
  cursor: pointer;
}
.footer {
  text-align: right;
}
.btn-delete {
  width: 120px;
}
.panel-body {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.msg-scroll-reverse {
  overflow: auto;
  max-height: 600px;
  display: flex;
  flex-direction: column-reverse;
  gap: 10px;
  padding-right: 15px;
}
.flex {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.flex-1 {
  flex: 1;
}
.format-text {
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}
.hide {
  display: none;
}
.modal {
  text-align: center;
}

.close-modal {
  position: absolute;
  top: 10px;
  right: 10px;
  text-align: right;
}

@media screen and (min-width: 768px) {
  .modal:before {
    display: inline-block;
    vertical-align: middle;
    content: ' ';
    height: 100%;
  }
}

.modal-dialog {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}

.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.pr-0 {
  padding-right: 0px;
}
.pl-15 {
  padding-left: 15px;
}
.pr-15 {
  padding-right: 15px;
}
.w-480 {
  width: 480px;
}
.p-20 {
  padding: 20px;
}
.p-10 {
  padding: 10px;
}
.mt-0 {
  margin-top: 0px;
}
.mt-10 {
  margin-top: 10px;
}
.mb-0 {
  margin-bottom: 0;
}
.mb-10 {
  margin-bottom: 10px;
}
.mb-20 {
  margin-bottom: 20px;
}

.mr-15 {
  margin-right: 8.25px;
}
.flex-center {
  display: flex;
  justify-content: center;
}
.flex-right {
  display: flex;
  justify-content: flex-end;
}
.flex-wrap {
  display: flex;
  flex-wrap: wrap;
}
.flex-direction-column {
  flex-direction: column;
}
.gap-10 {
  gap: 10px;
}
.gap-20 {
  gap: 20px;
}
#tab {
  padding: 20px;
  border: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.hidden {
  display: none;
}
.html-view {
  padding: 10px;
  border: 1px solid #ddd;
  margin-top: 10px;
}

.width-100 {
  width: 100%;
}
.width-90 {
  width: 90%;
}
.width-80 {
  width: 80%;
}
.width-50 {
  width: 50%;
}
.width-33 {
  width: 33%;
}
.width-25 {
  width: 25%;
}
.width-20 {
  width: 20%;
}
.width-10 {
  width: 10%;
}
.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.color-red {
  color: red;
}

form[name='threadSearchForm'] select,
form[name='threadSearchForm'] input,
form[name='threadSearchForm'] button {
  margin-bottom: 20px;
}

form[name='threadSearchForm'] input[name='search'] {
  min-width: 300px;
}

#pageThreads {
  margin-bottom: 15px;
}

.container-thread-data {
  clear: both;
}

#pageThreads a {
  background-color: #808080;
  color: #ffffff;
  margin-left: 15px;
  border-radius: 5px;
}

.error {
  position: relative;
  color: red;
  text-align: start;
  margin-top: -10px;
  font-weight: 500;
}

.task-block {
  width: 40%;
  float: right;
}
.message-block {
  width: 55%;
  position: relative;
  float: left;
}

.task-info {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0px 10px;
}
.task-completed {
  background-color: #ddd;
}
.task-info-title {
  align-items: center;
}
.w-40 {
  width: 40%;
}
.w-33 {
  width: 33%;
}
.hide-textarea {
  border: none;
  outline: none;
  width: 100%;
  resize: none;
}

.font-bold {
  font-weight: 700;
}
.attachment-preview {
  width: 80px;
  height: 80px;
  border-radius: 6px;
}
.remove-attachment {
  display: grid;
  place-items: center;
  font-size: 14px;
  width: 24px;
  height: 24px;
  background-color: rgb(255 255 255 / 79%);
  cursor: pointer;
  color: #3d3c3c;
  text-align: center;
  position: absolute;
  top: 0px;
  right: 0px;
  border-radius: 0 6px 0 0;
}

.row-reverse {
  flex-direction: row-reverse;
}

.msg-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.msg-admin {
  background-size: 59%;
  background-repeat: no-repeat;
  background-position: center;
}
.msg-shop {
  background-size: cover;
}
.bg-green {
  background-color: #33803a;
}
.bg-msg-memo {
  border: 1px solid #ddd;
}
.bg-msg-shop {
  background-color: #3d3c3ccf;
  color: white;
}

.bg-msg-shop .draft-preview a {
  color: #ffffff;
  text-decoration: underline;
}

.bg-msg-admin {
  background-color: #dddddd52;
}

.action-msg {
  min-width: 140px;
  text-align: left;
}
.template-name-text {
  min-width: 120px;
  white-space: normal;
}
.line-height-0 {
  line-height: 0 !important;
}
.top-2 {
  top: 2px !important;
}

form[name='taskSearchForm'] select,
form[name='taskSearchForm'] input,
form[name='taskSearchForm'] button,
form[name='taskSearchForm'] button.datepicker-input {
  margin-bottom: 20px;
  width: 100% !important;
}

form[name='taskSearchForm'] .btn-search,
form[name='searchForm'] .btn-search {
  width: fit-content !important;
  border: 1px solid #ccc;
  background-color: #fff;
}

form[name='taskSearchForm'] button.datepicker-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

form[name='taskSearchForm'] input[type='button'] {
  min-width: 50px !important;
}

form[name='taskSearchForm'] .datepicker-input .clear-updated-at,
form[name='taskSearchForm'] .datepicker-input .clear-confirm-date {
  display: none;
  cursor: pointer;
  margin-right: 15px;
}
.no-border {
  border: 0px !important;
}
.no-border-left {
  border-left: 0px !important;
}
.no-border-right {
  border-right: 0px !important;
  background-color: #ffffff;
}
.pt-0 {
  padding-top: 0px !important;
}
.flex-between-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.draft-preview {
  overflow: none;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

.modal-dialog .modal-body p.title {
  font-size: 16px;
  font-weight: 700;
  margin: 0;
}

.task-data {
  max-height: 1000px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

form[name='threadSearchForm'] .row-group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

form[name='threadSearchForm'] .row-group:first-child {
  margin-bottom: 0;
}

form[name='threadSearchForm'] .form-control {
  flex: 1;
}

form[name='threadSearchForm'] button:focus {
  background-color: unset;
  outline: unset;
}

form[name='threadSearchForm'] button[type='button'] {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

form[name='threadSearchForm'] button[type='button']:hover {
  background-color: unset;
}

form[name='threadSearchForm'] button[type='submit'],
#pageThreads .btn {
  width: fit-content !important;
  border: 1px solid #ccc;
  background-color: #fff;
  color: black;
}

form[name='threadSearchForm'] .datepicker-input .clear-date {
  display: none;
  cursor: pointer;
  margin-right: 15px;
}

form[name='threadSearchForm'] .datepicker-input .text {
  flex-grow: 1;
  text-align: left;
}

form[name='threadSearchForm'] .datepicker-input .icons {
  display: flex;
  align-items: center;
}

.loading-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1000;
  display: grid;
  place-items: center;
}
.tranparent-block {
  opacity: 0.4;
}

#modalUpsertTask label {
  font-weight: normal;
}

#modalUpsertTask .input-datepicker {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#modalUpsertTask .input-datepicker .text {
  padding-left: 5px;
}

#modalUpsertTask .input-datepicker .clear-confirm-date {
  display: none;
  cursor: pointer;
  margin-right: 15px;
}

.datepicker-dropdown {
  z-index: 1030;
}

a:hover {
  text-decoration: none;
  cursor: pointer;
}

.block-resize {
  resize: vertical;
}

[v-cloak] {
  display: none;
}

.loading-container {
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  justify-content: center;
  align-items: center;
  background: white;
}

.bg-gray {
  background-color: #dddddd52;
}

.overflow-anywhere {
  overflow-wrap: anywhere;
}

.overflow-break-word {
  overflow-wrap: break-word;
}

.space-pre-wrap {
  white-space: pre-wrap;
}

.draft-preview a,
.draft-preview a:hover {
  text-decoration: underline;
}

@media screen and (max-width: 768px) {
  form[name='threadSearchForm'] .row-group {
    flex-direction: column;
  }

  .modal-dialog {
    width: 100%;
  }

  .modal-content {
    width: auto;
  }
}

#concurrentMessage .wrap-attachment {
  display: flex;
  align-items: center;
}

#concurrentMessage .wrap-attachment .btn-attachment {
  margin-bottom: 0;
}

#concurrentMessage .wrap-attachment .attachment-list {
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 0 10px;
}

#concurrentMessage .wrap-attachment .attachment-list .attachment-item {
  display: flex;
}

#concurrentMessage .wrap-attachment .attachment-list .attachment-item .delete-attachment {
  color: #626262;
  opacity: 1;
  font-weight: normal;
  padding-left: 20px;
  padding-right: 10px;
}

#concurrentMessage span a.btn-secondary {
  padding: 0 10px;
}

#concurrentMessage .attachment-preview,
#messagePreviewContainer .attachment-preview {
  width: 49px;
  height: 36px;
  margin-right: 5px;
  background-position: center;
  border-radius: unset;
}

#concurrentMessage .bg-msg-admin:before,
#messagePreviewContainer .bg-msg-admin:before {
  content: '';
  display: block;
  height: 18px;
  width: 18px;
  color: #f4f4f4;
  position: absolute;
  top: auto;
  right: auto;
  left: auto;
  border-width: 9px;
  border-style: solid;
  border-color: transparent #f4f4f4 transparent transparent;
  margin-left: -27px;
}

.loading-wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1051;
  display: grid;
  place-items: center;
  background-color: rgba(0, 0, 0, 0.5);
}

.loading-div-wrap {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1005;
  display: grid;
  place-items: center;
  margin-top: 5%;
  margin-bottom: 5%;
}

#infinite-list .msg-system,
#concurrentMessage .msg-system {
  display: inline-block;
  padding: 8px 10px;
  background-color: #fff;
  border: 2px #e3e3e3 solid;
  margin-top: 5px;
  margin-bottom: 10px;
  border-radius: 4px;
  font-size: 1.2rem;
}

.container-broadcast-message .attachment-list {
  display: flex;
}

.container-broadcast-message .attachment-list .attachment-item {
  margin-right: 7px;
}
.form-group-item {
  border: 2px #e3e3e3 solid;
  padding: 6px 12px;
  margin-bottom: 15px;
  margin-left: 1%;
}
.group-item {
  margin-left: 5%;
}
.store-item {
  margin-left: 5%;
  margin-right: 5%;
}
.csv-button {
  border: 1px solid #ccc;
  padding: 5px 10px;
}
table.threads td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}
.modal-footer span.text-danger {
  display: block; /* Make the span a block element */
  margin-top: 8px; /* Add some space between the button and the error message */
}
