let csrfToken = document.currentScript.getAttribute('csrf');
const axiosClient = axios.create({});
axiosClient.interceptors.request.use(function (config) {
  config.headers['X-CSRF-TOKEN'] = csrfToken;
  config.headers['Accept'] = 'application/json';
  return config;
});
axiosClient.interceptors.response.use(
  function (response) {
    if (response.data._csrf) {
      csrfToken = response.data._csrf;
    }
    return response;
  },
  function (error) {
    if (error.response.status === 401) {
      return window.location.reload();
    }

    return Promise.reject(error);
  },
);
