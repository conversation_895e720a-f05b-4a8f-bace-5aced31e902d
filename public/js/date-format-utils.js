/* eslint-disable @typescript-eslint/no-unused-vars */
/*
 * Format for updatedAt
 * 今日の場合はHH:mm
 * 今年の場合はMM月DD日
 * それ以外はYYYY年MM月DD日
 */
var formatUpdatedAt = function (date) {
  if (!date) return '';
  const now = dayjs();
  const input = dayjs(date);

  return now.isSame(input, 'day')
    ? input.format('HH:mm')
    : now.isSame(input, 'year')
    ? input.format('MM月DD日')
    : input.format('YYYY年MM月DD日');
};

/*
 * Format for createdAt
 * 今年の場合はMM月DD日 HH:mm
 * それ以外はYYYY年MM月DD日 HH:mm
 */
var formatCreatedAt = function (date) {
  if (!date) return '';
  const now = dayjs();
  const input = dayjs(date);
  if (now.isSame(input, 'day')) {
    return input.format('今日 HH:mm');
  }
  if (now.isSame(input, 'year')) {
    return input.format('MM月DD日 HH:mm');
  }
  return input.format('YYYY年MM月DD日 HH:mm');
};

/*
 * Format for createdAt broadcast-message
 * 今年の場合はMM月DD日 HH:mm
 * それ以外はYYYY年MM月DD日 HH:mm
 */
var formatCreatedAtYear = function (date) {
  if (!date) return '';
  const input = dayjs(date);
  return input.format('YYYY/MM/DD HH:mm');
};

var formatShopWebCreatedAt = function (date) {
  if (!date) return '';
  const now = dayjs();
  const input = dayjs(date);
  if (now.isSame(input, 'day')) {
    return input.format('今日 HH:mm');
  }
  if (now.isSame(input, 'year')) {
    return input.format('M月D日 HH:mm');
  }
  return input.format('YYYY年M月D日 HH:mm');
};
/*
 * Format for confirmedDate
 * 今年の場合はMM月DD日
 * それ以外はYYYY年MM月DD日
 */
var formatConfirmedDate = function (date) {
  if (!date) return '';
  const now = dayjs();
  const input = dayjs(date);

  return now.isSame(input, 'year') ? input.format('MM月DD日') : input.format('YYYY年MM月DD日');
};

/*
 * Format display data of datepicker
 * @param string dateString
 * @param string defaultVal
 */
var formatDateFilter = function (dateString, defaultVal = '') {
  if (!dateString) return defaultVal;
  const date = dayjs(dateString);
  if (!date.isValid()) return defaultVal;
  return date.format('YYYY年MM月DD日');
};

var formatGroupMessagesDate = function (date) {
  if (!date) return '';
  const now = dayjs();
  const input = dayjs(date).locale('ja');
  if (now.isSame(input, 'day')) {
    return input.format('今日');
  }
  if (now.isSame(input, 'year')) {
    return input.format('M/D(dd)');
  }
  return input.format('YYYY/M/D(dd)');
};

var dateUtils = {
  formatUpdatedAt: formatUpdatedAt,
  formatCreatedAt: formatCreatedAt,
  formatCreatedAtYear: formatCreatedAtYear,
  formatShopWebCreatedAt: formatShopWebCreatedAt,
  formatConfirmedDate: formatConfirmedDate,
  formatDateFilter: formatDateFilter,
  formatGroupMessagesDate: formatGroupMessagesDate,
};
