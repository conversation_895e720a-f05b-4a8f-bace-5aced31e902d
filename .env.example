# This file is a "template" of which env vars need to be defined for your application
# Common vars to run application
# (Required) Application configuration
APP_PORT=3000
# local|kaizen|prod
APP_ENV=local
APP_URL=https://xxxxx-kaizen.wkwk.io
TERRY_URL=https://xxxxx-kaizen.wkwk.io
# (Optional) Config server keep alive timeout. Default is 65000 (65s)
SERVER_KEEP_ALIVE_TIMEOUT=65000
# (Optional) Config server headers timeout. Default is 70000 (70s)
SERVER_HEADERS_TIMEOUT=70000

# (Required) Application configuration for static assets.
# If you use static domain for static assets then only to need set value APP_STATIC_BASE_URL and set empty for APP_STATIC_PREFIX
# Example: https://(staticDomainPrefix)/consulting-message
# Then please set
# APP_STATIC_BASE_URL=https://(staticDomainPrefix)/consulting-message
# APP_STATIC_PREFIX=
# Otherwise, you need to set APP_STATIC_BASE_URL and APP_STATIC_PREFIX
# Example: https://(staticDomainPrefix)/consulting-message/*
# Then please set
# APP_STATIC_BASE_URL=https://(staticDomainPrefix)
# APP_STATIC_PREFIX=consulting-message
# Updated 2025-01-01: If you want to use cnd domain you must remove APP_STATIC_URL and must set value for INFRA_FRONTEND_STATIC_DOMAIN is cnd domain.
# Ensure that you also config cloudfront to foward request to INFRA_FRONTEND_STATIC_DOMAIN
APP_STATIC_BASE_URL=https://xxxxx-kaizen.wkwk.io
APP_STATIC_PREFIX=consulting-message
# (Optional) This option need for local development to debug when error occur. Default is false
APP_DEBUG=false
# If set RUN_INITIAL_COMMAND is true, application will run command curama:init when start.
# The goal of this command is to sync data from google and create verified permission policy template.
# (RUN_INITIAL_COMMAND = true is required for first time deploy) Accepted values: true
RUN_INITIAL_COMMAND=

# (Optional) Define timezone for application and database. Default is Asia/Tokyo
TZ=Asia/Tokyo

# (Required) Using replication mode for postgres setting replica database
DATABASE_HOST_REPLICA=
# (Required) Database configuration
DATABASE_DRIVER=postgres
DATABASE_HOST=
DATABASE_USERNAME=
DATABASE_PASSWORD=
DATABASE_PORT=5432
DATABASE_NAME=consulting-message
DATABASE_SCHEMA=public
DATABASE_POOL_SIZE=10
SESSION_KEY=CuramaVietNam
# (Optional) Default is 86400 (1 day)
SESSION_TTL=86400

# (Required) RabbitMQ configuration
RABBITMQ_HOST=127.0.0.1
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_PATH=/

# (Required) Py3 configuration
PY3_BASE_URL=
API_FW_PRINCIPAL_KEY=
INTERNAL_API_DOMAIN=https://internal-api.curama.jp
# (Optional) This option need for local development. It's not necessary for production.
PY3_API_KEY=

# (Optional) HTTP configuration
# HTTP_TIMEOUT default is 60000 (60s)
HTTP_TIMEOUT=60000
# HTTP_MAX_RETRY default is 3
HTTP_MAX_RETRY=3
# HTTP_STATUS_RETRY default is [].
# By default, HttpApiClient always retry for status 408 or 5xx.
# If set HTTP_STATUS_RETRY value, application will retry for status in the list and status 408 or 5xx.
HTTP_STATUS_RETRY=
# HTTP_RETRY_ENDPOINT default is [].
# By default, HttpApiClient always retry for all endpoint.
# If set HTTP_RETRY_ENDPOINT value, application will retry for endpoint in the list.
HTTP_RETRY_ENDPOINT=

# (Required) Redis configuration
REDIS_URL=redis://localhost/

# (Required) AWS configuration
# AWS Cognito App Client Domain. Please includes https://
COGNITO_OAUTH2_DOMAIN=
# AWS Cognito App Client ID
COGNITO_OAUTH2_CLIENT_ID=
# AWS Cognito App Client Secret
COGNITO_OAUTH2_CLIENT_SECRET=
# AWS Cognito App Client Scope
COGNITO_OAUTH2_SCOPES=aws.cognito.signin.user.admin,email,openid,phone,profile

# (Optional) AWS configuration
# AWS ACCESS KEY (Not need to set if you use aws profile)
AWS_ACCESS_KEY=
# AWS SECRET KEY (Not need to set if you use aws profile)
AWS_SECRET_KEY=
# AWS REGION (Not need to set if you use aws profile)
AWS_REGION=ap-northeast-1
# Verified permission policy store id that get from aws verified permission service
VERIFIED_PERMISSION_POLICY_STORE_ID=
# Verified permission user pool id that related to verified permission policy store
VERIFIED_PERMISSION_USER_POOL_ID=

# (Require) Google app account service to synchronize data from google
# Must be admin email of google workspace
GOOGLE_APP_ACCOUNT_SERVICE_EMAIL=
# Must be domain of google workspace
GOOGLE_APP_ACCOUNT_SERVICE_DOMAIN=
# Must be customer id of google workspace
GOOGLE_APP_ACCOUNT_CUSTOMER_ID=
# Google app account service key
GOOGLE_SERVICE_ACCOUNT_KEY=
# Google channel token to verify webhook request using jwttoken
GOOGLE_CHANNEL_TOKEN_SECRET=
# Google channel token aud
GOOGLE_CHANNEL_TOKEN_AUD=

# (Optional) If you want to this microservice canary release, you need to set this variable to true
IS_CANARY_RELEASE_STORE=false

# (Optional) Config for concurrent message sending
CONCURRENT_MESSAGE_SIZE_BATCH=500
CONCURRENT_MESSAGE_MINUTE_DELAY=3600000

# (Required) The base url of lambda function that used for get file from s3
PRIVATE_FILE_HANDLER_URL=
# env that used for get file from s3
PRIVATE_FILE_JWT_SECRET=
PRIVATE_FILE_JWT_AUD=
PRIVATE_FILE_JWT_EXPIRES_IN=

# The s3 bucket name that used for upload file to s3
CONSULTING_MESSAGE_S3_BUCKET_NAME=vietnam.staticfile

# By default, the data will be retrieved from the infrastructureSettings.json file.
# If you want to replace some infrastructure information, set SHOULD_REPLACE_INFRA_SETTING=true. (Default is false)
# Ensure that you also set values for them; these values will replace the data in the infrastructureSettings.json file every time the application starts.
SHOULD_REPLACE_INFRA_SETTING=false
INFRA_FRONTEND_STATIC_DOMAIN=
INFRA_FRONTEND_EXTERNAL_STATIC_CDN=
INFRA_FRONTEND_IMAGE_DOMAIN=
INFRA_GOOGLE_ANALYTIC_TRACKING_ID=
INFRA_GOOGLE_TAGMANAGER_CONTAINER_ID=

# The hostname or IP address of the Redis server.
REDIS_HOST=
# The port on which the Redis server is running.
REDIS_PORT=
# The Redis database index to use (default is 0).
REDIS_DB=
# The password to authenticate with the Redis server, if required.
REDIS_PASSWORD=
# A key prefix to namespace Redis keys, if needed.
REDIS_PRIFIX=

# (Required) Slack webhook configuration
NOTIFICATION_SLACK_WEBHOOK=
PROMOTION_INTERNAL_HOST=

OPENSEARCH_AUTH_TYPE=aws|basic
OPENSEARCH_USERNAME=
OPENSEARCH_PASSWORD=
OPENSEARCH_AWS_REGION=ap-northeast-1
OPENSEARCH_ACCESS_KEY_ID=
OPENSEARCH_SECRET_ACCESS_KEY=
OPENSEARCH_SESSION_TOKEN=
OPENSEARCH_NODE_URL=https://search-curama-vietnam-test-2-bh5lswtfwpbizr32sgohua3zq4.ap-northeast-1.es.amazonaws.com
OPENSEARCH_INDEX=curama_consulting_message