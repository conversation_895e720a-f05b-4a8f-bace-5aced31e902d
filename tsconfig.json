{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": false, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true, "paths": {"@src/*": ["src/*"], "@configs/*": ["src/configs/*"], "@modules/*": ["src/modules/*"], "@providers/*": ["src/providers/*"], "@exceptions/*": ["src/exceptions/*"], "@common/*": ["src/common/*"], "@migrations/*": ["src/migrations/*"], "@curama-auth": ["libs/curama-auth/src"], "@curama-auth/*": ["libs/curama-auth/src/*"], "@vietnam/curama-worker": ["libs/curama-worker/src"], "@vietnam/curama-worker/*": ["libs/curama-worker/src/*"], "@vietnam/curama-opensearch": ["libs/curama-opensearch/src"], "@vietnam/curama-opensearch/*": ["libs/curama-opensearch/src/*"]}}}