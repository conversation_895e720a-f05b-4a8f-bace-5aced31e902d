{"name": "curama-consulting-message", "version": "0.0.1", "description": "curama-consulting-message", "author": "<EMAIL>", "private": true, "license": "MIT", "scripts": {"prebuild": "rimraf dist && npm run scss", "prestart": "npm run scss", "build": "nest build", "debug": "nodemon", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "npm run prebuild && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "scss": "npx sass views:public/css --style=compressed", "scss:dev": "npx sass --watch views:public/css", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest --maxWorkers=1", "test:watch": "jest --watch", "test:cov": "jest --coverage --runInBand", "test:ver": "jest --verbose", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "npm run typeorm migration:generate", "migration:show": "npm run typeorm migration:show", "migration:run": "npm run typeorm migration:run", "migration:revert": "npm run typeorm migration:revert", "migration:create": "typeorm-ts-node-commonjs migration:create", "command": "node dist/src/command", "command:debug": "nest build && node dist/src/command", "lint:staged": "lint-staged", "dev:prepare": "npm install --save-dev @typescript-eslint/eslint-plugin @typescript-eslint/parser eslint eslint-config-prettier eslint-plugin-prettier prettier husky lint-staged && npx husky install"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.369.0", "@aws-sdk/client-s3": "^3.478.0", "@aws-sdk/client-verifiedpermissions": "^3.369.0", "@golevelup/nestjs-rabbitmq": "^3.6.0", "@liaoliaots/nestjs-redis": "^9.0.5", "@nestjs/axios": "^2.0.0", "@nestjs/bullmq": "^10.2.1", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.0", "@nestjs/core": "^9.0.0", "@nestjs/platform-express": "^9.0.0", "@nestjs/schedule": "^3.0.4", "@nestjs/swagger": "^7.1.1", "@nestjs/typeorm": "^9.0.1", "@opensearch-project/opensearch": "^2.6.0", "@vietnam/cnga-frontend": "1.0.12", "@vietnam/cnga-http-request": "0.0.1", "@vietnam/curama-py3-api": "1.1.6", "@vietnam/cnga-middleware": "1.1.0", "aws-sdk": "^2.1416.0", "bullmq": "^5.15.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "connect-flash": "^0.1.1", "connect-redis": "^7.1.0", "cookie-parser": "^1.4.6", "crypto-js": "^4.1.1", "csrf": "^3.1.0", "csv-parse": "^5.5.2", "csv-parser": "^3.0.0", "dayjs": "^1.11.9", "elastic-builder": "^2.30.0", "express-session": "^1.17.3", "file-type": "^19.0.0", "googleapis": "^122.0.0", "jsonwebtoken": "^9.0.2", "nest-commander": "^3.11.0", "nestjs-i18n": "^9.2.2", "nodemon": "^3.0.1", "pg": "^8.11.0", "pg-query-stream": "^4.7.1", "sharp": "^0.32.2", "typeorm": "0.3.17"}, "devDependencies": {"@commitlint/cli": "^17.6.5", "@commitlint/config-conventional": "^17.6.5", "@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/amqplib": "^0.10.1", "@types/cookie-parser": "^1.4.6", "@types/express": "^4.17.13", "@types/express-session": "^1.17.7", "@types/jest": "28.1.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.7", "@types/node": "^16.18.3", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.56.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "jest": "28.1.3", "lint-staged": "^13.3.0", "prettier": "^2.8.8", "ts-jest": "28.0.8", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["!**/infrastructure/**", "!**/mocks/**", "!**/*.module.ts", "!**/index.ts", "!**/*.json"], "coverageDirectory": "coverage", "testEnvironment": "node", "coverageReporters": ["lcov", "text", "text-summary"], "verbose": true, "collectCoverage": true, "coverageThreshold": {"**": {"statements": 75, "branches": 60, "functions": 75}}, "coveragePathIgnorePatterns": ["/dist/", "/public/", "/coverage/", "/src/common", "/src/configs", "/src/database", "/src/exceptions", "/src/providers", "/src/entities", "/src/migrations", "/src/enums", "/dtos/", "/libs", ".eslintrc.js", "worker.ts", "main.ts", "command.ts", "src/app.controller.ts", "src/modules/shared/worker/workers/Worker.ts", "src/modules/shared/worker/workers/WorkerHandler.ts", "src/cron-script.ts", "src/batch-sync-opensearch.ts", "src/modules/shared/search/batch/sync-all-data.ts", "node_modules", "src/modules/shared/post-migration.service.ts"], "moduleNameMapper": {"^@src/(.*)": "<rootDir>/src/$1", "^@configs/(.*)": "<rootDir>/src/configs/$1", "^@modules/(.*)": "<rootDir>/src/modules/$1", "^@providers/(.*)": "<rootDir>/src/providers/$1", "^@exceptions/(.*)": "<rootDir>/src/exceptions/$1", "^@common/(.*)": "<rootDir>/src/common/$1", "^@migrations/(.*)": "<rootDir>/src/migrations/$1", "^@curama-auth/(.*)$": "<rootDir>/libs/curama-auth/src/$1", "^@vietnam/curama-worker/(.*)$": "<rootDir>/libs/curama-worker/src/$1", "^@vietnam/curama-opensearch(.*)$": "<rootDir>/libs/curama-opensearch/src$1"}}, "lint-staged": {"*.ts": ["npm run format", "npm run lint"]}}