import { Modu<PERSON> } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { ConfigProvider } from '@providers/config.provider';
import { LoggingProvider } from '@providers/logging.provider';
import { TypeOrmConfigService } from '@providers/database.provider';
import { WinstonLoggerService } from '@vietnam/cnga-middleware';
import { BatchService } from '@modules/shared/batch/batch.service';
import { BatchModule } from '@modules/shared/batch/batch.module';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    ConfigProvider.register(),
    LoggingProvider.register(),
    TypeOrmModule.forRootAsync({
      useClass: TypeOrmConfigService,
    }),
    BatchModule,
  ],
})
class CronScriptModule {}

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(CronScriptModule);
  app.useLogger(app.get(WinstonLoggerService));
  const batchService = app.get(BatchService);
  await batchService.run();
  return app;
}

bootstrap()
  .then((app) => app.close())
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('CronScriptModule error', error);
    process.exit(-1);
  });
