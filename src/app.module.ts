import { MessageModule } from '@modules/shared/message/message.module';
import { AdminModule } from '@src/modules/shared/admin/admin.module';
import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { RequestContextMiddleware, RequestLoggingMiddleware } from '@vietnam/cnga-middleware';
import { ConfigProvider } from '@providers/config.provider';
import { DatabaseProvider } from '@providers/database.provider';
import { RedisAuthProvider } from '@providers/redis-auth.provider';
import { ViewProvider } from '@providers/view.provider';
import { QueueProvider } from '@providers/queue.provider';
import { LoggingProvider } from '@providers/logging.provider';
import { CuramaPy3ApiModule } from '@vietnam/curama-py3-api';
import { TerryModule } from '@src/modules/terry/terry.module';
import { MessageTemplateModule } from '@modules/shared/message-template/message-template.module';
import { InquiryThreadModule } from '@modules/shared/inquiry-thread/inquiry-thread.module';
import { ShopModule } from '@src/modules/shop/shop.module';
import { TeamModule } from '@modules/shared/team/team.module';
import { I18nProvider } from '@providers/i18n.provider';
import { NgWordModule } from '@modules/shared/ng-word/ng-word.module';
import { TaskModule } from '@modules/shared/task/task.module';
import { DraftModule } from '@modules/shared/draft/draft.module';
import { AttachmentModule } from '@modules/shared/attachment/attachment.module';
import { AppController } from './app.controller';
import { SyncModule } from '@modules/shared/sync/sync.module';
import { MobileCheckMiddleware } from '@common/middlewares/mobile-check.middleware';
import { GENERATE_STORE_ROUTE } from '@common/routes';
import { GoogleChannelService } from '@curama-auth/services/google-channel.service';
import { OpensearchProvider } from '@providers/opensearch.provider';
import { SearchModule } from '@modules/shared/search/search.module';
import { BroadcastMessageModule } from './modules/shared/broadcast-message/broadcast-message.module';
import { NotificationModule } from './modules/shared/notification/notification.module';
import { PostMigrationService } from './modules/shared/post-migration.service';

@Module({
  imports: [
    ConfigProvider.register(),
    DatabaseProvider.register(),
    RedisAuthProvider.register(),
    ViewProvider.register(),
    QueueProvider.register(),
    LoggingProvider.register(),
    OpensearchProvider.register(),
    I18nProvider.register(),
    CuramaPy3ApiModule,
    NgWordModule,
    TerryModule,
    MessageTemplateModule,
    AdminModule,
    TeamModule,
    InquiryThreadModule,
    BroadcastMessageModule,
    ShopModule,
    MessageModule,
    TaskModule,
    DraftModule,
    AttachmentModule,
    SyncModule,
    SearchModule,
    NotificationModule,
  ],
  controllers: [AppController],
  providers: [GoogleChannelService, PostMigrationService],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestContextMiddleware, RequestLoggingMiddleware).forRoutes({
      path: '*',
      method: RequestMethod.ALL,
    });
    consumer.apply(MobileCheckMiddleware).forRoutes(GENERATE_STORE_ROUTE('*'));
  }
}
