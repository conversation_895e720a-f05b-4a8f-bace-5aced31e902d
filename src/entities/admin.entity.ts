import { Column, Entity, JoinTable, ManyToMany, OneToMany } from 'typeorm';
import { AbstractEntity } from '@common/entity/abstract-entity';
import { AdminStatus } from '@src/common/enums/admin-status';
import { AdminsTeamsEntity } from '@src/entities/admins-teams.entity';
import { MessageEntity } from '@src/entities/message.entity';
import { AdminsStoresEntity } from '@src/entities/admins-stores.entity';
import { TeamEntity } from './team.entity';
import { TaskEntity } from './task.entity';
import { DraftEntity } from './draft.entity';
import { BroadcastMessageEntity } from './broadcast-message.entity';

@Entity('Admins')
export class AdminEntity extends AbstractEntity {
  @Column({ name: 'name', nullable: true })
  name: string;

  @Column({ name: 'email', unique: true })
  email: string;

  @Column({ name: 'status', type: 'smallint', default: AdminStatus.Active })
  status: AdminStatus;

  @ManyToMany(() => TeamEntity)
  @JoinTable({
    name: 'Admins_Teams',
    joinColumn: {
      name: 'admin_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'team_id',
      referencedColumnName: 'id',
    },
    synchronize: false,
  })
  teams: TeamEntity[];

  @OneToMany(() => AdminsTeamsEntity, (adminTeam) => adminTeam.admin, {
    createForeignKeyConstraints: false,
  })
  adminsTeams: AdminsTeamsEntity[];

  @OneToMany(() => MessageEntity, (message) => message.admin)
  messages: DraftEntity[];

  @OneToMany(() => DraftEntity, (draft) => draft.admin)
  draft: DraftEntity[];

  @OneToMany(() => TaskEntity, (task) => task.assignmentAdmin)
  tasks: TaskEntity[];

  @OneToMany(() => AdminsStoresEntity, (adminStore) => adminStore.admin)
  adminsStores: AdminsStoresEntity[];

  @OneToMany(() => BroadcastMessageEntity, (message) => message.createdAdmin)
  createdBroadcastMessages: BroadcastMessageEntity[];

  @OneToMany(() => BroadcastMessageEntity, (message) => message.approvedAdmin)
  approvedBroadcastMessages: BroadcastMessageEntity[];

  @OneToMany(() => BroadcastMessageEntity, (message) => message.sendingAdmin)
  sendingBroadcastMessages: BroadcastMessageEntity[];
}
