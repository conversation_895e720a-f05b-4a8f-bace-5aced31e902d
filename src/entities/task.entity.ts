import { AbstractEntity } from '@src/common/entity/abstract-entity';
import { AdminEntity } from '@src/entities/admin.entity';
import { TeamEntity } from '@src/entities/team.entity';
import { Column, Entity, JoinColumn, ManyToOne, VirtualColumn } from 'typeorm';
import { InquiryThreadEntity } from './inquiry-thread.entity';
import { WorkTypeEntity } from './work-type.entity';
import { TaskStatusMapping } from '@src/common/constants';

@Entity('Tasks')
export class TaskEntity extends AbstractEntity {
  @Column({ name: 'thread_id', type: 'uuid' })
  threadId: string;

  @Column({ name: 'work_type_id', type: 'uuid' })
  workTypeId: string;

  @Column({ name: 'memo', length: 1000, nullable: true })
  memo: string;

  @Column({ name: 'assignment_admin_id', nullable: true, type: 'uuid' })
  assignmentAdminId: string;

  @Column({ name: 'assignment_team_id', nullable: true, type: 'uuid' })
  assignmentTeamId: string;

  @Column({ name: 'next_confirmation_date', nullable: true, type: 'date' })
  nextConfirmationDate: string;

  @Column({ name: 'task_no', type: 'smallint' })
  taskNo: number;

  @Column({ name: 'status', type: 'smallint' })
  status: number;

  @Column({ name: 'activity_id', type: 'uuid' })
  activityId: string;

  @VirtualColumn({
    query: (alias) => `${alias}.status`,
    transformer: {
      to: (value) => value,
      from: (value) => TaskStatusMapping[value],
    },
  })
  displayStatus: string;

  @Column({ name: 'created_by', nullable: true })
  createdBy: string;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @ManyToOne(() => AdminEntity, (admin) => admin.tasks)
  @JoinColumn({ name: 'assignment_admin_id', referencedColumnName: 'id' })
  assignmentAdmin: AdminEntity;

  @ManyToOne(() => TeamEntity, (team) => team.tasks)
  @JoinColumn({ name: 'assignment_team_id', referencedColumnName: 'id' })
  assignmentTeam: TeamEntity;

  @ManyToOne(() => WorkTypeEntity, (workType) => workType.tasks)
  @JoinColumn({ name: 'work_type_id', referencedColumnName: 'id' })
  workType: WorkTypeEntity;

  @ManyToOne(() => InquiryThreadEntity, (thread) => thread.tasks)
  @JoinColumn({ name: 'thread_id', referencedColumnName: 'id' })
  thread: InquiryThreadEntity;
}
