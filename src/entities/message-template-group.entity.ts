import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm';
import { AbstractEntity } from '@src/common/entity/abstract-entity';
import { MessageTemplateEntity } from '@src/entities/message-template.entity';

@Entity('MessageTemplateGroups')
export class MessageTemplateGroupEntity extends AbstractEntity {
  @Column({ name: 'name' })
  name: string;

  @Column({ name: 'parent_id', nullable: true })
  parentId: string;

  @Column({ name: 'sort' })
  sort: number;

  @ManyToOne(() => MessageTemplateGroupEntity, (groupParent) => groupParent.messageTemplates)
  @JoinColumn({ name: 'parent_id', referencedColumnName: 'id' })
  groupParent: MessageTemplateGroupEntity;

  @OneToMany(() => MessageTemplateEntity, (messageTemplate) => messageTemplate.groupParent)
  messageTemplates: MessageTemplateEntity[];
}
