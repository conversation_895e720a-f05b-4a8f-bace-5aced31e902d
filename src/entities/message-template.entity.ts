import { ApiProperty } from '@nestjs/swagger';
import { AbstractEntity } from '@src/common/entity/abstract-entity';
import { MessageTemplateGroupEntity } from '@src/entities/message-template-group.entity';
import { Column, Entity, Join<PERSON>olumn, ManyToOne } from 'typeorm';

@Entity('MessageTemplates')
export class MessageTemplateEntity extends AbstractEntity {
  @Column({ name: 'name', unique: true })
  @ApiProperty()
  name: string;

  @Column({ name: 'content' })
  @ApiProperty()
  content: string;

  @Column({ name: 'sort', default: 99 })
  @ApiProperty()
  sort: number;

  @Column({ name: 'group_id' })
  @ApiProperty()
  groupId: string;

  @ManyToOne(() => MessageTemplateGroupEntity, (groupParent) => groupParent.messageTemplates)
  @JoinColumn({ name: 'group_id', referencedColumnName: 'id' })
  groupParent: MessageTemplateGroupEntity;
}
