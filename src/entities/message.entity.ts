import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DeleteDate<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { AbstractEntity } from '@common/entity/abstract-entity';
import { InquiryThreadEntity } from '@src/entities/inquiry-thread.entity';
import { AdminEntity } from '@src/entities/admin.entity';
import { MessageType } from '@src/common/enums/message-type';
import { AttachmentEntity } from './attachment.entity';

@Entity('Messages')
export class MessageEntity extends AbstractEntity {
  @Column({ name: 'thread_id', type: 'uuid' })
  threadId: string;

  @Column({ name: 'content', length: 2000 })
  content: string;

  @Column({ name: 'from_admin_id', type: 'uuid', nullable: true })
  fromAdminId?: string;

  @Column({ name: 'type', type: 'smallint' })
  type: MessageType;

  @Column({ name: 'label', length: 255 })
  label: string;

  @Column({ name: 'deleted_by', nullable: true, type: 'uuid' })
  deletedBy?: string;

  @ManyToOne(() => AdminEntity, (admin) => admin.messages)
  @JoinColumn({ name: 'deleted_by', referencedColumnName: 'id' })
  adminDelete?: AdminEntity;

  @DeleteDateColumn({ name: 'deleted_at', nullable: true })
  deletedAt?: Date;

  @ManyToOne(() => InquiryThreadEntity, (inquiryThread) => inquiryThread.messages)
  @JoinColumn({ name: 'thread_id', referencedColumnName: 'id' })
  inquiryThread?: InquiryThreadEntity;

  @ManyToOne(() => AdminEntity, (admin) => admin.messages)
  @JoinColumn({ name: 'from_admin_id', referencedColumnName: 'id' })
  admin?: AdminEntity;

  @OneToMany(() => AttachmentEntity, (at) => at.message, { cascade: true })
  attachments?: AttachmentEntity[];
}
