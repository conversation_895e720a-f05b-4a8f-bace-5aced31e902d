import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';
import { TeamEntity } from '@src/entities/team.entity';
import { AbstractEntity } from '@src/common/entity/abstract-entity';

@Entity('Teams_Stores')
export class TeamsStoresEntity extends AbstractEntity {
  @Column({ name: 'store_id', type: 'uuid' })
  storeId: string;

  @Column({ name: 'team_id', type: 'uuid' })
  teamId: string;

  @ManyToOne(() => TeamEntity, (team) => team.teamsStores)
  @JoinColumn({ name: 'team_id', referencedColumnName: 'id' })
  team: TeamEntity;
}
