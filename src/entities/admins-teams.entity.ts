import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import { AdminEntity } from '@src/entities/admin.entity';
import { TeamEntity } from '@src/entities/team.entity';
import { AbstractEntity } from '@src/common/entity/abstract-entity';

@Entity('Admins_Teams')
export class AdminsTeamsEntity extends AbstractEntity {
  @PrimaryColumn({ name: 'admin_id', type: 'uuid' })
  adminId: string;

  @PrimaryColumn({ name: 'team_id', type: 'uuid' })
  teamId: string;

  @ManyToOne(() => AdminEntity, (admin) => admin.adminsTeams)
  @JoinColumn({ name: 'admin_id', referencedColumnName: 'id' })
  admin: AdminEntity;

  @ManyToOne(() => TeamEntity, (team) => team.adminsTeams)
  @JoinColumn({ name: 'team_id', referencedColumnName: 'id' })
  team: TeamEntity;
}
