import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON> } from 'typeorm';
import { AbstractEntity } from '@src/common/entity/abstract-entity';
import { AdminEntity } from '@src/entities/admin.entity';

@Entity('Admins_Stores')
export class AdminsStoresEntity extends AbstractEntity {
  @Column({ name: 'store_id', type: 'uuid' })
  storeId: string;

  @Column({ name: 'admin_id', type: 'uuid' })
  adminId: string;

  @ManyToOne(() => AdminEntity, (admin) => admin.adminsStores)
  @JoinColumn({ name: 'admin_id', referencedColumnName: 'id' })
  admin: AdminEntity;
}
