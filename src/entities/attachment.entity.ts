import { AbstractEntity } from '@common/entity/abstract-entity';
import { AttachmentType } from '@src/common/enums/attachment-type';
import { Column, Entity, JoinColumn, ManyToOne, VirtualColumn } from 'typeorm';
import { MessageEntity } from './message.entity';
import { DraftEntity } from './draft.entity';
import { getPrivateFile } from '@src/common/utils/image-handler';
import { BroadcastMessageEntity } from './broadcast-message.entity';

@Entity('Attachments')
export class AttachmentEntity extends AbstractEntity {
  @Column({ name: 'path' })
  path: string;

  @Column({ name: 'attachment_type', type: 'smallint' })
  attachmentType: AttachmentType;

  @Column({ name: 'relation_id', type: 'uuid' })
  relationId: string;

  @ManyToOne(() => DraftEntity, (draft) => draft.attachments, {
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'relation_id', referencedColumnName: 'id' })
  draft: DraftEntity;

  @ManyToOne(() => MessageEntity, (msg) => msg.attachments, {
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'relation_id', referencedColumnName: 'id' })
  message: MessageEntity;

  @ManyToOne(() => BroadcastMessageEntity, (msg) => msg.attachments, {
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'relation_id', referencedColumnName: 'id' })
  broadcastMessage: BroadcastMessageEntity;

  @VirtualColumn({
    query: (alias) => `${alias}.path`,
    transformer: {
      to: (value) => value,
      from: (value) => {
        if (!value) return null;
        return getPrivateFile(value);
      },
    },
  })
  url: string;

  @VirtualColumn({
    query: (alias) => `${alias}.path`,
    transformer: {
      to: (value) => value,
      from: (value) => {
        if (!value) return null;
        return getPrivateFile(value, '430x322');
      },
    },
  })
  urlThumb: string;
}
