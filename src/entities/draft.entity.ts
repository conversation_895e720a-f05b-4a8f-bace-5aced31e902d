import { AbstractEntity } from '@common/entity/abstract-entity';
import { InquiryThreadEntity } from '@src/entities/inquiry-thread.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany, OneToOne, VirtualColumn } from 'typeorm';
import { AdminEntity } from './admin.entity';
import { ThreadStatus } from '@src/common/constants';
import { AttachmentEntity } from './attachment.entity';

@Entity('Drafts')
export class DraftEntity extends AbstractEntity {
  @Column({ name: 'thread_id' })
  threadId: string;

  @Column({ name: 'content', length: 2000, nullable: true })
  content: string;

  @Column({ name: 'from_admin_id', type: 'uuid' })
  fromAdminId: string;

  @Column({ name: 'status', type: 'smallint' })
  status: number;

  @VirtualColumn({
    query: (alias) => `${alias}.status`,
    transformer: {
      to: (value) => value,
      from: (value) => ThreadStatus[value],
    },
  })
  displayStatus: string;

  @VirtualColumn({
    query: (alias) => `${alias}.content`,
    transformer: {
      to: (value) => value,
      from: (value) => value?.escape().urlize(),
    },
  })
  contentEscape: string;

  @OneToOne(() => InquiryThreadEntity, (inquiryThread) => inquiryThread.draft)
  @JoinColumn({ name: 'thread_id', referencedColumnName: 'id' })
  inquiryThread: InquiryThreadEntity;

  @ManyToOne(() => AdminEntity, (admin) => admin.draft)
  @JoinColumn({ name: 'from_admin_id', referencedColumnName: 'id' })
  admin: AdminEntity;

  @OneToMany(() => AttachmentEntity, (at) => at.draft)
  attachments: AttachmentEntity[];
}
