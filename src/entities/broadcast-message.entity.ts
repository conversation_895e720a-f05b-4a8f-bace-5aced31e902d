import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { AbstractEntity } from '@common/entity/abstract-entity';
import { AdminEntity } from '@src/entities/admin.entity';
import { AttachmentEntity } from './attachment.entity';

@Entity('BroadcastMessages')
export class BroadcastMessageEntity extends AbstractEntity {
  @Column({ name: 'title', length: 255 })
  title: string;

  @Column({ name: 'description', length: 2000 })
  description: string;

  @Column({ name: 'content', length: 2000 })
  content: string;

  @Column({ name: 'status', type: 'smallint' })
  status: number;

  @Column({ name: 'scheduled_send_time', type: 'timestamp', nullable: true })
  scheduledSendTime: Date;

  @Column({ name: 'approved_at', type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ name: 'import_file_path', type: 'varchar', nullable: true })
  importFilePath: string;

  @Column({ name: 'csv_file_name', type: 'varchar', nullable: true })
  csvFileName: string;

  @Column({ name: 'can_send_store_count', type: 'int', nullable: true })
  canSendStoreCount: number;

  @Column({ name: 'cannot_send_store_count', type: 'int', nullable: true })
  cannotSendStoreCount: number;

  @Column({ name: 'store_sent_success_count', type: 'int', nullable: true })
  storeSentSuccessCount: number;

  @Column({ name: 'store_sent_fail_count', type: 'int', nullable: true })
  storeSentFailCount: number;

  @Column({ name: 'created_by', type: 'uuid' })
  createdBy: string;

  @Column({ name: 'approved_by', type: 'uuid', nullable: true })
  approvedBy: string;

  @Column({ name: 'sending_by', type: 'uuid', nullable: true })
  sendingBy: string;

  @ManyToOne(() => AdminEntity, (admin) => admin.createdBroadcastMessages)
  @JoinColumn({ name: 'created_by', referencedColumnName: 'id' })
  createdAdmin: AdminEntity;

  @ManyToOne(() => AdminEntity, (admin) => admin.approvedBroadcastMessages)
  @JoinColumn({ name: 'approved_by', referencedColumnName: 'id' })
  approvedAdmin: AdminEntity;

  @ManyToOne(() => AdminEntity, (admin) => admin.sendingBroadcastMessages)
  @JoinColumn({ name: 'sending_by', referencedColumnName: 'id' })
  sendingAdmin: AdminEntity;

  @OneToMany(() => AttachmentEntity, (attachment) => attachment.broadcastMessage, { cascade: true })
  attachments: AttachmentEntity[];
}
