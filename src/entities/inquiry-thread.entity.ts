import { ThreadStatus } from '@common/constants';
import { AbstractEntity } from '@src/common/entity/abstract-entity';
import { MessageEntity } from '@src/entities/message.entity';
import { Column, Entity, OneToMany, OneToOne, VirtualColumn } from 'typeorm';
import { DraftEntity } from './draft.entity';
import { TaskEntity } from './task.entity';
import { formatToDateTimeWithTimezone, JP_YYYYMD_HHMM } from '@src/common/utils/date-util';

@Entity('InquiryThreads')
export class InquiryThreadEntity extends AbstractEntity {
  @Column({ name: 'store_id', type: 'uuid' })
  storeId: string;

  @Column({ name: 'store_name' })
  storeName: string;

  @Column({ name: 'thread_type', type: 'smallint' })
  threadType: number;

  @Column({ name: 'draft_id', type: 'uuid', nullable: true })
  draftId: string;

  @Column({ name: 'lastest_message_id', type: 'uuid', nullable: true })
  lastestMessageId: string;

  @Column({ name: 'status', type: 'smallint', default: 1 }) //unread
  status: number;

  @Column({ name: 'is_read', default: false })
  isRead: boolean;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @OneToOne(() => DraftEntity, (draft) => draft.inquiryThread)
  draft: DraftEntity;

  @OneToMany(() => MessageEntity, (message) => message.inquiryThread)
  messages: MessageEntity[];

  @OneToMany(() => TaskEntity, (task) => task.thread)
  tasks: TaskEntity[];

  @VirtualColumn({
    query: (alias) => `${alias}.status`,
    transformer: {
      to: (value) => value,
      from: (value) => ThreadStatus[value],
    },
  })
  displayStatus: string;

  @VirtualColumn({
    query: (alias) => `${alias}.updated_at`,
    transformer: {
      to: (value) => value,
      from: (value) => {
        return formatToDateTimeWithTimezone(value, JP_YYYYMD_HHMM);
      },
    },
  })
  displayUpdatedAt: string;
}
