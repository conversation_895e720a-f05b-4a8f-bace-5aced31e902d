import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm';
import { AbstractEntity } from '@src/common/entity/abstract-entity';
import { AdminEntity } from '@src/entities/admin.entity';

@Entity('QualityAdmins_Stores')
export class QualityAdminsStoresEntity extends AbstractEntity {
  @Column({ name: 'store_id', type: 'uuid' })
  storeId: string;

  @Column({ name: 'admin_id', type: 'uuid' })
  adminId: string;

  @ManyToOne(() => AdminEntity, (admin) => admin.adminsStores)
  @JoinColumn({ name: 'admin_id', referencedColumnName: 'id' })
  admin: AdminEntity;
}
