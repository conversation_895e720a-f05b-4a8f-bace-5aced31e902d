import { AbstractEntity } from '@src/common/entity/abstract-entity';
import { AdminsTeamsEntity } from '@src/entities/admins-teams.entity';
import { TeamsStoresEntity } from '@src/entities/teams-stores.entity';
import { Column, Entity, JoinTable, ManyToMany, OneToMany } from 'typeorm';
import { TaskEntity } from './task.entity';
import { AdminEntity } from './admin.entity';

@Entity('Teams')
export class TeamEntity extends AbstractEntity {
  @Column({ name: 'name', unique: true })
  name: string;

  @Column({ name: 'orgunit_path', nullable: true })
  orgUnitPath: string;

  @Column({ name: 'department_id', type: 'uuid', nullable: true })
  departmentId: string;

  @Column({ name: 'is_last', type: 'boolean', default: true })
  isLast: boolean;

  @OneToMany(() => AdminsTeamsEntity, (adminTeam) => adminTeam.team)
  adminsTeams: AdminsTeamsEntity[];

  @OneToMany(() => TeamsStoresEntity, (teamStore) => teamStore.team)
  teamsStores: TeamsStoresEntity[];

  @OneToMany(() => TaskEntity, (task) => task.assignmentTeam)
  tasks: TaskEntity[];

  @ManyToMany(() => AdminEntity)
  @JoinTable({
    name: 'Admins_Teams',
    joinColumn: {
      name: 'team_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'admin_id',
      referencedColumnName: 'id',
    },
    synchronize: false,
  })
  admins: AdminEntity[];
}
