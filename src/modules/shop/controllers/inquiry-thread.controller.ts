import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Lo<PERSON>,
  Param,
  ParseEnumPipe,
  Post,
  Render,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { RequireStoreInterceptor, PageSetup } from '@vietnam/curama-py3-api';
import { ShopRequest } from '@src/modules/shop/interfaces/shop-request.interface';
import { InquiryThreadService } from '@modules/shared/inquiry-thread/inquiry-thread.service';
import { MessageService } from '@src/modules/shared/message/message.service';
import { MessageDto } from '@src/modules/shop/dtos/message.dto';
import { HttpResponseFormatter } from '@common/response/response';
import { ThreadType, ThreadTypeKey } from '@src/common/enums/thread-type';
import { ACCOUNT_TYPES, AccountTypeAuthorization, PreventSuperLoginAccess } from '@vietnam/cnga-middleware';
import { routes } from '@src/common/routes';
import { getThreadTypeId<PERSON>y<PERSON><PERSON> } from '@src/common/utils/helper';
import { ReadMessageInterceptor } from '@src/common/interceptors/read-message.interceptor';
import { ApiFiles } from '@src/common/decorators/api-files.decorator';
import { SHOP_INBOX_TAB } from '@src/common/constants';

@Controller('')
@UseInterceptors(RequireStoreInterceptor)
@AccountTypeAuthorization(ACCOUNT_TYPES.StoreUser)
export class InquiryThreadController {
  private readonly logger = new Logger(InquiryThreadController.name);
  constructor(
    private readonly inquiryThreadService: InquiryThreadService,
    private readonly messageService: MessageService,
  ) {}

  @Get(routes.store.message.webDetail)
  @Render('shop/inquiry-thread/web/detail')
  @UseInterceptors(ReadMessageInterceptor)
  @PageSetup({ activeTab: SHOP_INBOX_TAB })
  async detailWeb(
    @Param('type', new ParseEnumPipe(ThreadTypeKey, { errorHttpStatusCode: HttpStatus.NOT_FOUND }))
    threadTypeSlug: ThreadTypeKey,
    @Req() request: ShopRequest,
  ) {
    const threadTypeId = getThreadTypeIdByKey(threadTypeSlug);
    const storeId = request.storeInfo.store.id;
    const thread = await this.inquiryThreadService.getThreadByType(storeId, threadTypeId);
    let messages = {
      pagination: {},
      list: [],
    };
    if (thread) {
      messages = await this.messageService.getByInquiryThread(thread.id);
    }

    const isShowHelpFeelModal = this.inquiryThreadService.isShowHelpFeelModal(threadTypeId, messages.list);
    let isShowMessageInput = true;
    if (threadTypeId === ThreadType.QualityThread && messages.list.length === 0) {
      isShowMessageInput = false;
    }

    const threadType = this.inquiryThreadService.getThreadTypeObject(threadTypeId);

    const isAdmin = request.user?.accountType === ACCOUNT_TYPES.Admin;

    return {
      threadType,
      threadTypeSlug,
      thread,
      messages,
      isShowHelpFeelModal,
      isShowMessageInput,
      isAdmin,
    };
  }

  @Get(routes.store.message.mobileDetail)
  @Render('shop/inquiry-thread/mobile/detail')
  @UseInterceptors(ReadMessageInterceptor)
  @PageSetup({ activeTab: SHOP_INBOX_TAB })
  async detailMobile(
    @Param('type', new ParseEnumPipe(ThreadTypeKey, { errorHttpStatusCode: HttpStatus.NOT_FOUND }))
    threadTypeSlug: ThreadTypeKey,
    @Req() request: ShopRequest,
  ) {
    const threadTypeId = getThreadTypeIdByKey(threadTypeSlug);
    const storeId = request.storeInfo.store.id;
    const thread = await this.inquiryThreadService.getThreadByType(storeId, threadTypeId);
    let messages = {
      pagination: {},
      list: [],
    };
    if (thread) {
      messages = await this.messageService.getByInquiryThread(thread.id);
    }

    const isShowHelpFeelModal = this.inquiryThreadService.isShowHelpFeelModal(threadTypeId, messages.list);
    let isShowMessageInput = true;
    if (threadTypeId === ThreadType.QualityThread && messages.list.length === 0) {
      isShowMessageInput = false;
    }

    const threadType = this.inquiryThreadService.getThreadTypeObject(threadTypeId);

    return {
      threadType,
      threadTypeSlug,
      thread,
      messages,
      isShowHelpFeelModal,
      isShowMessageInput,
    };
  }

  @Post([routes.store.message.webSave, routes.store.message.mobileSave])
  @UseGuards(PreventSuperLoginAccess)
  @ApiFiles('attachments', 5)
  async createMessageByStore(
    @Param('type', new ParseEnumPipe(ThreadTypeKey, { errorHttpStatusCode: HttpStatus.NOT_FOUND }))
    threadTypeSlug: ThreadTypeKey,
    @Body() messageRequest: MessageDto,
    @UploadedFiles() attachments: Express.Multer.File[],
    @Req() request: ShopRequest,
  ) {
    const store = request.storeInfo.store;
    const threadTypeId = getThreadTypeIdByKey(threadTypeSlug);
    const thread = await this.inquiryThreadService.getThreadByType(store.id, threadTypeId);
    // Store cannot create quantity thread
    const shouldBeDeny = threadTypeId === ThreadType.QualityThread && !thread;
    if (shouldBeDeny) {
      return HttpResponseFormatter.responseForbidden({
        message: 'You cannot create quality thread',
      });
    }
    return this.inquiryThreadService.createMessageByStore(messageRequest, store, attachments, thread);
  }
}
