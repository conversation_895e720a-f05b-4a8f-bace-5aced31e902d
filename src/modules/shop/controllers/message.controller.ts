import { RequireStoreInterceptor } from '@vietnam/curama-py3-api';
import { MessageService } from '@src/modules/shared/message/message.service';
import { Controller, Get, HttpStatus, Param, ParseEnumPipe, Req, UseInterceptors } from '@nestjs/common';
import { HttpResponseFormatter } from '@common/response/response';
import { AccountTypeAuthorization, ACCOUNT_TYPES } from '@vietnam/cnga-middleware';
import { routes } from '@src/common/routes';
import { ThreadTypeKey } from '@src/common/enums/thread-type';
import { getThreadTypeIdByKey } from '@src/common/utils/helper';
import { ShopRequest } from '../interfaces/shop-request.interface';
import { InquiryThreadService } from '@src/modules/shared/inquiry-thread/inquiry-thread.service';

@Controller('')
@UseInterceptors(RequireStoreInterceptor)
@AccountTypeAuthorization(ACCOUNT_TYPES.StoreUser)
export class MessageController {
  constructor(
    private readonly inquiryThreadService: InquiryThreadService,
    private readonly messageService: MessageService,
  ) {}

  @Get([routes.store.message.webGetList, routes.store.message.mobileGetList])
  async getMessages(
    @Param('type', new ParseEnumPipe(ThreadTypeKey, { errorHttpStatusCode: HttpStatus.NOT_FOUND }))
    threadTypeSlug: ThreadTypeKey,
    @Req() request: ShopRequest,
  ) {
    const threadTypeId = getThreadTypeIdByKey(threadTypeSlug);
    const store = request.storeInfo.store;
    const thread = await this.inquiryThreadService.getThreadByType(store.id, threadTypeId);

    if (!thread) {
      return HttpResponseFormatter.formatSuccessResponse({
        data: [],
        meta: {
          pageNumber: 0,
          total: 0,
          perPage: 0,
        },
      });
    }

    const perPage = request.query?.perPage ? +request.query.perPage : null;
    const page = request.query?.page ? +request.query.page : null;
    const messagePagination = await this.messageService.getByInquiryThread(thread.id, perPage, page);
    return HttpResponseFormatter.formatSuccessResponse({
      data: messagePagination.list,
      meta: messagePagination.pagination,
    });
  }
}
