import { <PERSON>, Get, Param, ParseUUIDPipe } from '@nestjs/common';
import { InquiryThreadService } from '@modules/shared/inquiry-thread/inquiry-thread.service';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { CanaryReleaseStoreService } from '@modules/shared/canary-release-store/canary-release-store.service';
import { LatestMessageResponse } from '@modules/shop/dtos/latest-message.dto';
import { routes } from '@src/common/routes';
import { CanGetStoreData } from '@vietnam/cnga-middleware';
import { ThreadType } from '@common/enums/thread-type';
import { debug } from '@src/common/utils/debugger';

@Controller('')
@ApiTags('Internal API')
export class InternalApiController {
  constructor(
    private readonly inquiryThreadService: InquiryThreadService,
    private readonly canaryReleaseStoreService: CanaryReleaseStoreService,
  ) {}

  /**
   * 最終メッセージ、未読・既読状態を返す
   * @param storeId
   */
  @Get(routes.internalApi.latestMessage)
  @ApiParam({ name: 'storeId', type: String })
  @CanGetStoreData('storeId')
  async getLatestMessages(@Param('storeId', new ParseUUIDPipe({ version: '4' })) storeId: string) {
    const canAccess = await this.canaryReleaseStoreService.checkRelease(storeId);

    debug(`[getLatestMessages] Endpoint: ${routes.internalApi.latestMessage}`, { storeId });
    debug(`[getLatestMessages] checkCanaryRelease`, { canAccess });

    if (!canAccess) {
      return { threads: [] };
    }

    const latestMessages = await this.inquiryThreadService.getLatestMessageForEachType(storeId);
    const defaultConsultantMessage = {
      thread_type: ThreadType.CuramaThread,
      latest_message: null,
      latest_time: null,
      is_read: false,
    };

    const isHasConsultantLatestMessage =
      latestMessages.findIndex((message) => message.thread_type === ThreadType.CuramaThread) !== -1;

    if (!isHasConsultantLatestMessage) {
      latestMessages.unshift(defaultConsultantMessage);
    }

    const data = latestMessages.map((message, index) => new LatestMessageResponse(message, index));

    debug(`[getLatestMessages] Response`, { data });

    return {
      threads: data,
    };
  }
}
