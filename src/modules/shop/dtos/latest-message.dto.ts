import { ThreadTypeName } from '@src/common/enums/thread-type';
import { API_DATE_FORMAT_WITH_TZ, TZ_JP, formatToDateTimeWithTimezone } from '@src/common/utils/date-util';

export class LatestMessage {
  thread_type: number;
  latest_message: string;
  latest_time: string;
  is_read: boolean;
}

export class LatestMessageResponse {
  name: string;
  latest_message: string;
  latest_message_sent_at: string;
  is_read: boolean;
  sort: number;

  public constructor(item: LatestMessage, index: number) {
    this.name = ThreadTypeName[item.thread_type];
    this.latest_message = item.latest_message;
    this.latest_message_sent_at = item.latest_time
      ? formatToDateTimeWithTimezone(item.latest_time, API_DATE_FORMAT_WITH_TZ, TZ_JP)
      : null;
    this.is_read = item.is_read;
    this.sort = index + 1;
  }
}
