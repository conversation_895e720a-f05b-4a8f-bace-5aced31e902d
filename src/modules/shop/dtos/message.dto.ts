import { i18nValidationMessage } from 'nestjs-i18n';
import { IsIn, IsInt, IsNotEmpty, IsOptional, IsString, MaxLength, Validate, ValidateIf } from 'class-validator';
import { Type } from 'class-transformer';
import { MinLengthValidator } from '@src/common/validators/min-length.validator';

export class MessageDto {
  @Type(() => Number)
  @IsInt()
  @IsIn([1, 2])
  @IsNotEmpty({ message: i18nValidationMessage('validation.isNotEmpty') })
  threadTypeId: number;

  @IsString()
  @ValidateIf((object, value) => !!value)
  @IsNotEmpty({ message: i18nValidationMessage('validation.isNotEmpty') })
  @MaxLength(2000, { message: i18nValidationMessage('validation.maxLengthMessage'), always: false })
  @Validate(MinLengthValidator, [10])
  message: string;

  @IsOptional()
  attachments: string[];
}
