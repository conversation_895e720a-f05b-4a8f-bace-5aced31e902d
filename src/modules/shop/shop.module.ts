import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { CuramaPy3ApiModule } from '@vietnam/curama-py3-api';
import { InquiryThreadModule } from '@modules/shared/inquiry-thread/inquiry-thread.module';
import { InquiryThreadController } from '@src/modules/shop/controllers/inquiry-thread.controller';
import { MessageController } from '@src/modules/shop/controllers/message.controller';
import { AuthModule, CanAccessInternalApiMiddleware, SessionManagerMiddleware } from '@vietnam/cnga-middleware';
import { PassportModule } from '@nestjs/passport';
import { MessageModule } from '@src/modules/shared/message/message.module';
import { CanaryReleaseStoreModule } from '@modules/shared/canary-release-store/canary-release-store.module';
import { AttachmentModule } from '../shared/attachment/attachment.module';
import { InternalApiController } from './controllers/internal-api.controller';
import { ApiClientModule } from '@vietnam/cnga-http-request';
import { ConfigService } from '@nestjs/config';

@Module({
  controllers: [InquiryThreadController, MessageController, InternalApiController],
  exports: [],
  providers: [],
  imports: [
    ApiClientModule.forRootAsync({
      useFactory: (configService: ConfigService) => {
        const httpConfig = configService.get('http');
        return {
          retryEndPoints: httpConfig.retryEndPoints,
          httpMaxRetry: httpConfig.httpMaxRetry,
          timeout: httpConfig.httpTimeout,
          statusRetry: httpConfig.statusRetry,
        };
      },
      inject: [ConfigService],
    }),
    CuramaPy3ApiModule,
    InquiryThreadModule,
    MessageModule,
    CanaryReleaseStoreModule,
    AttachmentModule,
    PassportModule,
    AuthModule,
  ],
})
export class ShopModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(SessionManagerMiddleware).forRoutes({
      path: '*',
      method: RequestMethod.ALL,
    });

    consumer.apply(CanAccessInternalApiMiddleware).forRoutes(InternalApiController);
  }
}
