import { StoreAssignmentDto } from '@modules/terry/dtos/store-assignment.dto';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ActivityType,
  QUALITY_TEAM_NAME,
  StoppedStoreStatuses,
  TaskStatusMapping,
  ThreadStatus,
  SYNC_OPENSEARCH_MESSAGE_MQ_TASK,
} from '@src/common/constants';
import { ThreadType } from '@src/common/enums/thread-type';
import { HttpResponseFormatter } from '@src/common/response/response';
import {
  AdminsStoresEntity,
  AttachmentEntity,
  InquiryThreadEntity,
  MessageEntity,
  TaskEntity,
  TeamsStoresEntity,
  WorkTypeEntity,
} from '@src/entities';
import { QualityAdminsStoresEntity } from '@src/entities/quality-admins-stores.entity';
import { InquiryThreadQueryRequest } from '@src/modules/terry/dtos/inquiry-thread-query-request.dto';
import { Request } from 'express';
import { DataSource, EntityManager, Not, Repository } from 'typeorm';
import { InquiryThreadService } from '../shared/inquiry-thread/inquiry-thread.service';
import { MessageTemplateService } from '../shared/message-template/message-template.service';
import { MessageService } from '../shared/message/message.service';
import { TaskService } from '../shared/task/task.service';
import { TeamService } from '../shared/team/team.service';
import { TaskStatus as TaskStatusEnum } from '@src/common/enums/task-status';
import { SelectMessageTemplateDto } from './dtos/select-message-template.dto';
import { replaceParams } from '../../common/utils/helper';
import { DraftEntity } from '../../entities/draft.entity';
import { InquiryThreadStatus } from '../../common/enums/inquiry-thread-status';
import { MessageType } from '../../common/enums/message-type';
import { CanaryReleaseStoreService } from '../shared/canary-release-store/canary-release-store.service';
import { StoreDetailDto } from '@vietnam/curama-py3-api';
import { PaginationInquiryThreads } from '../shared/inquiry-thread/dtos/pagination-inquiry-threads.dto';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';

@Injectable()
export class TerryService {
  private readonly logger = new Logger(TerryService.name);
  constructor(
    @InjectRepository(QualityAdminsStoresEntity)
    private readonly qualityAdminsStoresRepo: Repository<QualityAdminsStoresEntity>,
    @InjectRepository(AdminsStoresEntity)
    private readonly adminsStoresRepo: Repository<AdminsStoresEntity>,
    @InjectRepository(InquiryThreadEntity)
    private readonly inquiryThreadRepo: Repository<InquiryThreadEntity>,
    private readonly messageTemplateService: MessageTemplateService,
    private readonly inquiryThreadService: InquiryThreadService,
    private readonly messageService: MessageService,
    private dataSource: DataSource,
    private readonly taskService: TaskService,
    private readonly canaryReleaseStoreService: CanaryReleaseStoreService,
    private readonly teamService: TeamService,
    private readonly amqpConnection: AmqpConnection,
  ) {}

  async getStoreThreadDetails(store: StoreDetailDto, threadType: number, hasDeletePermission: boolean) {
    const storeId = store.id;
    const canRelease = await this.canaryReleaseStoreService.checkRelease(storeId);
    const canSendMsg = !StoppedStoreStatuses.includes(store.status_id);

    let threadDetails = {
      taskQuantity: 0,
      canRelease,
      canSendMsg,
      teamName: '',
      threadId: '',
      totalMessage: 0,
      totalTask: 0,
      threadType,
      threadStatus: '',
      status: '',
      largeCategories: [],
      listStatus: [],
      hasDeletePermission,
      threadDraft: {},
      threadTasks: [],
      threadMessages: [],
      assignmentAdmin: {},
    };
    if (!canRelease) return threadDetails;

    const thread = await this.inquiryThreadService.getByCondition({ storeId, threadType }, [
      'draft',
      'draft.admin',
      'draft.attachments',
    ]);
    const teamName = threadType !== ThreadType.QualityThread ? 'くらしのマーケット' : `${QUALITY_TEAM_NAME}チーム`;
    const listStatus = Object.entries(ThreadStatus).map((e) => ({ id: e[0], name: e[1] }));

    const [largeCategories, assignmentAdmin, [threadMessages, totalMessage], [threadTasks, totalTask], taskQuantity] =
      await Promise.all([
        this.messageTemplateService.getLargeCategories(),
        threadType === ThreadType.QualityThread
          ? this.qualityAdminsStoresRepo.findOne({ where: { storeId }, relations: ['admin'] })
          : this.adminsStoresRepo.findOne({ where: { storeId }, relations: ['admin'] }),
        thread
          ? this.messageService.findAndCount({
              where: {
                threadId: thread.id,
              },
              order: {
                createdAt: 'DESC',
              },
              take: 10,
              withDeleted: true,
              relations: ['admin', 'attachments', 'adminDelete'],
            })
          : [[], 0],
        thread
          ? this.taskService.findAndCount({
              where: {
                threadId: thread.id,
                status: Not(TaskStatusEnum.Finish),
              },
              order: {
                updatedAt: 'DESC',
              },
              take: 10,
              relations: ['workType', 'assignmentTeam', 'assignmentAdmin'],
            })
          : [[], 0],
        thread ? this.taskService.countTotal(thread.id) : 0,
      ]);

    threadDetails = {
      ...threadDetails,
      taskQuantity,
      teamName,
      totalMessage,
      totalTask,
      largeCategories,
      listStatus,
      threadTasks,
      threadMessages,
      assignmentAdmin: assignmentAdmin?.admin ?? {},
    };
    if (!thread) return threadDetails;

    return {
      ...threadDetails,
      threadId: thread.id,
      threadType: thread.threadType,
      threadStatus: thread.status || '',
      status: ThreadStatus[thread.status] || '',
      threadDraft: {
        ...thread.draft,
      },
      threadTasks: threadTasks.map((e) => {
        e.memo = e.memo?.escape().urlize();
        return e;
      }),
      threadMessages: threadMessages.map((e) => {
        if (e.deletedAt) {
          e.content = '（このメッセージは削除されました）';
          e.attachments = [];
        }
        e.content = e.content.escape().urlize();
        return e;
      }),
    };
  }

  async getInquiryThreads(
    inquiryThreadQueryRequest: InquiryThreadQueryRequest,
    request: Request,
  ): Promise<PaginationInquiryThreads> {
    try {
      const inquiryThreads = await this.inquiryThreadService.getAll(inquiryThreadQueryRequest);
      inquiryThreads.pagination = {
        ...inquiryThreads.pagination,
        request: { query: request.query, path: request.path },
      };

      return new PaginationInquiryThreads(inquiryThreads, request);
    } catch (err) {
      this.logger.error({ err: err.message });
      return new PaginationInquiryThreads({ threads: [], request });
    }
  }

  async storeAssignment(storeId: string, storeAssignmentDto: StoreAssignmentDto) {
    const result = await this.dataSource.transaction(async (transactionalEntityManager) => {
      await Promise.all([
        transactionalEntityManager.delete(AdminsStoresEntity, { storeId: storeId }),
        transactionalEntityManager.delete(TeamsStoresEntity, { storeId: storeId }),
        transactionalEntityManager.delete(QualityAdminsStoresEntity, { storeId: storeId }),
      ]);

      if (storeAssignmentDto.adminId) {
        const adminStore = new AdminsStoresEntity();
        adminStore.storeId = storeId;
        adminStore.adminId = storeAssignmentDto.adminId;
        await transactionalEntityManager.save(adminStore);
      }

      if (storeAssignmentDto.teamId) {
        const teamStore = new TeamsStoresEntity();
        teamStore.storeId = storeId;
        teamStore.teamId = storeAssignmentDto.teamId;
        await transactionalEntityManager.save(teamStore);
      }

      if (storeAssignmentDto.qualityAdminId) {
        const qualityAdminStore = new QualityAdminsStoresEntity();
        qualityAdminStore.storeId = storeId;
        qualityAdminStore.adminId = storeAssignmentDto.qualityAdminId;
        await transactionalEntityManager.save(qualityAdminStore);
      }
    });

    await this.amqpConnection.publish('taskManager', SYNC_OPENSEARCH_MESSAGE_MQ_TASK, {
      event: RabbitMQEventType.UpdateAdminTeamStore,
      content: { store_id: storeId },
    });
    return result;
  }

  async selectPatrolMessageTemplate(selectMessageTemplateRequest: SelectMessageTemplateDto) {
    const { adminId, storeId, storeName, templateId, customerName, reservationCode, serviceName, activityId } =
      selectMessageTemplateRequest;
    const messageTemplate = await this.messageTemplateService.getById(templateId);
    if (!messageTemplate) {
      return HttpResponseFormatter.responseBadRequest({ errors: 'Message template not found' });
    }

    try {
      return await this.dataSource.transaction(async (entityManager: EntityManager) => {
        let thread = await this.inquiryThreadRepo.findOne({
          where: { storeId, threadType: ThreadType.QualityThread },
        });
        if (!thread) {
          thread = await entityManager.save(InquiryThreadEntity, {
            storeId,
            threadType: ThreadType.QualityThread,
            storeName,
          });
        }

        if (thread.draftId) {
          await Promise.all([
            entityManager.delete(AttachmentEntity, { relationId: thread.draftId }),
            entityManager.update(
              InquiryThreadEntity,
              { draftId: thread.draftId },
              { draftId: null, updatedAt: thread.updatedAt },
            ),
            entityManager.delete(DraftEntity, { id: thread.draftId }),
          ]);
        }

        const draftContent = replaceParams(messageTemplate.content || '', {
          customerName,
          reservationCode: reservationCode || '',
          serviceName,
        });
        const AIWorkType = await entityManager.findOneBy(WorkTypeEntity, { name: ActivityType.Patrol });
        const taskNo = await this.taskService.generateTaskNo(thread.id);
        let messageContent = `タスク${taskNo}を作成しました。\n業務：${AIWorkType.name} \nステータス：${
          TaskStatusMapping[TaskStatusEnum.WaitingForResponse]
        }\nお客様名: ${customerName}\nサービス名: ${serviceName}`;
        const taskMemo = `お客様名: ${customerName}`;
        if (reservationCode) {
          messageContent += `\n予約詳細: ${reservationCode}`;
        }

        const newDraft = await entityManager.save(DraftEntity, {
          threadId: thread.id,
          content: draftContent,
          status: InquiryThreadStatus.UnderConfirmation,
          fromAdminId: adminId,
        });
        const qualityTeam = await this.teamService.findOne({ where: { name: QUALITY_TEAM_NAME } });

        await Promise.all([
          entityManager.update(InquiryThreadEntity, { id: thread.id }, { draftId: newDraft.id }),
          entityManager.save(TaskEntity, {
            threadId: thread.id,
            workTypeId: AIWorkType.id,
            memo: taskMemo,
            status: TaskStatusEnum.WaitingForResponse,
            assignmentTeamId: qualityTeam?.id,
            taskNo: taskNo,
            activityId: activityId,
            createdBy: adminId,
            updatedBy: adminId,
          }),
          entityManager.save(MessageEntity, {
            threadId: thread.id,
            content: messageContent,
            type: MessageType.Memo,
            fromAdminId: adminId,
          }),
        ]);
        return HttpResponseFormatter.responseOK({ data: {} });
      });
    } catch (err) {
      this.logger.error({ err: err.message });
      return HttpResponseFormatter.responseInternalError({ errors: err.message });
    }
  }
}
