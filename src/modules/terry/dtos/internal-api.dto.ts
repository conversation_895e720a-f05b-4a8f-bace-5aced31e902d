import { ApiProperty } from '@nestjs/swagger';
import { HttpResponseDto } from '@src/common/response/response.dto';
import { MessageTemplateEntity } from '@src/entities';

export class PatrolTemplateResponseDto extends HttpResponseDto {
  @ApiProperty({ type: [MessageTemplateEntity] })
  data: Array<MessageTemplateEntity>;
}

export class SelectPatrolTemplateResponseDto extends HttpResponseDto {
  @ApiProperty()
  data: Record<string, any>;
}
