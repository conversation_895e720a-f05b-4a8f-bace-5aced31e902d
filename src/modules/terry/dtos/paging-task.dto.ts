import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';

export class PagingTaskQueryDto {
  @ApiProperty()
  @IsUUID('4', { message: i18nValidationMessage('validation.invalidUUID') })
  threadId: string;
  @ApiProperty({ default: 'true' })
  withCompleted: string;
  @ApiProperty()
  limit: number;
  @ApiProperty()
  offset: number;
}
