import { Transform, Type } from 'class-transformer';
import { IsIn, IsInt, IsOptional, IsString, IsUUID } from 'class-validator';
import { InquiryThreadStatus } from '@src/common/enums/inquiry-thread-status';
import { PAGINATOR } from '@src/common/constants/paginator';
import { i18nValidationMessage } from 'nestjs-i18n';

export class InquiryThreadQueryRequest {
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  page: number;

  @IsOptional()
  @IsString()
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.invalidUUID') })
  storeId: string;

  @IsOptional()
  @IsString()
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.invalidUUID') })
  assignmentTeamId: string;

  @IsOptional()
  @IsString()
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.invalidUUID') })
  assignmentAdminId: string;

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Transform((data) => (!data.value ? undefined : parseInt(data.value)))
  @IsIn(Object.values(InquiryThreadStatus), { message: i18nValidationMessage('validation.isIn') })
  status: number;

  @IsOptional()
  @IsString()
  updatedAt: string;

  @IsOptional()
  @IsString()
  search: string;

  get offset(): number {
    return this.page > 0 ? (this.page - 1) * PAGINATOR.TERRY : 0;
  }

  get pageNumber(): number {
    return parseInt(String(this.page ? this.page : 1));
  }

  get perPage(): number {
    return PAGINATOR.TERRY;
  }
}
