import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';

export class SelectMessageTemplateDto {
  @ApiProperty()
  @IsString()
  @IsUUID('4', { message: i18nValidationMessage('validation.formatUUID') })
  adminId: string;

  @ApiProperty()
  @IsString()
  @IsUUID('4', { message: i18nValidationMessage('validation.formatUUID') })
  storeId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  storeName: string;

  @ApiProperty()
  @IsString()
  @IsUUID('4', { message: i18nValidationMessage('validation.formatUUID') })
  templateId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  customerName: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  reservationCode: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  serviceName: string;

  @ApiProperty()
  @IsString()
  activityId: string;
}
