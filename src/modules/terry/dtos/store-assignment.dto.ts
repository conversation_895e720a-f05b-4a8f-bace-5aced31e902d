import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';
import { Transform } from 'class-transformer';
import { i18nValidationMessage } from 'nestjs-i18n';

export class StoreAssignmentDto {
  @ApiProperty({
    required: false,
    description: '担当者ID',
    example: 'a88f9f9e-3b92-4105-9ae9-56b3886c7f9b',
  })
  @IsOptional()
  @IsString({ message: i18nValidationMessage('validation.isString') })
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.formatUUID') })
  adminId: string;

  @ApiProperty({
    required: false,
    description: '担当チームID',
    example: 'a88f9f9e-3b92-4105-9ae9-56b3886c7f9b',
  })
  @IsOptional()
  @IsString({ message: i18nValidationMessage('validation.isString') })
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.formatUUID') })
  teamId: string;

  @ApiProperty({
    required: false,
    description: '品質管理担当者ID',
    example: 'a88f9f9e-3b92-4105-9ae9-56b3886c7f9b',
  })
  @IsOptional()
  @IsString({ message: i18nValidationMessage('validation.isString') })
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.formatUUID') })
  qualityAdminId: string;
}
