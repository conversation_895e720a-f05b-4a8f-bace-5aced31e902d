import { Type } from 'class-transformer';
import { IsInt, IsOptional, IsString } from 'class-validator';
import { PAGINATOR } from '@src/common/constants/paginator';

export class BroadcastMessageQueryRequest {
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  page: number;

  @IsOptional()
  @IsString()
  search: string;

  get offset(): number {
    return this.page > 0 ? (this.page - 1) * PAGINATOR.TERRY : 0;
  }

  get pageNumber(): number {
    return parseInt(String(this.page ? this.page : 1));
  }

  get perPage(): number {
    return PAGINATOR.TERRY;
  }
}
