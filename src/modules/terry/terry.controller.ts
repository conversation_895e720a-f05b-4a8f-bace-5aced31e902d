import { CuramaActions, CuramaResources } from '@curama-auth/authorization/aws/schema';
import { CURAMA_AUTH_SERVICE } from '@curama-auth/constants';
import { SessionAdminInfo } from '@curama-auth/decorators/session-admin-info.decorator';
import { AdminAuthenticatedGuard } from '@curama-auth/guards/admin-authenticated.guard';
import { IdentityServiceProvider } from '@curama-auth/identities/identity.provider';
import { SessionAdmin } from '@curama-auth/sessions/session-admin';
import { InquiryThreadQueryRequest } from '@src/modules/terry/dtos/inquiry-thread-query-request.dto';
import {
  Controller,
  Get,
  HttpStatus,
  Inject,
  NotFoundException,
  Param,
  ParseEnumPipe,
  ParseUUIDPipe,
  Redirect,
  Render,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { QUALITY_TEAM_NAME, ThreadStatus } from '@src/common/constants';
import { StoreDetailDto, TerryStoreDetailInterceptor, PageAdminSetup } from '@vietnam/curama-py3-api';
import { ThreadTypeKey } from '@src/common/enums/thread-type';
import { routes } from '@src/common/routes';
import { TerryService } from './terry.service';
import {
  getParentTeamNameByPath,
  getThreadTypeIdByKey,
  validationExceptionArrayMessages,
} from '@src/common/utils/helper';
import { ConfigService } from '@nestjs/config';
import { plainToClass } from 'class-transformer';
import { I18n, I18nContext } from 'nestjs-i18n';
import { Request } from 'express';
import { TeamService } from '../shared/team/team.service';
import { AdminService } from '../shared/admin/admin.service';
import { PaginationInquiryThreads } from '../shared/inquiry-thread/dtos/pagination-inquiry-threads.dto';

@Controller('')
@ApiTags('Terry Views')
@UseGuards(AdminAuthenticatedGuard)
export class TerryController {
  constructor(
    private readonly configService: ConfigService,
    private readonly terryService: TerryService,
    private readonly teamService: TeamService,
    private readonly adminService: AdminService,
    @Inject(CURAMA_AUTH_SERVICE) private readonly authService: IdentityServiceProvider,
  ) {}

  @Get(routes.terry.thread.viewList)
  @Render('admin/pages/thread/list')
  @PageAdminSetup({ activeAdminPage: 'messageManagement' })
  async getThreads(@Req() request: Request, @I18n() i18n: I18nContext) {
    const searchQuery = plainToClass(InquiryThreadQueryRequest, request.query);
    const validation = await i18n.validate(searchQuery);
    let threads: PaginationInquiryThreads;
    let errors = [];
    if (validation.length > 0) {
      errors = validationExceptionArrayMessages(validation);
      threads = new PaginationInquiryThreads({ threads: [], request });
    } else {
      threads = await this.terryService.getInquiryThreads(searchQuery, request);
    }

    const [teams, admins] = await Promise.all([
      this.teamService.find({ where: { isLast: true }, select: ['id', 'name'] }),
      this.adminService.find({ select: ['id', 'name'] }),
    ]);

    const statuses = Object.entries(ThreadStatus).map((e) => ({ id: e[0], name: e[1] }));

    return { data: { teams, admins, statuses, ...threads, query: request.query }, errors };
  }

  @Get(routes.terry.thread.viewByStore)
  @ApiParam({ name: 'storeId', type: String })
  @Redirect()
  async getStoreThreadMessageByStoreId(
    @Param('storeId', new ParseUUIDPipe({ version: '4' })) storeId: string,
    @SessionAdminInfo() user: SessionAdmin,
  ) {
    const type = user.teams?.some(
      (e) =>
        e.name.startsWith(QUALITY_TEAM_NAME) || getParentTeamNameByPath(e.orgUnitPath).startsWith(QUALITY_TEAM_NAME),
    )
      ? ThreadTypeKey.QualityThread
      : ThreadTypeKey.CuramaThread;

    return {
      url: routes.terry.thread.viewDetail.replace(':storeId', storeId).replace(':type', type),
    };
  }

  @Get(routes.terry.thread.viewDetail)
  @ApiParam({ name: 'storeId', type: String })
  @ApiParam({ name: 'type', enum: ThreadTypeKey })
  @Render('admin/pages/shop-thread/details')
  @UseInterceptors(TerryStoreDetailInterceptor)
  @PageAdminSetup({ activeAdminPage: 'adminStoreSearch', activeAdminTab: 'storeConsultingMessages' })
  async getStoreThreadMessageByStoreIdAndType(
    @Param('storeId', new ParseUUIDPipe({ version: '4' })) storeId: string,
    @Param('type', new ParseEnumPipe(ThreadTypeKey, { errorHttpStatusCode: HttpStatus.NOT_FOUND }))
    threadTypeKey: ThreadTypeKey,
    @SessionAdminInfo() user: SessionAdmin,
    @Req() request: { store: StoreDetailDto },
  ) {
    if (!request?.store) {
      throw new NotFoundException('Store not found');
    }
    const threadTypeId = getThreadTypeIdByKey(threadTypeKey);
    const hasDeletePermission = await user?.can(this.configService, CuramaResources.ConsultingMessage, [
      CuramaActions.Delete,
    ]);
    return this.terryService.getStoreThreadDetails(request.store, threadTypeId, hasDeletePermission);
  }
}
