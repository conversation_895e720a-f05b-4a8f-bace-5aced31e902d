import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUI<PERSON>ipe,
  Post,
  Put,
  Render,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { BroadcastMessageService } from '@modules/shared/broadcast-message/broadcast-message.service';
import { routes } from '@src/common/routes';
import { getPrivateFile } from '@common/utils/image-handler';
import {
  CONCURRENT_MESSAGE_FILENAME_SENT_SUCCESS,
  CONCURRENT_MESSAGE_FOLDER_NAME,
  LIMIT_5_MB,
  ValidFileTypes,
} from '@common/constants';
import { formatToDateTimeWithTimezone, JP_YYYYMD_HHMM } from '@common/utils/date-util';
import { plainToClass } from 'class-transformer';
import { BroadcastMessageQueryRequest } from '../dtos/broadcast-message-query-request.dto';
import { Request } from 'express';
import { FilesInterceptor } from '@nestjs/platform-express';
import { SaveTemplateDto } from '@src/modules/shared/broadcast-message/dtos/save-template.dto';
import { MultiInputFiles } from '@src/common/decorators/multi-input-files.decorator';
import { SessionAdmin } from '@curama-auth/sessions/session-admin';
import { SessionAdminInfo } from '@curama-auth/decorators/session-admin-info.decorator';
import { AdminAuthenticatedGuard } from '@curama-auth/guards/admin-authenticated.guard';
import { CuramaActions, CuramaResources } from '@curama-auth/authorization/aws/schema';
import { ConfigService } from '@nestjs/config';
import { SaveTimeTemplateDto } from '@src/modules/shared/broadcast-message/dtos/save-time-template.dto';
import { MessageLabel } from '@common/enums/message-label';
import { PageAdminSetup } from '@vietnam/curama-py3-api';

@Controller('')
@ApiTags('Broadcast message management')
@UseGuards(AdminAuthenticatedGuard)
export class BroadcastMessageController {
  constructor(
    private readonly broadcastMessageService: BroadcastMessageService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Renders the broadcast message history view.
   *
   * @param broadcastMessageId - The ID of the broadcast message to display.
   * @returns The details of the broadcast message, including attachments and metadata for display.
   */
  @Get(routes.terry.broadcastMessage.detailHistory)
  @Render('admin/pages/broadcast-message/detail')
  @PageAdminSetup({ activeAdminPage: 'broadcastMessageManagement' })
  async displayMessageSendHistory(
    @Param('broadcastMessageId', new ParseUUIDPipe({ version: '4' })) broadcastMessageId: string,
  ) {
    const fileReportPath = `consulting-message/${CONCURRENT_MESSAGE_FOLDER_NAME}/${broadcastMessageId}/${CONCURRENT_MESSAGE_FILENAME_SENT_SUCCESS}.csv`;

    const broadcastMessage = await this.broadcastMessageService.findOneById(broadcastMessageId, [
      'createdAdmin',
      'approvedAdmin',
      'attachments',
    ]);

    return {
      label: MessageLabel.System,
      message: broadcastMessage.content.escape().urlize(),
      createdBy: broadcastMessage.createdAdmin.name,
      createdAt: formatToDateTimeWithTimezone(broadcastMessage.createdAt, JP_YYYYMD_HHMM),
      approvedBy: broadcastMessage?.approvedAdmin?.name,
      approvedAt: formatToDateTimeWithTimezone(broadcastMessage.approvedAt, JP_YYYYMD_HHMM),
      linkDownload: !broadcastMessage.storeSentSuccessCount ? '' : getPrivateFile(fileReportPath),
      storeSuccessCount: broadcastMessage.storeSentSuccessCount,
      storeUnsuccessCount: broadcastMessage.storeSentFailCount,
      attachmentFiles: broadcastMessage.attachments,
    };
  }

  /**
   * Renders the list view of all broadcast messages.
   *
   * @param request - The HTTP request object containing query parameters.
   * @returns The list of broadcast messages to display.
   */
  @Get(routes.terry.broadcastMessage.viewList)
  @Render('admin/pages/broadcast-message/list')
  @PageAdminSetup({ activeAdminPage: 'broadcastMessageGetList' })
  async getAll(@Req() request: Request) {
    const searchQuery = plainToClass(BroadcastMessageQueryRequest, request.query);
    const listBroadcastMessage = await this.broadcastMessageService.getAll(searchQuery, request);
    return { listBroadcastMessage };
  }

  @Get(routes.terry.broadcastMessage.createTemplate)
  @Render('admin/pages/broadcast-message/create')
  @PageAdminSetup({ activeAdminPage: 'broadcastMessageCreate' })
  createList() {
    return {};
  }

  /**
   * Retrieves a broadcast message by ID.
   *
   * @param id - The UUID of the broadcast message.
   * @returns The broadcast message details.
   */
  @Get(routes.terry.broadcastMessage.editTemplate)
  @Render('admin/pages/broadcast-message/create')
  @PageAdminSetup({ activeAdminPage: 'broadcastMessageEdit' })
  @ApiParam({ name: 'id', type: String })
  async getById(@Param('id', new ParseUUIDPipe({ version: '4' })) id: string, @SessionAdminInfo() user: SessionAdmin) {
    const broadcastMessage = await this.broadcastMessageService.getById(id);
    return {
      allowEdit: broadcastMessage.createdBy === user.id,
      broadcastMessage,
    };
  }

  /**
   * Retrieves a broadcast message by ID.
   *
   * @param id - The UUID of the broadcast message.
   * @returns The broadcast message details.
   */
  @Get(routes.terry.broadcastMessage.previewTemplate)
  @Render('admin/pages/broadcast-message/preview')
  @PageAdminSetup({ activeAdminPage: 'broadcastMessagePreview' })
  @ApiParam({ name: 'id', type: String })
  async preview(@Param('id', new ParseUUIDPipe({ version: '4' })) id: string, @SessionAdminInfo() user: SessionAdmin) {
    const broadcastMessage = await this.broadcastMessageService.getById(id);
    const hasApprovePermission = await user?.can(this.configService, CuramaResources.ConsultingMessage, [
      CuramaActions.Approve,
    ]);
    return {
      label: MessageLabel.System,
      broadcastMessage,
      hasApprovePermission,
      adminId: user?.id,
    };
  }

  /**
   * Retrieves a broadcast message by ID.
   *
   * @param id - The UUID of the broadcast message.
   * @returns The broadcast message details.
   */
  @Get(routes.terry.broadcastMessage.trackingTemplate)
  @ApiParam({ name: 'id', type: String })
  async getBroadcastMessageById(@Param('id', new ParseUUIDPipe({ version: '4' })) id: string) {
    const broadcastMessage = await this.broadcastMessageService.findOneById(id, ['attachments']);
    return {
      broadcastMessage,
    };
  }
  /**
   * Deletes a broadcast message and its related attachments by ID.
   *
   * @param id - The UUID of the broadcast message to delete.
   * @returns A message indicating the deletion result.
   */
  @Delete(routes.terry.broadcastMessage.getById)
  @ApiParam({ name: 'id', type: String })
  async deleteById(@Param('id', new ParseUUIDPipe({ version: '4' })) id: string) {
    return await this.broadcastMessageService.deleteBroadcastMessageAndAttachments(id);
  }

  /**
   * Updates the status of a broadcast message.
   *
   * @param id - The UUID of the broadcast message to update.
   * @param newStatus - The new status value to set.
   * @param request - The HTTP request object containing headers.
   * @returns A message indicating the status update result.
   */
  @Put(routes.terry.broadcastMessage.update)
  @ApiParam({ name: 'id', type: String })
  async updateStatusById(
    @Param('id', new ParseUUIDPipe({ version: '4' })) id: string,
    @Body('status') newStatus: number,
    @SessionAdminInfo() user: SessionAdmin,
  ) {
    return await this.broadcastMessageService.updateStatus(id, newStatus, user);
  }

  /**
   * Processes and counts the number of valid stores from a CSV file.
   *
   * @param files - An array of uploaded files (CSV).
   * @returns A list of stores extracted from the CSV file.
   */
  @Post(routes.terry.broadcastMessage.countCSV)
  @UseInterceptors(FilesInterceptor('files'))
  async countCSVFile(@UploadedFiles() files: Express.Multer.File[]) {
    const csvStream = await this.broadcastMessageService.checkCSVFile(files[0]);
    const listStore = await this.broadcastMessageService.processPreviewStore(csvStream);

    return {
      listStore,
    };
  }

  /**
   * Updates the scheduled send time for a broadcast message.
   *
   * @param id - The UUID of the broadcast message to update.
   * @param dateTime - The new scheduled send time.
   * @returns The result of saving the updated time.
   */
  @Post(routes.terry.broadcastMessage.setScheduleTime)
  @ApiParam({ name: 'id', type: String })
  async handleScheduleSendingMessage(
    @Param('id', new ParseUUIDPipe({ version: '4' })) id: string,
    @Body() dto: SaveTimeTemplateDto,
    @SessionAdminInfo() user: SessionAdmin,
  ) {
    return await this.broadcastMessageService.handleScheduleSendingMessage(id, dto, user);
  }

  /**
   * Saves or updates a broadcast message along with attachments.
   *
   * @param files - The uploaded files, including the CSV file and other attachments.
   * @param user - The session admin performing the action.
   * @param body - The data transfer object containing broadcast message details.
   * @returns The result of saving the broadcast message.
   */
  @Post(routes.terry.broadcastMessage.store)
  @MultiInputFiles([
    { name: 'files', fileType: ['text/csv'] },
    { name: 'attachments', maxCount: 5, fileType: ValidFileTypes, fileSize: LIMIT_5_MB },
  ])
  async store(
    @UploadedFiles() files: { files?: Express.Multer.File[]; attachments?: Express.Multer.File[] },
    @SessionAdminInfo() user: SessionAdmin,
    @Body() body: SaveTemplateDto,
  ) {
    return this.broadcastMessageService.save(body, user.id, files?.files?.[0], files?.attachments);
  }
  /**
   * Saves or updates a broadcast message along with attachments.
   *
   * @param files - The uploaded files, including the CSV file and other attachments.
   * @param user - The session admin performing the action.
   * @param body - The data transfer object containing broadcast message details.
   * @returns The result of saving the broadcast message.
   */
  @Post(routes.terry.broadcastMessage.update)
  @MultiInputFiles([
    { name: 'files', fileType: ['text/csv'] },
    { name: 'attachments', maxCount: 5, fileType: ValidFileTypes, fileSize: LIMIT_5_MB },
  ])
  async update(
    @Param('id', new ParseUUIDPipe({ version: '4' })) id: string,
    @UploadedFiles() files: { files?: Express.Multer.File[]; attachments?: Express.Multer.File[] },
    @SessionAdminInfo() user: SessionAdmin,
    @Body() body: SaveTemplateDto,
  ) {
    return this.broadcastMessageService.update(body, id, user.id, files?.files?.[0], files?.attachments);
  }
}
