import { StoreAssignmentDto } from '@modules/terry/dtos/store-assignment.dto';
import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { ApiBody, ApiOkResponse, ApiOperation, ApiTags, getSchemaPath, ApiQuery } from '@nestjs/swagger';
import { routes } from '@src/common/routes';
import { I18n, I18nContext } from 'nestjs-i18n';
import { TerryService } from '../terry.service';
import { InternalApiRequireAdminAuth } from '@vietnam/cnga-middleware';
import { MessageTemplateService } from '../../shared/message-template/message-template.service';
import { SelectMessageTemplateDto } from '../dtos/select-message-template.dto';
import { SelectPatrolTemplateResponseDto } from '../dtos/internal-api.dto';
import { AdminService } from '@src/modules/shared/admin/admin.service';
import { uuidValidateV4 } from '@src/common/utils/helper';
import { HttpResponseFormatter } from '@src/common/response/response';
import { TeamService } from '@src/modules/shared/team/team.service';

@Controller('')
@ApiTags('Internal API')
export class InternalApiController {
  constructor(
    private readonly terryService: TerryService,
    private readonly messageTemplateService: MessageTemplateService,
    private readonly adminService: AdminService,
    private readonly teamService: TeamService,
  ) {}

  @Get(routes.internalApi.getMessageTemplateGroupByCategory)
  @ApiOperation({
    operationId: 'getTemplateByCategory',
    description: 'get list message template by category',
  })
  getMessageTemplateGroupByCategory(@Query('search') search?: string) {
    return this.messageTemplateService.getMessageTemplateGroupByCategory(search);
  }

  @Put(routes.internalApi.assignments)
  @ApiOperation({ summary: '店舗の担当者、担当チーム、品質管理担当者を更新する' })
  @ApiOkResponse({
    description: 'OK',
    schema: { $ref: getSchemaPath(StoreAssignmentDto) },
  })
  @UseGuards(InternalApiRequireAdminAuth)
  async storeAssignment(
    @Param('storeId', new ParseUUIDPipe({ version: '4' })) storeId,
    @Body() storeAssignmentDto: StoreAssignmentDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.terryService.storeAssignment(storeId, storeAssignmentDto);
    return {
      message: i18n.t('general.storeSuccessAssignment'),
    };
  }

  @Post(routes.internalApi.selectPatrolTemplate)
  @ApiBody({
    description: 'selectPatrolMessageTemplate payload',
    type: SelectMessageTemplateDto,
  })
  @ApiOperation({ operationId: 'selectPatrolMessageTemplate', description: 'save draft and create task by template' })
  @ApiOkResponse({
    description: 'OK',
    type: SelectPatrolTemplateResponseDto,
  })
  selectPatrolMessageTemplate(
    @Body() selectMessageTemplateRequest: SelectMessageTemplateDto,
    @Param('templateId', new ParseUUIDPipe({ version: '4' })) templateId: string,
  ) {
    selectMessageTemplateRequest.templateId = templateId;
    return this.terryService.selectPatrolMessageTemplate(selectMessageTemplateRequest);
  }

  @Get(routes.internalApi.admins)
  @UseGuards(InternalApiRequireAdminAuth)
  @ApiQuery({ name: 'teamId', type: String })
  async getAdmins(@Query('teamId') teamId: string, @Query('select') select: string[]) {
    if (teamId) {
      if (!uuidValidateV4(teamId)) throw new BadRequestException('TeamId Invalid UUID v4');
      return this.adminService.getAdmins(teamId, select);
    } else {
      const data = await this.adminService.find({ select } as any);
      return HttpResponseFormatter.responseOK({ data });
    }
  }

  @Get(routes.internalApi.teams)
  @UseGuards(InternalApiRequireAdminAuth)
  async getTeams(@Query('select') select: string[], @Query('relations') relations: string[]) {
    const data = await this.teamService.find({ select, relations } as any);
    return HttpResponseFormatter.responseOK({ data });
  }
}
