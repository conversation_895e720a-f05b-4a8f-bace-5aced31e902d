import {
  Body,
  Controller,
  Delete,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Req,
  UploadedFiles,
  UseGuards,
} from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { ApiFiles } from '@src/common/decorators/api-files.decorator';
import { AdminAuthenticatedGuard } from '@curama-auth/guards/admin-authenticated.guard';
import { SessionAdminInfo } from '@curama-auth/decorators/session-admin-info.decorator';
import { SessionAdmin } from '@curama-auth/sessions/session-admin';
import { DraftService } from '@src/modules/shared/draft/draft.service';
import { UpsertDraftDto } from '@src/modules/shared/draft/dtos/upsert-draft.dto';
import { routes } from '@src/common/routes';
import { Request } from 'express';
import { StoreDetailDto, TerryStoreDetailInterceptor } from '@vietnam/curama-py3-api';

@Controller('')
@UseGuards(AdminAuthenticatedGuard)
@ApiTags('Draft Views')
export class DraftController {
  constructor(private readonly draftService: DraftService) {}

  @Delete(routes.terry.draft.delete)
  @ApiParam({ name: 'draftId', type: String })
  delete(@Param('draftId', new ParseUUIDPipe({ version: '4' })) draftId: string) {
    return this.draftService.delete(draftId);
  }

  @Put(routes.terry.draft.convert)
  @ApiParam({ name: 'draftId', type: String })
  saveDraftToMessage(
    @Param('draftId', new ParseUUIDPipe({ version: '4' })) draftId: string,
    @SessionAdminInfo() user: SessionAdmin,
    @Req() request: Request,
  ) {
    return this.draftService.saveDraftToMessage(draftId, user.id, request);
  }
  @Post(routes.terry.draft.upsert)
  @ApiFiles('files', 5, {}, [TerryStoreDetailInterceptor])
  upsertDraft(
    @UploadedFiles() files: Express.Multer.File[],
    @Body() body: UpsertDraftDto,
    @SessionAdminInfo() user: SessionAdmin,
    @Req() request: { store: StoreDetailDto },
  ) {
    return this.draftService.upsertDraft(files, body, user.id, request.store);
  }
}
