import { <PERSON>, Delete, Get, Param, ParseU<PERSON><PERSON>ipe, Query, UseGuards } from '@nestjs/common';
import { ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { SessionAdminInfo } from '@curama-auth/decorators/session-admin-info.decorator';
import { SessionAdmin } from '@curama-auth/sessions/session-admin';
import { AdminAuthenticatedGuard } from '@curama-auth/guards/admin-authenticated.guard';
import { MessageService } from '@src/modules/shared/message/message.service';
import { routes } from '@src/common/routes';
import { PagingMessageQueryDto } from '../dtos/paging-message.dto';

@Controller('')
@ApiTags('Message')
@UseGuards(AdminAuthenticatedGuard)
export class MessageController {
  constructor(private readonly messageService: MessageService) {}

  @Delete(routes.terry.message.delete)
  @ApiParam({ name: 'id', type: String })
  deleteMessage(
    @Param('id', new ParseUUIDPipe({ version: '4' })) messageId: string,
    @SessionAdminInfo() user: SessionAdmin,
  ) {
    return this.messageService.delete(messageId, user.id);
  }
  @Get(routes.terry.message.getPagingMessage)
  @ApiQuery({ name: 'threadId', type: String })
  @ApiQuery({ name: 'limit', type: Number })
  @ApiQuery({ name: 'offset', type: Number })
  getPagingMessages(@Query() query: PagingMessageQueryDto) {
    return this.messageService.getPagingMessages(query);
  }
}
