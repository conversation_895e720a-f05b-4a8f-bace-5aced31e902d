import { AdminAuthenticatedGuard } from '@curama-auth/guards/admin-authenticated.guard';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Query, Render, Req, UseGuards } from '@nestjs/common';
import { Api<PERSON>aram, ApiQ<PERSON>y, ApiTags } from '@nestjs/swagger';
import { PageAdminSetup } from '@vietnam/curama-py3-api';
import { routes } from '@src/common/routes';
import { TasksQueryRequest } from '@src/modules/shared/task/dtos/tasks-query-request.do';
import { TaskService } from '@src/modules/shared/task/task.service';
import { Request } from 'express';
import { PagingTaskQueryDto } from '../dtos/paging-task.dto';
import { SessionAdminInfo } from '@curama-auth/decorators/session-admin-info.decorator';
import { SessionAdmin } from '@curama-auth/sessions/session-admin';
import { UpsertTaskDto } from '@src/modules/shared/task/dtos/upsert-task.dto';
import { plainToClass } from 'class-transformer';
import { I18n, I18nContext } from 'nestjs-i18n';
import { validationExceptionArrayMessages } from '@src/common/utils/helper';
import { TeamService } from '@src/modules/shared/team/team.service';
import { AdminService } from '@src/modules/shared/admin/admin.service';
import { PaginationTasks } from '@src/modules/shared/task/dtos/pagination-tasks.dto';
import { WorkTypeService } from '@src/modules/shared/work-type/work-type.service';
import { TaskStatusMapping } from '@src/common/constants';

@Controller('')
@ApiTags('Task Views')
@UseGuards(AdminAuthenticatedGuard)
export class TaskController {
  constructor(
    private readonly taskService: TaskService,
    private readonly teamService: TeamService,
    private readonly adminService: AdminService,
    private readonly workTypeService: WorkTypeService,
  ) {}

  @Get(routes.terry.task.viewList)
  @Render('admin/pages/task/list')
  @PageAdminSetup({ activeAdminPage: 'taskManagement' })
  async getListOfTask(@Req() request: Request, @I18n() i18n: I18nContext, @SessionAdminInfo() user: SessionAdmin) {
    request.query = {
      assignmentTeamId: user?.teams[0]?.id,
      assignmentAdminId: user.id,
      ...request.query,
    };

    const searchQuery = plainToClass(TasksQueryRequest, request.query);
    const validation = await i18n.validate(searchQuery);
    let errors = [];
    let tasks: any;
    if (validation.length > 0) {
      errors = validationExceptionArrayMessages(validation);
      tasks = new PaginationTasks({ tasks: [], request });
    } else {
      tasks = await this.taskService.getListOfTask(searchQuery, request);
    }

    const [teams, admins, workTypes] = await Promise.all([
      this.teamService.find({ where: { isLast: true }, select: ['id', 'name'] }),
      this.adminService.find({ select: ['id', 'name'] }),
      this.workTypeService.find({ select: ['id', 'name'] }),
    ]);

    const statuses = Object.entries(TaskStatusMapping).map((e) => ({ id: e[0], name: e[1] }));
    return { data: { ...tasks, statuses, teams, admins, workTypes, query: request.query }, errors };
  }

  @Get(routes.terry.task.getPagingTask)
  @ApiQuery({ name: 'threadId', type: String })
  @ApiQuery({ name: 'limit', type: Number })
  @ApiQuery({ name: 'offset', type: Number })
  getPagingTasks(@Query() query: PagingTaskQueryDto) {
    return this.taskService.getPagingTasks(query);
  }
  @Post(routes.terry.task.upsert)
  upsertTask(@Body() body: UpsertTaskDto, @SessionAdminInfo() user: SessionAdmin) {
    return this.taskService.upsertTask(body, user.id);
  }

  @Get(routes.terry.task.getForCreate)
  getDataForCreateTask(@SessionAdminInfo() user: SessionAdmin) {
    return this.taskService.getDataForCreate(user);
  }

  @Get(routes.terry.task.getForEdit)
  @ApiParam({ name: 'id', type: String })
  getDataForUpdate(@Param('id', new ParseUUIDPipe({ version: '4' })) taskId: string) {
    return this.taskService.getDataForUpdate(taskId);
  }
}
