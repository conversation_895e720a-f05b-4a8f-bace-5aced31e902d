import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  Render,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PageAdminSetup } from '@vietnam/curama-py3-api';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { NgWordService } from '@src/modules/shared/ng-word/ng-word.service';
import { NgWordDto } from '@src/modules/shared/ng-word/dtos/ng-word.dto';
import { routes } from '@src/common/routes';
import { AdminAuthenticatedGuard } from '@curama-auth/guards/admin-authenticated.guard';

@Controller('')
@ApiTags('NG Word Views')
@UseGuards(AdminAuthenticatedGuard)
export class NgWordController {
  constructor(private readonly ngWordService: NgWordService) {}

  @Get(routes.terry.ngWord.viewList)
  @Render('admin/pages/ng-word/list')
  @PageAdminSetup({ activeAdminPage: 'messageManagement' })
  async listNgWord(@Req() request: Request, @Query('page') page?: number) {
    const listNgWord = await this.ngWordService.getListNgWord(request, page);

    return {
      listNgWord,
    };
  }

  @Post(routes.terry.ngWord.save)
  async store(@Body() ngWordRequest: NgWordDto) {
    return await this.ngWordService.save(ngWordRequest);
  }

  @Delete(routes.terry.ngWord.delete)
  @ApiParam({ name: 'id', type: String })
  async delete(@Param('id', new ParseUUIDPipe({ version: '4' })) ngWordId: string) {
    return await this.ngWordService.delete(ngWordId);
  }
}
