import { AdminAuthenticatedGuard } from '@curama-auth/guards/admin-authenticated.guard';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  Render,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { PageAdminSetup } from '@vietnam/curama-py3-api';
import { routes } from '@src/common/routes';
import { uuidValidateV4 } from '@src/common/utils/helper';
import { MessageTemplateDto } from '@src/modules/shared/message-template/dtos/MessageTemplate.dto';
import { PaginateMessageTemplateDto } from '@src/modules/shared/message-template/dtos/paginate-message-template.dto';
import { MessageTemplateService } from '@src/modules/shared/message-template/message-template.service';
import { Request } from 'express';

@Controller('')
@ApiTags('Message Template')
@UseGuards(AdminAuthenticatedGuard)
export class MessageTemplateController {
  constructor(private readonly messageTemplateService: MessageTemplateService) {}

  @Get(routes.terry.messageTemplate.subCategory)
  @ApiQuery({ name: 'largeCategoryId', type: String })
  getSubCategories(@Query('largeCategoryId', new ParseUUIDPipe({ version: '4' })) largeCategoryId: string) {
    return this.messageTemplateService.getSubCategories(largeCategoryId);
  }

  @Get(routes.terry.messageTemplate.getTemplate)
  @ApiQuery({ name: 'groupId', type: String })
  getMessageTemplate(@Query('groupId') groupId: string, @Query('search') search: string) {
    if (groupId) {
      if (!uuidValidateV4(groupId)) throw new BadRequestException('Invalid UUID v4');
    }
    return this.messageTemplateService.getTemplates(groupId, search);
  }

  @Get(routes.terry.messageTemplate.viewList)
  @Render('admin/pages/message-template/list')
  @PageAdminSetup({ activeAdminPage: 'messageManagement' })
  async getAll(@Query() query: PaginateMessageTemplateDto, @Req() request: Request) {
    const listMessageTemplate = await this.messageTemplateService.getAll(query, request);

    return {
      listMessageTemplate,
    };
  }

  @Post(routes.terry.messageTemplate.save)
  async store(@Body() messageRequest: MessageTemplateDto) {
    return await this.messageTemplateService.save(messageRequest);
  }

  @Get(routes.terry.messageTemplate.getDetail)
  @ApiParam({ name: 'id', type: String })
  async edit(@Param('id', new ParseUUIDPipe({ version: '4' })) id: string) {
    return await this.messageTemplateService.getById(id, ['groupParent', 'groupParent.groupParent']);
  }

  @Delete(routes.terry.messageTemplate.delete)
  @ApiParam({ name: 'id', type: String })
  async delete(@Param('id', new ParseUUIDPipe({ version: '4' })) messageTemplateId: string) {
    return await this.messageTemplateService.delete(messageTemplateId);
  }
}
