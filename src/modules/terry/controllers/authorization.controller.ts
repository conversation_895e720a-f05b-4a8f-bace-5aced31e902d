import { routes } from './../../../common/routes/index';
import { AWSVerifiedPermissionProvider } from '@curama-auth/authorization';
import { CuramaActions, CuramaPrincipal, CuramaResources } from '@curama-auth/authorization/aws/schema';
import { CURAMA_AUTHORIZATION_SERVICE, CURAMA_AUTH_SERVICE } from '@curama-auth/constants';
import { AdminEntity, AdminStatus } from '@curama-auth/entities/admin.entity';
import { AdminAuthenticatedGuard } from '@curama-auth/guards/admin-authenticated.guard';
import { IdentityServiceProvider } from '@curama-auth/identities';
import {
  Body,
  Controller,
  Get,
  Inject,
  NotFoundException,
  Param,
  ParseUUIDPipe,
  Post,
  Redirect,
  Render,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiBody, ApiParam, ApiTags } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { PageAdminSetup } from '@vietnam/curama-py3-api';
import { Repository } from 'typeorm';
import { Request, Response } from 'express';
import { AdminAuthorization } from '@curama-auth/decorators/admin-authorization.decorator';

@Controller('')
@ApiTags('Authorization Management')
export class AuthorizationController {
  constructor(
    @Inject(CURAMA_AUTHORIZATION_SERVICE) private readonly verifiedPermissionService: AWSVerifiedPermissionProvider,
    @InjectRepository(AdminEntity) private readonly adminRepository: Repository<AdminEntity>,

    @Inject(CURAMA_AUTH_SERVICE) private readonly authService: IdentityServiceProvider,
    private readonly configService: ConfigService,
  ) {}
  @Get(routes.terry.auth.login)
  @Render('admin/login')
  showLoginForm() {
    const loginUrl = this.authService.getOauth2Url();
    return {
      loginUrl,
    };
  }

  @Get(routes.terry.auth.logout)
  @Redirect(routes.terry.auth.login)
  logout(@Req() req: Request) {
    this.authService.signOut(req);
  }

  @Get(routes.terry.auth.callback)
  async oauth2Callback(@Req() req, @Res() res: Response): Promise<any> {
    await this.authService.authenticate(req);
    const intendedUrl = req?.cookies ? req.cookies['intendedUrl'] : req?.terrySession?.intendedUrl;
    res.clearCookie('intendedUrl');
    return res.redirect(intendedUrl ?? this.configService.get<string>('curama-auth.app.auth.homeUrl'));
  }

  @Get(routes.terry.auth.authorization)
  @AdminAuthorization(CuramaResources.Policy, CuramaActions.Edit)
  @UseGuards(AdminAuthenticatedGuard)
  @Render('admin/pages/authorization/index')
  @PageAdminSetup({ activeAdminPage: 'UserAccounts' })
  async index() {
    // get all schemas excludes Curama::User
    const policies = await this.verifiedPermissionService.getAllPoliciesResourcesWithActions();
    const admins = await this.adminRepository.findBy({ status: AdminStatus.Active });
    const departmentTemplates = [];
    return { policies, admins, departmentTemplates };
  }

  @Get(routes.terry.auth.getPolicy)
  @AdminAuthorization(CuramaResources.Policy, CuramaActions.Edit)
  @UseGuards(AdminAuthenticatedGuard)
  @ApiParam({ name: 'adminId', type: String })
  async getUserPolicies(@Param('adminId', new ParseUUIDPipe({ version: '4' })) adminId: string) {
    const admin = await this.adminRepository.findOneBy({ id: adminId });

    if (!admin) {
      throw new NotFoundException('申し訳ありませんが、指定されたユーザーは見つかりませんでした。');
    }

    const userPolicies = await this.verifiedPermissionService.getUserPoliciesResourcesWithActions(
      admin as unknown as CuramaPrincipal,
    );

    return { userPolicies };
  }

  @Post(routes.terry.auth.postPolicy)
  @AdminAuthorization(CuramaResources.Policy, CuramaActions.Edit)
  @UseGuards(AdminAuthenticatedGuard)
  @ApiParam({ name: 'adminId', type: String })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        policies: {
          type: 'array',
        },
      },
    },
  })
  async updateUserPolicies(
    @Param('adminId', new ParseUUIDPipe({ version: '4' })) adminId: string,
    @Body('policies') policies: string[],
  ) {
    const admin = await this.adminRepository.findOneBy({ id: adminId });

    if (!admin) {
      throw new NotFoundException('申し訳ありませんが、指定されたユーザーは見つかりませんでした。');
    }

    await this.verifiedPermissionService.updateUserPolicies(admin as unknown as CuramaPrincipal, policies);

    return { message: 'ユーザーのポリシーが正常に追加されました。' };
  }
}
