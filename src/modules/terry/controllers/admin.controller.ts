import { AdminAuthenticatedGuard } from '@curama-auth/guards/admin-authenticated.guard';
import { BadRequestException, Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiQuery, ApiTags } from '@nestjs/swagger';
import { HttpResponseFormatter } from '@src/common/response/response';
import { routes } from '@src/common/routes';
import { uuidValidateV4 } from '@src/common/utils/helper';
import { AdminService } from '@src/modules/shared/admin/admin.service';

@Controller('')
@ApiTags('Admin Management')
@UseGuards(AdminAuthenticatedGuard)
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get(routes.terry.admin.list)
  @ApiQuery({ name: 'teamId', type: String })
  async getAdmins(@Query('teamId') teamId: string) {
    if (teamId) {
      if (!uuidValidateV4(teamId)) throw new BadRequestException('Invalid UUID v4');
      return this.adminService.getAdmins(teamId);
    } else {
      const data = await this.adminService.find({ select: ['id', 'name'] });
      return HttpResponseFormatter.responseOK({ data });
    }
  }
}
