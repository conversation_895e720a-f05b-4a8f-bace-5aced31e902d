import { MessageModule } from '../shared/message/message.module';
import { InquiryThreadModule } from '@modules/shared/inquiry-thread/inquiry-thread.module';
import { Module } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>roller } from './terry.controller';
import { CuramaPy3ApiModule } from '@vietnam/curama-py3-api';
import { TerryService } from './terry.service';
import { MessageTemplateModule } from '../shared/message-template/message-template.module';
import { TeamModule } from '../shared/team/team.module';
import { NgWordModule } from '../shared/ng-word/ng-word.module';
import { AuthModule } from '@curama-auth/auth.module';
import { AdminModule } from '../shared/admin/admin.module';
import { TaskModule } from '../shared/task/task.module';
import { AttachmentModule } from '../shared/attachment/attachment.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QualityAdminsStoresEntity } from '@src/entities/quality-admins-stores.entity';
import { AdminsStoresEntity, InquiryThreadEntity, TeamEntity } from '@src/entities';
import { DraftModule } from '../shared/draft/draft.module';
import { TaskController } from './controllers/task.controller';
import { NgWordController } from './controllers/ng-word.controller';
import { AdminController } from './controllers/admin.controller';
import { DraftController } from './controllers/draft.controller';
import { MessageTemplateController } from './controllers/message-template.controller';
import { MessageController } from './controllers/message.controller';
import { AuthorizationController } from './controllers/authorization.controller';
import { AdminEntity } from '@curama-auth/entities/admin.entity';
import { InternalApiController } from './controllers/internal-api.controller';
import { CanaryReleaseStoreModule } from '../shared/canary-release-store/canary-release-store.module';
import { WorkTypeModule } from '../shared/work-type/work-type.module';
import { NotificationModule } from '@modules/shared/notification/notification.module';
import { QueueProvider } from '@providers/queue.provider';
import { RedisProvider } from '@providers/redis.provider';
import { BroadcastMessageController } from '@modules/terry/controllers/broadcast-message.controller';
import { BroadcastMessageModule } from '@modules/shared/broadcast-message/broadcast-message.module';

@Module({
  imports: [
    QueueProvider.register(),
    RedisProvider.register(),
    TypeOrmModule.forFeature([
      QualityAdminsStoresEntity,
      AdminsStoresEntity,
      AdminEntity,
      InquiryThreadEntity,
      TeamEntity,
    ]),
    CuramaPy3ApiModule,
    MessageTemplateModule,
    BroadcastMessageModule,
    TeamModule,
    AdminModule,
    InquiryThreadModule,
    MessageModule,
    NgWordModule,
    AuthModule,
    TaskModule,
    AttachmentModule,
    DraftModule,
    WorkTypeModule,
    CanaryReleaseStoreModule,
    QueueProvider.register(),
    NotificationModule,
  ],
  controllers: [
    TerryController,
    AdminController,
    DraftController,
    MessageTemplateController,
    BroadcastMessageController,
    MessageController,
    NgWordController,
    TaskController,
    AuthorizationController,
    InternalApiController,
    BroadcastMessageController,
  ],
  providers: [TerryService],
  exports: [TerryService],
})
export class TerryModule {}
