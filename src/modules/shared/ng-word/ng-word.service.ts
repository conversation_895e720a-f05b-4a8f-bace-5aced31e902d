import { Injectable } from '@nestjs/common';
import { NgWordEntity } from '@src/entities';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Request } from 'express';
import { NgWordDto } from './dtos/ng-word.dto';
import { HttpResponseFormatter } from '@src/common/response/response';
import { PAGINATOR } from '@src/common/constants/paginator';

@Injectable()
export class NgWordService {
  constructor(
    @InjectRepository(NgWordEntity)
    private readonly ngWordRepository: Repository<NgWordEntity>,
  ) {}

  async getListNgWord(request: Request, page: number): Promise<object> {
    const perPage = PAGINATOR.NGWORD;
    const pageNumber = parseInt(String(page ? page : 1));

    const [list, total] = await this.ngWordRepository.findAndCount({
      take: perPage,
      skip: page > 0 ? (page - 1) * perPage : 0,
      order: {
        createdAt: 'DESC',
      },
    });

    return {
      list,
      pagination: { pageNumber, total, perPage, request: { query: request.query, path: request.path } },
    };
  }

  /**
   * Create new ng word
   * @param ngWordRequest NgWordEntity entity
   */
  async save(ngWordRequest: NgWordDto) {
    const existNgWord = await this.ngWordRepository.findOne({
      where: {
        content: ngWordRequest.content,
      },
    });

    if (existNgWord) {
      return HttpResponseFormatter.formatErrorResponse({
        status: 400,
        message: 'NGワードは既に存在しています。',
      });
    }

    const ngWordEntity = new NgWordEntity();
    ngWordEntity.content = ngWordRequest.content;

    await this.ngWordRepository.save(ngWordEntity);

    return HttpResponseFormatter.responseOK({ data: ngWordEntity });
  }

  /**
   * Delete ng word
   * @param ngWordId
   */
  async delete(ngWordId: string) {
    try {
      const result = await this.ngWordRepository.delete({ id: ngWordId });
      return HttpResponseFormatter.formatSuccessResponse({
        data: result,
      });
    } catch (err) {
      return HttpResponseFormatter.responseInternalError({ errors: err.message });
    }
  }
  async checkNGWord(content: string) {
    const words = await this.ngWordRepository
      .createQueryBuilder('NgWords')
      .select('content', 'content')
      .where(":content ILIKE '%' || content || '%'", { content })
      .getRawMany();
    return words.map((e) => e.content);
  }
}
