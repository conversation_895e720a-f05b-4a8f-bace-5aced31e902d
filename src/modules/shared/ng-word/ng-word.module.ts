import { Module } from '@nestjs/common';
import { NgWordService } from '@modules/shared/ng-word/ng-word.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NgWordEntity } from '@src/entities';
import { CuramaPy3ApiModule } from '@vietnam/curama-py3-api';

@Module({
  imports: [CuramaPy3ApiModule, TypeOrmModule.forFeature([NgWordEntity])],
  exports: [NgWordService],
  providers: [NgWordService],
})
export class NgWordModule {}
