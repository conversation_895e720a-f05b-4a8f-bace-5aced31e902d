import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Between, DataSource, EntityManager, In, Repository } from 'typeorm';
import * as csv from 'csv-parser';
import { Readable } from 'stream';
import { StoreEntity } from '@src/entities/store.entity';
import { Py3CommonRequestHeadersInterface } from '@vietnam/cnga-http-request';
import { uuidValidateV4 } from '@common/utils/helper';
import { AttachmentFileUploadType } from '@common/types/attachment-file-upload.type';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

import {
  BroadcastMessageStatus,
  CONCURRENT_MESSAGE_FOLDER_NAME,
  CONCURRENT_MESSAGE_MINUTE_DELAY,
  CONCURRENT_MESSAGE_MQ_NAME,
  CONCURRENT_MESSAGE_SIZE_BATCH,
  StoreStatus,
  BULLMQ_MESSAGE_BROADCAST_QUEUE,
  BULLMQ_JOB_SEND_MESSAGE_BATCH,
} from '@common/constants';
import { AttachmentEntity } from '@src/entities';
import { BroadcastMessageEntity } from '@src/entities/broadcast-message.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { StoresInfo } from '@modules/terry/types/csv-record';
import { BroadcastMessageQueryRequest } from '@src/modules/terry/dtos/broadcast-message-query-request.dto';
import { Request } from 'express';
import { SaveTemplateDto } from './dtos/save-template.dto';
import { AttachmentService } from '../attachment/attachment.service';
import { getPrivateFile } from '@src/common/utils/image-handler';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { randomUUID } from 'crypto';
import { ACCOUNT_TYPES } from '@vietnam/cnga-middleware';
import { SaveTimeTemplateDto } from './dtos/save-time-template.dto';
import { SessionAdmin } from '@curama-auth/sessions/session-admin';
import { v4 as uuidv4 } from 'uuid';
import { SlackService } from '../notification/slack.service';
import { formatDateWithJapaneseWeekday, JP_YYYYMD_HHMM_TZ } from '@src/common/utils/date-util';
import { HttpResponseFormatter } from '@src/common/response/response';
import { finished } from 'stream/promises';
@Injectable()
export class BroadcastMessageService {
  private readonly logger = new Logger(BroadcastMessageService.name);

  constructor(
    private readonly amqpConnection: AmqpConnection,
    private readonly dataSource: DataSource,
    private readonly attachmentService: AttachmentService,
    private readonly httpService: HttpService,
    private readonly slackService: SlackService,
    @InjectRepository(StoreEntity) private storeRepository: Repository<StoreEntity>,
    @InjectRepository(AttachmentEntity) private attachmentRepository: Repository<AttachmentEntity>,
    @InjectRepository(BroadcastMessageEntity) private broadcastMessageRepository: Repository<BroadcastMessageEntity>,
    @InjectQueue(BULLMQ_MESSAGE_BROADCAST_QUEUE) private readonly messageBroadcastQueue: Queue,
  ) {}

  /**
   * Retrieves a CSV file from S3 and returns it as a readable stream.
   *
   * This method fetches the CSV file from an S3 bucket using a private URL and
   * converts the retrieved data into a readable stream.
   *
   * @param csvFilePath - The S3 path of the CSV file.
   * @returns A readable stream of the CSV file data.
   * @throws InternalServerErrorException if an error occurs while retrieving or processing the file.
   */
  async getStreamFromS3(csvFilePath: string): Promise<Readable> {
    const csvURL = getPrivateFile(csvFilePath);
    const csvFile = await lastValueFrom(this.httpService.get(csvURL, { responseType: 'arraybuffer' }));
    return this.bufferToStream(csvFile.data);
  }

  /**
   * Get valid store IDs from a CSV file in S3, filtering by those with status = true.
   * @param csvFilePath The S3 path to the CSV file (`import_file_path` column in `BroadcastMessages` table).
   * @param attachmentFiles List of attachment files.
   * @returns A list of objects containing store IDs and names where status = true.imageString
   */
  async getValidStoreIdsFromCsv(csvFilePath: string): Promise<StoresInfo[]> {
    try {
      const s3Stream = await this.getStreamFromS3(csvFilePath);
      const result = await this.processCSVData(s3Stream);
      return result.publicAndSuspendedStores;
    } catch (error) {
      this.logger.log('Unable to access the file from S3');
      return [];
    }
  }

  async getCountStoreByStatus(statuses: number[]): Promise<number> {
    const storesCount = await this.storeRepository.count({
      where: {
        status: In(statuses),
      },
    });
    return storesCount;
  }

  /**
   * Processes the CSV data stream to extract store IDs and fetch their status.
   *
   * This method reads a CSV file stream and processes store IDs in batches. For each batch,
   * it checks the store status and adds stores with a valid (true) status to the result array.
   * Batching ensures efficient handling of large datasets.
   *
   * @param stream - The readable stream of CSV data.
   * @param checkAll - A boolean flag indicating whether to process all store IDs (true) or stop after finding 30 valid stores (false).   * @returns An array of stores with a valid (true) status.
   * @param attachmentFiles List of attachment files.
   * @throws InternalServerErrorException if an error occurs during processing or batch handling.
   */
  async processStoreCount(stream: Readable) {
    let totalRow = 0;
    let totalStoresSuccess = 0;
    let batchStoreIds: string[] = [];
    const batchPromises: Promise<void>[] = [];

    const processBatch = async (ids: string[]) => {
      try {
        const validStores = await this.getStoreLengthByStatus(ids);
        totalStoresSuccess += validStores;
      } catch (error) {
        this.logger.error(`Error checking store status for IDs: ${ids.join(', ')}`, error);
        throw error;
      }
    };

    try {
      const parser = csv();

      stream.pipe(parser);

      parser.on('data', (row) => {
        const storeId = row.id?.trim();
        if (storeId && uuidValidateV4(storeId)) {
          totalRow++;
          batchStoreIds.push(storeId);

          if (batchStoreIds.length >= CONCURRENT_MESSAGE_SIZE_BATCH) {
            batchPromises.push(processBatch([...batchStoreIds]));
            batchStoreIds = [];
          }
        }
      });

      await finished(parser);

      if (batchStoreIds.length > 0) {
        batchPromises.push(processBatch(batchStoreIds));
      }

      await Promise.all(batchPromises);

      return {
        totalRow,
        totalStoresSuccess,
      };
    } catch (error) {
      this.logger.error(`Error processing store counts from the CSV: ${error.message}`, error);
      throw error;
    }
  }
  async processCSVData(stream: Readable) {
    try {
      let totalRow = 0;
      const publicAndSuspendedStores: StoresInfo[] = [];
      let batchStoreIds: string[] = [];
      const batchPromises: Promise<void>[] = [];
      const parser = csv();
      const getPublicAndSuspendedStores = async (ids: string[]) => {
        try {
          const validStores = await this.getStoreStatusByIds(ids);
          publicAndSuspendedStores.push(...validStores);
        } catch (error) {
          this.logger.log(`Have an error while check store status: ${ids.join(',')}`);
          throw error;
        }
      };

      await new Promise((resolve, reject) => {
        stream
          .pipe(parser)
          .on('data', async (row) => {
            row.id = row.id.trim();
            if (!uuidValidateV4(row.id)) {
              resolve(true);
            }
            if (row.id) {
              totalRow++;
              batchStoreIds.push(row.id);
            }
            if (batchStoreIds.length >= CONCURRENT_MESSAGE_SIZE_BATCH) {
              const currentBatch = [...batchStoreIds];
              batchStoreIds = [];
              const batchPromise = getPublicAndSuspendedStores(currentBatch);
              batchPromises.push(batchPromise);
            }
          })
          .on('end', async () => {
            if (batchStoreIds.length > 0) {
              const lastBatchPromise = getPublicAndSuspendedStores(batchStoreIds);
              batchPromises.push(lastBatchPromise);
            }
            await Promise.all(batchPromises);
            resolve(true);
          })
          .on('error', (error) => {
            reject(error);
          });
      });
      return {
        totalRow,
        publicAndSuspendedStores: publicAndSuspendedStores,
      };
    } catch (error) {
      this.logger.log(`An error occurred while retrieving the store ID from the CSV: ${error?.message}`);
      throw error;
    }
  }
  async processPreviewStore(stream: Readable) {
    try {
      const publicAndSuspendedStores: StoresInfo[] = [];
      let batchStoreIds: string[] = [];
      const parser = csv();
      await new Promise((resolve, reject) => {
        stream
          .pipe(parser)
          .on('data', async (row) => {
            row.id = row.id.trim();
            if (!uuidValidateV4(row.id)) {
              resolve(true);
            }
            if (row.id) {
              batchStoreIds.push(row.id);
            }
            if (batchStoreIds.length >= CONCURRENT_MESSAGE_SIZE_BATCH) {
              parser.pause();
              const currentBatch = [...batchStoreIds];
              batchStoreIds = [];
              const validStores = await this.getStoreStatusByIds(currentBatch);
              publicAndSuspendedStores.push(...validStores);
            }
            if (publicAndSuspendedStores.length > 30) {
              parser.destroy();
              resolve(true);
            }
            parser.resume();
          })
          .on('end', async () => {
            if (batchStoreIds.length > 0) {
              const validStores = await this.getStoreStatusByIds(batchStoreIds);
              publicAndSuspendedStores.push(...validStores);
            }
            if (publicAndSuspendedStores.length > 30) {
              parser.destroy();
              resolve(true);
            }
            resolve(true);
          })
          .on('error', (error) => {
            reject(error);
          });
      });
      return {
        publicAndSuspendedStores: publicAndSuspendedStores.slice(0, 30),
      };
    } catch (error) {
      this.logger.log(`An error occurred while retrieving the store ID from the CSV: ${error?.message}`);
      throw error;
    }
  }
  /**
   * Validates and processes a CSV file.
   *
   * This method checks if the uploaded file exists and if it is a valid CSV file.
   * If the file is valid, it converts the file into a stream and processes the CSV data.
   *
   * @param file - The uploaded CSV file to validate and process.
   * @returns The result of processing the CSV data.
   * @throws BadRequestException if the file is not uploaded or is not a valid CSV file.
   */
  async checkCSVFile(file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('The CSV file has not been uploaded');
    }
    if (!file.mimetype.includes('csv')) {
      throw new BadRequestException('Please upload the CSV file');
    }
    return this.bufferToStream(file.buffer);
  }

  /**
   * Process the broadcast message and send concurrent messages to valid store IDs.
   * @param broadcastMessage The BroadcastMessageEntity containing message details.
   * @returns A success message if the messages are sent successfully.
   */
  async processAndSendMessages(broadcastMessage: BroadcastMessageEntity): Promise<any> {
    try {
      // Prepare broadcast data for BullMQ jobs
      const attachmentFiles = await this.getAttachmentFilesByRelationId(broadcastMessage.id);
      const broadcastData = {
        content: broadcastMessage.content,
        attachments: attachmentFiles,
      };

      const validStores = broadcastMessage.importFilePath
        ? await this.getValidStoreIdsFromCsv(broadcastMessage.importFilePath)
        : [];
      const totalStores = broadcastMessage.importFilePath
        ? validStores.length
        : await this.getCountStoreByStatus([
            StoreStatus.NewlyApplied,
            StoreStatus.InReview,
            StoreStatus.Published,
            StoreStatus.OnHold,
            StoreStatus.Suspended,
          ]);
      if (totalStores < 1) {
        await this.broadcastMessageRepository.update(broadcastMessage.id, {
          storeSentSuccessCount: 0,
          storeSentFailCount: broadcastMessage.canSendStoreCount,
          status: BroadcastMessageStatus.Sent,
        });
        return { message: `Bulk message sending was successful!` };
      }
      if (totalStores < broadcastMessage.canSendStoreCount) {
        await this.broadcastMessageRepository.update(broadcastMessage.id, {
          storeSentFailCount: broadcastMessage.canSendStoreCount - totalStores,
        });
      }
      const headers = <Py3CommonRequestHeadersInterface>(<unknown>{
        'x-request-id': randomUUID(),
        'x-client-id': broadcastMessage.sendingBy,
        'x-client-type': ACCOUNT_TYPES.Admin,
      });
      if (broadcastMessage.importFilePath) {
        await this.publishBatchMessage(validStores, broadcastMessage, headers, broadcastData);
      } else {
        await this.processFullStore(broadcastMessage.id, headers, broadcastData);
      }

      return { message: `Bulk message sending was successful!` };
    } catch (err) {
      this.logger.log(
        `An error occurred while sending the message: ${err.message} with broadcastMessageId: ${broadcastMessage.id}`,
      );
      throw err;
    }
  }
  async processFullStore(
    broadcastMessageId: string,
    headers: Py3CommonRequestHeadersInterface,
    broadcastData: { content: string; attachments: AttachmentFileUploadType[] },
  ) {
    return new Promise(async (resolve, reject) => {
      const storesStream = await this.storeRepository
        .createQueryBuilder('store')
        .select(['store.id', 'store.name'])
        .where('store.status IN (:...statuses)', {
          statuses: [
            StoreStatus.NewlyApplied,
            StoreStatus.InReview,
            StoreStatus.Published,
            StoreStatus.OnHold,
            StoreStatus.Suspended,
          ],
        })
        .stream();

      let batch = [];
      let batchIndex = 0;

      storesStream.on('data', async (row: { store_id: string; store_name: string }) => {
        batch.push({ id: row.store_id, name: row.store_name });
        if (batch.length === CONCURRENT_MESSAGE_SIZE_BATCH) {
          storesStream.pause();
          await this.addDelayedJobToBullMQ(broadcastMessageId, batchIndex, batch, headers, broadcastData);
          batch = [];
          batchIndex++;
          storesStream.resume();
        }
      });
      storesStream.on('end', async () => {
        if (batch.length > 0) {
          await this.addDelayedJobToBullMQ(broadcastMessageId, batchIndex, batch, headers, broadcastData);
        }
        resolve(true);
      });
      storesStream.on('error', (error) => reject(error));
    });
  }
  /**
   * Publish messages in batches to the message queue.
   * @param validStores List of valid stores
   * @param broadcastMessage The BroadcastMessage entity
   * @param headers - Headers for the request
   * @param broadcastData - The broadcast message content and attachments
   */
  private async publishBatchMessage(
    validStores: StoresInfo[],
    broadcastMessage: BroadcastMessageEntity,
    headers: Py3CommonRequestHeadersInterface,
    broadcastData: { content: string; attachments: AttachmentFileUploadType[] },
  ): Promise<void> {
    const storeIdsLength = validStores.length;
    let batchIndex = 0;

    for (let i = 0; i < storeIdsLength; i += CONCURRENT_MESSAGE_SIZE_BATCH) {
      const batch = validStores.slice(i, i + CONCURRENT_MESSAGE_SIZE_BATCH);
      await this.addDelayedJobToBullMQ(broadcastMessage.id, batchIndex, batch, headers, broadcastData);
      batchIndex++;
    }
  }

  /**
   * Retrieve attachment files by relation ID (broadcastMessageId).
   * @param relationId The relationId to search attachments.
   * @returns An array of attachment file data.
   */
  async getAttachmentFilesByRelationId(relationId: string): Promise<AttachmentFileUploadType[]> {
    const attachments = await this.attachmentRepository.find({
      where: { relationId },
      select: ['id', 'path', 'attachmentType'],
    });

    return attachments;
  }

  /**
   * Get store IDs with status = true from the database.
   * @param storeIds List of store IDs to check.
   * @param attachmentFiles List of attachment files.
   * @returns A list of objects containing store IDs and names where status = true.
   */
  async getStoreStatusByIds(storeIds: string[]): Promise<StoresInfo[]> {
    try {
      if (storeIds.length < 1) {
        return [];
      }
      const stores = await this.storeRepository
        .createQueryBuilder('store')
        .select(['store.id', 'store.name'])
        .where('store.id IN (:...ids)', { ids: storeIds })
        .andWhere('store.status IN (:...statuses)', {
          statuses: [
            StoreStatus.NewlyApplied,
            StoreStatus.InReview,
            StoreStatus.Published,
            StoreStatus.OnHold,
            StoreStatus.Suspended,
          ],
        })
        .getMany();

      return stores;
    } catch (error) {
      this.logger.log('error when check status', error);
      return [];
    }
  }
  async getStoreLengthByStatus(storeIds: string[]): Promise<number> {
    try {
      if (storeIds.length < 1) {
        return 0;
      }
      const countStores = await this.storeRepository
        .createQueryBuilder('store')
        .select(['store.id', 'store.name'])
        .where('store.id IN (:...ids)', { ids: storeIds })
        .andWhere('store.status IN (:...statuses)', {
          statuses: [
            StoreStatus.NewlyApplied,
            StoreStatus.InReview,
            StoreStatus.Published,
            StoreStatus.OnHold,
            StoreStatus.Suspended,
          ],
        })
        .getCount();

      return countStores;
    } catch (error) {
      this.logger.log('error when check status', error);
      return 0;
    }
  }
  /**
   * Updates the success and failure counts and sets the broadcast message status to 'Sent'.
   *
   * @param broadcastMessageId - The ID of the broadcast message to update.
   * @param successCount - Number of successful sends.
   * @param failCount - Number of failed sends.
   */
  async updateSentCountsAndStatus(broadcastMessageId: string, successCount: number, failCount: number): Promise<void> {
    const broadcastMessage = await this.broadcastMessageRepository.findOne({
      where: { id: broadcastMessageId, status: BroadcastMessageStatus.Sending },
    });

    if (broadcastMessage) {
      await this.broadcastMessageRepository.update(broadcastMessageId, {
        storeSentSuccessCount: successCount,
        storeSentFailCount: (broadcastMessage.storeSentFailCount ?? 0) + failCount,
        status: BroadcastMessageStatus.Sent,
      });
    }
  }

  /**
   * Get all threads
   * @param dto
   * @param request
   */
  public async getAll(
    dto: BroadcastMessageQueryRequest,
    request: Request,
  ): Promise<{ listBroadcastMessages: any[]; pagination: any }> {
    const { search, pageNumber = 1, perPage = 10 } = dto;

    const queryBuilder = this.broadcastMessageRepository.createQueryBuilder('broadcastMessage');

    if (search) {
      const normalizeSpaces = (text: string) => text.trim();
      queryBuilder.where('broadcastMessage.title ILIKE :search', { search: `%${normalizeSpaces(search)}%` });
    }

    queryBuilder
      .orderBy('updated_at', 'DESC')
      .skip((pageNumber - 1) * perPage)
      .take(perPage);

    const [listBroadcastMessage, count] = await queryBuilder.getManyAndCount();

    const listBroadcastMessages = listBroadcastMessage.map((message) => {
      const fileName = message.csvFileName ? message.csvFileName : '';

      return {
        id: message.id,
        status: message.status,
        scheduledSendTime: message.scheduledSendTime || '',
        title: message.title,
        csvName: fileName ? `${fileName} (${message.canSendStoreCount}件)` : `${message.canSendStoreCount}件`,
        description: message.description,
      };
    });

    return {
      listBroadcastMessages,
      pagination: {
        perPage,
        pageNumber,
        total: count,
        request: { query: request.query, path: request.path },
      },
    };
  }

  /**
   * Get broadcast message by ID and return specific fields with additional logic for CSV and store list.
   * @param id: string
   * @param firstTime: boolean
   */
  public async getById(id: string) {
    try {
      const broadcastMessage = await this.findOneById(id, ['attachments']);

      if (!broadcastMessage) {
        throw new NotFoundException('Broadcast message not found');
      }

      // Extract the file name if the importFilePath exists
      const fileName = broadcastMessage.csvFileName || 'No CSV';

      let storeList: StoresInfo[];
      if (broadcastMessage.importFilePath) {
        // If importFilePath exists, get the first 30 store IDs from the CSV file
        const stream = await this.getStreamFromS3(broadcastMessage.importFilePath);
        const storeIds = await this.processPreviewStore(stream);
        storeList = storeIds.publicAndSuspendedStores;
      } else {
        // If no importFilePath, get the first 30 store IDs from the StoreEntity
        storeList = await this.storeRepository
          .createQueryBuilder('store')
          .select(['store.id', 'store.name'])
          .where('store.status IN (:...statuses)', {
            statuses: [
              StoreStatus.NewlyApplied,
              StoreStatus.InReview,
              StoreStatus.Published,
              StoreStatus.OnHold,
              StoreStatus.Suspended,
            ],
          })
          .limit(30)
          .getMany();
      }
      return {
        id: broadcastMessage.id,
        title: broadcastMessage.title,
        escapeUrlizeContent: broadcastMessage.content.escape().urlize(),
        content: broadcastMessage.content,
        status: broadcastMessage.status,
        description: broadcastMessage.description,
        createdBy: broadcastMessage.createdBy,
        attachments: broadcastMessage.attachments,
        canSendStoreCount: broadcastMessage.canSendStoreCount,
        cannotSendStoreCount: broadcastMessage.cannotSendStoreCount,
        csvName: fileName ? fileName : '',
        scheduledSendTime: broadcastMessage.scheduledSendTime,
        storeList, // The list of store IDs with status true
      };
    } catch (error) {
      this.logger.log({ error: error.message });
      throw error;
    }
  }

  /**
   * Updates the status of a broadcast message and schedules a message if the status is set to "sent".
   *
   * This method finds the broadcast message by its ID and updates its status. If the status is updated
   * to 5 (sent), it calculates the delay until the scheduled send time and queues a message to be sent.
   *
   * @param id - The ID of the broadcast message to update.
   * @param newStatus - The new status value to set (1-5).
   * @param user -
   * @returns A success message indicating that the status was updated.
   * @throws BadRequestException if the broadcast message is not found or the status is invalid.
   * @throws InternalServerErrorException if an error occurs during the status update or scheduling.
   */
  public async updateStatus(id: string, newStatus: number, user?: SessionAdmin): Promise<{ message: string }> {
    try {
      const broadcastMessage = await this.broadcastMessageRepository.findOne({
        where: {
          id,
          status: In([
            BroadcastMessageStatus.Draft,
            BroadcastMessageStatus.Unapproved,
            BroadcastMessageStatus.SendingScheduled,
          ]),
        },
      });
      if (!broadcastMessage) {
        throw new BadRequestException('Broadcast message not found');
      }
      if (![BroadcastMessageStatus.Unapproved, BroadcastMessageStatus.Approved].includes(newStatus)) {
        throw new BadRequestException('Invalid new status value. Valid values are 2, 3.');
      }
      if (
        broadcastMessage.status === BroadcastMessageStatus.Unapproved &&
        newStatus === BroadcastMessageStatus.Approved
      ) {
        broadcastMessage.approvedBy = user.id;
        broadcastMessage.approvedAt = new Date();
      }
      if (
        broadcastMessage.status === BroadcastMessageStatus.SendingScheduled &&
        newStatus === BroadcastMessageStatus.Approved
      ) {
        broadcastMessage.scheduledSendTime = null;
      }
      broadcastMessage.status = newStatus;
      await this.broadcastMessageRepository.update(id, {
        status: newStatus,
        approvedBy: broadcastMessage.approvedBy,
        approvedAt: broadcastMessage.approvedAt,
        scheduledSendTime: broadcastMessage.scheduledSendTime,
      });

      return { message: `Broadcast message status updated to ${newStatus}` };
    } catch (error) {
      this.logger.log({ error: error.message });
      throw error;
    }
  }

  /**
   * Save the scheduled send time for a broadcast message and update its status to 3.
   * Reject if the current status is not 2.
   * @param id - The ID of the broadcast message.
   * @param dto -
   * @returns Success message or error.
   */
  public async handleScheduleSendingMessage(
    id: string,
    dto: SaveTimeTemplateDto,
    user?: SessionAdmin,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const { dateTime, sendNow } = dto;
      // Find the broadcast message by ID
      const broadcastMessage = await this.findOneById(id);

      if (!broadcastMessage) {
        throw new BadRequestException('Broadcast message not found');
      }

      // Check if the current status is not 3 (Approved)
      if (broadcastMessage.status !== BroadcastMessageStatus.Approved) {
        throw new BadRequestException('Cannot update scheduled time. Status must be 3 (Approved).');
      }

      if (sendNow) {
        const checkTimeOverride = await this.checkTime(new Date());
        if (checkTimeOverride) {
          return { success: false, message: 'Cannot set time for message have schedule nearby!' };
        }
        // Save the updated broadcast message
        broadcastMessage.scheduledSendTime = new Date();
        broadcastMessage.sendingBy = user.id;
        broadcastMessage.status = BroadcastMessageStatus.Sending;
        await this.broadcastMessageRepository.update(id, {
          scheduledSendTime: broadcastMessage.scheduledSendTime,
          sendingBy: broadcastMessage.sendingBy,
          status: BroadcastMessageStatus.Sending,
        });
        // Publish the message to RabbitMQ with the consulting-message routing key
        await this.amqpConnection.publish('taskManager', CONCURRENT_MESSAGE_MQ_NAME, {
          event: RabbitMQEventType.ScheduledBroadcastMessage,
          content: { broadcastMessageId: broadcastMessage.id },
        });
        // await this.sendSlackNotification(broadcastMessage);
        return { success: true, message: 'Scheduled sended.' };
      }
      const checkTimeOverride = await this.checkTime(dateTime);
      if (checkTimeOverride) {
        return { success: false, message: 'Cannot set time for message have schedule nearby!' };
      }
      if (dateTime < new Date()) {
        return { success: false, message: 'Cannot set time in history!' };
      }
      // Update the scheduled_send_time and status
      broadcastMessage.scheduledSendTime = dateTime;
      broadcastMessage.status = BroadcastMessageStatus.SendingScheduled;
      broadcastMessage.sendingBy = user.id;
      await this.broadcastMessageRepository.update(id, {
        scheduledSendTime: broadcastMessage.scheduledSendTime,
        status: BroadcastMessageStatus.SendingScheduled,
        sendingBy: broadcastMessage.sendingBy,
      });
      // await this.sendSlackNotification(broadcastMessage);

      return { success: true, message: 'Scheduled send time saved and status updated to 3 (Approved).' };
    } catch (error) {
      this.logger.log({ error: error.message });
      throw error;
    }
  }

  /**
   * Saves or updates a broadcast message along with its related CSV file and attachments.
   *
   * This method handles both creation and update logic for a broadcast message. If `id` is provided,
   * it attempts to update the existing message; otherwise, it creates a new one. It also uploads
   * a CSV file and any additional attachments if provided.
   *
   * @param body - The DTO containing details of the broadcast message.
   * @param adminId - The ID of the admin performing the action.
   * @param csvFile - (Optional) The CSV file to attach to the broadcast message.
   * @param attachments - (Optional) Additional attachments to be associated with the message.
   * @returns The saved or updated broadcast message with its attachments.
   * @throws BadRequestException if the message is not found or if it cannot be updated.
   * @throws InternalServerErrorException if an error occurs during the process.
   */
  public async save(
    body: SaveTemplateDto,
    adminId: string,
    csvFile?: Express.Multer.File,
    attachments?: Express.Multer.File[],
  ) {
    const { selectAllStores } = body;
    let { title, content, description } = body;
    try {
      let broadcastMessage: BroadcastMessageEntity;
      title = this.cleanString(title);
      content = this.cleanString(content);
      description = this.cleanString(description);
      if (content && content.length > 2000) {
        return HttpResponseFormatter.responseBadRequest({ errors: 'Please enter 2000 characters or less.' });
      }
      if (title && title.length > 200) {
        return HttpResponseFormatter.responseBadRequest({ errors: 'Please enter 200 characters or less.' });
      }
      if (description && description.length > 200) {
        return HttpResponseFormatter.responseBadRequest({ errors: 'Please enter 200 characters or less.' });
      }
      const result = await this.dataSource.transaction(async (entityManager: EntityManager) => {
        // Create new broadcast message
        broadcastMessage = entityManager.create(BroadcastMessageEntity, {
          id: uuidv4(),
          title,
          content,
          description,
          status: BroadcastMessageStatus.Draft,
          createdBy: adminId,
        });
        if (selectAllStores === true) {
          const totalStores = await this.storeRepository
            .createQueryBuilder('store')
            .where('store.status IN (:...statuses)', {
              statuses: [
                StoreStatus.NewlyApplied,
                StoreStatus.InReview,
                StoreStatus.Published,
                StoreStatus.OnHold,
                StoreStatus.Suspended,
              ],
            })
            .getCount();
          broadcastMessage.canSendStoreCount = totalStores;
          broadcastMessage.cannotSendStoreCount = 0;
        } else if (csvFile) {
          const csvUploadResponse = await this.attachmentService.uploadFile(
            csvFile,
            broadcastMessage.id,
            CONCURRENT_MESSAGE_FOLDER_NAME,
          );
          broadcastMessage.csvFileName = Buffer.from(csvFile.originalname, 'latin1').toString('utf8');
          broadcastMessage.importFilePath = csvUploadResponse.path;
        }

        await entityManager.insert(BroadcastMessageEntity, broadcastMessage);
        // Handle additional attachments if any
        if (attachments && attachments.length > 0) {
          const uploadResponses = await Promise.all(
            attachments.map((file) =>
              this.attachmentService.uploadFile(file, broadcastMessage.id, broadcastMessage.id),
            ),
          );

          const newAttachments = entityManager.create(AttachmentEntity, uploadResponses);
          await entityManager.insert(AttachmentEntity, newAttachments);
        }

        // Fetch the saved or updated broadcast message with attachments
        const savedBroadcastMessage = await entityManager
          .createQueryBuilder(BroadcastMessageEntity, 'broadcast')
          .leftJoinAndSelect('broadcast.attachments', 'attachments')
          .where('broadcast.id = :id', { id: broadcastMessage.id })
          .getOne();

        return {
          success: true,
          broadcastMessage: savedBroadcastMessage,
        };
      });

      if (broadcastMessage.importFilePath) {
        this.amqpConnection.publish('taskManager', CONCURRENT_MESSAGE_MQ_NAME, {
          event: RabbitMQEventType.HandleCSVUpload,
          content: { broadcastMessageId: broadcastMessage.id },
        });
      }

      return result;
    } catch (error) {
      this.logger.log({ error: error.message });
      throw error;
    }
  }

  /**
   * Saves or updates a broadcast message along with its related CSV file and attachments.
   *
   * This method handles both creation and update logic for a broadcast message. If `id` is provided,
   * it attempts to update the existing message; otherwise, it creates a new one. It also uploads
   * a CSV file and any additional attachments if provided.
   *
   * @param body - The DTO containing details of the broadcast message.
   * @param adminId - The ID of the admin performing the action.
   * @param csvFile - (Optional) The CSV file to attach to the broadcast message.
   * @param attachments - (Optional) Additional attachments to be associated with the message.
   * @returns The saved or updated broadcast message with its attachments.
   * @throws BadRequestException if the message is not found or if it cannot be updated.
   * @throws InternalServerErrorException if an error occurs during the process.
   */
  public async update(
    body: SaveTemplateDto,
    id: string,
    adminId: string,
    csvFile?: Express.Multer.File,
    attachments?: Express.Multer.File[],
  ) {
    const { selectAllStores, removedFiles } = body;
    let { title, content, description } = body;
    if (!id) {
      throw new BadRequestException('Cannot update with empty id');
    }
    try {
      let broadcastMessage: BroadcastMessageEntity;
      title = this.cleanString(title);
      content = this.cleanString(content);
      description = this.cleanString(description);
      if (content && content.length > 2000) {
        return HttpResponseFormatter.responseBadRequest({ errors: 'Please enter 2000 characters or less.' });
      }
      if (title && title.length > 200) {
        return HttpResponseFormatter.responseBadRequest({ errors: 'Please enter 200 characters or less.' });
      }
      if (description && description.length > 200) {
        return HttpResponseFormatter.responseBadRequest({ errors: 'Please enter 200 characters or less.' });
      }
      const result = await this.dataSource.transaction(async (entityManager: EntityManager) => {
        broadcastMessage = await entityManager.findOneOrFail(BroadcastMessageEntity, {
          where: {
            id,
            createdBy: adminId,
            status: In([BroadcastMessageStatus.Draft, BroadcastMessageStatus.Unapproved]),
          },
        });

        broadcastMessage.title = title;
        broadcastMessage.content = content;
        broadcastMessage.description = description;

        await entityManager.update(BroadcastMessageEntity, broadcastMessage.id, {
          title: title,
          content: content,
          description: description,
        });

        if (csvFile) {
          const csvUploadResponse = await this.attachmentService.uploadFile(
            csvFile,
            broadcastMessage.id,
            CONCURRENT_MESSAGE_FOLDER_NAME,
          );
          broadcastMessage.csvFileName = csvFile.originalname;
          broadcastMessage.importFilePath = csvUploadResponse.path;
          // Publish the message to RabbitMQ with the consulting-message routing key

          await entityManager.update(BroadcastMessageEntity, broadcastMessage.id, {
            csvFileName: (broadcastMessage.csvFileName = Buffer.from(csvFile.originalname, 'latin1').toString('utf8')),
            importFilePath: csvUploadResponse.path,
            canSendStoreCount: null,
            cannotSendStoreCount: null,
          }); // Update the broadcast message with the CSV path
        }

        if (selectAllStores === true) {
          broadcastMessage.importFilePath = null;
          broadcastMessage.csvFileName = null;
          const totalStores = await this.storeRepository
            .createQueryBuilder('store')
            .where('store.status IN (:...statuses)', {
              statuses: [
                StoreStatus.NewlyApplied,
                StoreStatus.InReview,
                StoreStatus.Published,
                StoreStatus.OnHold,
                StoreStatus.Suspended,
              ],
            })
            .getCount();
          broadcastMessage.canSendStoreCount = totalStores;
          broadcastMessage.cannotSendStoreCount = 0;
          await entityManager.update(BroadcastMessageEntity, broadcastMessage.id, {
            importFilePath: null,
            csvFileName: null,
            canSendStoreCount: totalStores,
            cannotSendStoreCount: 0,
          }); // Update the broadcast message with the CSV path
        }
        if (removedFiles?.length) {
          // Find the attachments that match the removed file IDs
          await entityManager.delete(AttachmentEntity, {
            id: In(removedFiles),
          });
        }
        // Handle additional attachments if any
        if (attachments?.length) {
          const uploadResponses = await Promise.all(
            attachments.map((file) =>
              this.attachmentService.uploadFile(file, broadcastMessage.id, broadcastMessage.id),
            ),
          );

          const newAttachments = entityManager.create(AttachmentEntity, uploadResponses);
          await entityManager.insert(AttachmentEntity, newAttachments);
        }

        // Fetch the saved or updated broadcast message with attachments
        const savedBroadcastMessage = await entityManager
          .createQueryBuilder(BroadcastMessageEntity, 'broadcast')
          .leftJoinAndSelect('broadcast.attachments', 'attachments')
          .where('broadcast.id = :id', { id: broadcastMessage.id })
          .getOne();

        return {
          success: true,
          broadcastMessage: savedBroadcastMessage,
        };
      });

      if (broadcastMessage.importFilePath) {
        this.amqpConnection.publish('taskManager', CONCURRENT_MESSAGE_MQ_NAME, {
          event: RabbitMQEventType.HandleCSVUpload,
          content: { broadcastMessageId: broadcastMessage.id },
        });
      }

      return result;
    } catch (error) {
      this.logger.log({ error: error.message });
      throw error;
    }
  }

  /**
   * Deletes a broadcast message and its related attachments by ID.
   *
   * This method finds the broadcast message by its ID, verifies its existence and
   * status, and then deletes the message along with its attachments in a transactional process.
   *
   * @param id - The ID of the broadcast message to delete.
   * @returns A success message if the deletion is completed.
   * @throws BadRequestException if the message is not found or if its status is "sent".
   * @throws InternalServerErrorException if an error occurs during deletion.
   */
  public async deleteBroadcastMessageAndAttachments(id: string): Promise<{ message: string }> {
    try {
      // Find the broadcast message by ID
      const broadcastMessage = await this.findOneById(id);

      if (!broadcastMessage) {
        throw new BadRequestException('Broadcast message not found');
      }

      if (
        broadcastMessage.status === BroadcastMessageStatus.Sent ||
        broadcastMessage.status === BroadcastMessageStatus.Sending
      ) {
        throw new BadRequestException('Cannot delete broadcast message with status "送信済み" (sent)');
      }

      await this.dataSource.transaction(async (entityManager: EntityManager) => {
        // Delete the attachments
        await entityManager.delete(AttachmentEntity, { relationId: id });

        // Delete the broadcast message
        await entityManager.delete(BroadcastMessageEntity, { id });
      });

      return { message: 'Broadcast message and related attachments deleted successfully' };
    } catch (error) {
      this.logger.log({ error: error.message });
      throw error;
    }
  }

  /**
   * Get one BroadcastMessageEntity
   * @param broadcastMessageId
   */
  async findOneById(broadcastMessageId: string, relations?: string[]): Promise<BroadcastMessageEntity> {
    return await this.broadcastMessageRepository.findOne({
      where: {
        id: broadcastMessageId,
      },
      ...(relations && { relations }),
    });
  }
  async findOneWithSelectedField(broadcastMessageId: string): Promise<BroadcastMessageEntity> {
    return await this.broadcastMessageRepository.findOne({
      where: {
        id: broadcastMessageId,
      },
      select: ['id', 'importFilePath', 'canSendStoreCount', 'content', 'sendingBy'],
    });
  }

  async sendSlackNotification(broadcastMessage: BroadcastMessageEntity) {
    const message = `新しい一斉送信を設定しました。
    ・タイトル:${broadcastMessage.title}
    ・送信時間: ${formatDateWithJapaneseWeekday(broadcastMessage.scheduledSendTime, JP_YYYYMD_HHMM_TZ)}
    ・URL: ${process.env.TERRY_URL}/admin/consulting-message/broadcast-message/${broadcastMessage.id}/preview`;
    return this.slackService.sendSlackNotification(message);
  }

  /**
   * Checks if there are any broadcast messages scheduled within one hour before or after the current time.
   * @param now - The current date and time to check against.
   */
  async checkTime(now: Date): Promise<boolean> {
    // Define start and end time as one hour before and after the current time
    const startTime = new Date(now);
    startTime.setMinutes(now.getMinutes() - 59); // 59 minutes before current time

    const endTime = new Date(now);
    endTime.setMinutes(now.getMinutes() + 59); // 59 minutes after current ti

    // Query the database for messages scheduled within this time range
    const messagesAroundNow = await this.broadcastMessageRepository.find({
      where: {
        scheduledSendTime: Between(startTime, endTime), // Use Between for range queries
      },
    });

    return messagesAroundNow.length > 0;
  }

  /**
   * Converts a Buffer into a readable stream.
   *
   * @param buffer - The Buffer to convert.
   * @returns A readable stream of the buffer's data.
   * @throws InternalServerErrorException if an error occurs.
   */
  bufferToStream(buffer: Buffer) {
    try {
      return Readable.from(buffer);
    } catch (error) {
      this.logger.log({ error: error.message });
      throw error;
    }
  }
  cleanString(str: string) {
    return str
      .replace(/\r\n/g, '\n')
      .replace(/[\u200B-\u200D\uFEFF]/g, '')
      .trim();
  }

  /**
   * Adds a delayed job to BullMQ for message broadcasting.
   * @param broadcastMessageId - The ID of the broadcast message.
   * @param batchIndex - The index of the batch.
   * @param batch - The batch of stores to be sent.
   * @param headers - The request headers.
   * @param broadcastData - The broadcast message content and attachments.
   */
  private async addDelayedJobToBullMQ(
    broadcastMessageId: string,
    batchIndex: number,
    batch: StoresInfo[],
    headers: Py3CommonRequestHeadersInterface,
    broadcastData: { content: string; attachments: AttachmentFileUploadType[] },
  ): Promise<void> {
    const delayMs = batchIndex * CONCURRENT_MESSAGE_MINUTE_DELAY;

    const jobData = {
      batch,
      broadcastMessageId,
      headers,
      batchIndex,
      broadcastData,
    };

    const jobOptions = {
      delay: delayMs,
      jobId: `${broadcastMessageId}-batch-${batchIndex}`, // Unique job ID to prevent duplicates
    };

    await this.messageBroadcastQueue.add(BULLMQ_JOB_SEND_MESSAGE_BATCH, jobData, jobOptions);

    this.logger.log(
      `[BullMQ] Batch ${batchIndex} added to queue for broadcast ${broadcastMessageId} with ${delayMs}ms delay`,
    );
  }
}
