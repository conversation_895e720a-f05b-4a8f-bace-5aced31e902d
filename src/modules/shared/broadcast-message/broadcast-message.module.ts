import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BroadcastMessageService } from '@modules/shared/broadcast-message/broadcast-message.service';
import { StoreEntity } from '@src/entities/store.entity';
import { AttachmentEntity } from '@src/entities';
import { QueueProvider } from '@providers/queue.provider';
import { BullMQProvider } from '@providers/bullmq.provider';
import { BroadcastMessageEntity } from '@src/entities/broadcast-message.entity';
import { AttachmentModule } from '../attachment/attachment.module';
import { HttpModule } from '@nestjs/axios';
import { NotificationModule } from '../notification/notification.module';
import { BULLMQ_MESSAGE_BROADCAST_QUEUE } from '@src/common/constants';

@Module({
  exports: [BroadcastMessageService],
  providers: [BroadcastMessageService],
  imports: [
    QueueProvider.register(),
    BullMQProvider.register(),
    BullMQProvider.forFeature(BULLMQ_MESSAGE_BROADCAST_QUEUE),
    TypeOrmModule.forFeature([StoreEntity, AttachmentEntity, BroadcastMessageEntity]),
    AttachmentModule,
    HttpModule,
    NotificationModule,
  ],
})
export class BroadcastMessageModule {}
