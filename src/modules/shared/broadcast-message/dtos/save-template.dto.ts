import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';
import { Transform } from 'class-transformer';

export class SaveTemplateDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.invalidUUID') })
  id?: string;
  @ApiProperty()
  @IsNotEmpty({ message: i18nValidationMessage('validation.isNotEmpty') })
  title: string;
  @ApiProperty()
  @IsNotEmpty({ message: i18nValidationMessage('validation.isNotEmpty') })
  content: string;
  @ApiProperty()
  @IsNotEmpty({ message: i18nValidationMessage('validation.isNotEmpty') })
  description: string;
  @ApiProperty()
  @Transform(({ value }) => value === 'true' || value === true) // Convert string 'true' to boolean true
  selectAllStores: boolean;
  @ApiProperty()
  removedFiles?: [string];
}
