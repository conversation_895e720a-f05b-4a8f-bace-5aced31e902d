import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { catchError, of } from 'rxjs';
import { NotificationConfig } from '@src/configs/notification';

@Injectable()
export class SlackService {
  private readonly logger = new Logger(SlackService.name);

  constructor(private readonly configService: ConfigService, private readonly httpService: HttpService) {}

  async sendSlackNotification(message: string) {
    try {
      const notificationConfig = this.configService.get<NotificationConfig>('notification');
      this.httpService
        .post(notificationConfig.slackWebhookUrl, {
          channel: '#general',
          text: message,
        })
        .pipe(
          catchError((err) => {
            this.logger.error(err.message, err.stack);
            return of(err.message);
          }),
        )
        .subscribe();
    } catch (error) {
      console.error('error', error);
    }
  }
}
