import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { Injectable, Logger } from '@nestjs/common';
import { ThreadType } from '@src/common/enums/thread-type';
import { routes } from '@src/common/routes';
import { PushNotificationDataType } from '@src/common/types/push-notification-data.type';
import { getThreadTypeKeyById } from '@src/common/utils/helper';
import { ACCOUNT_TYPES } from '@vietnam/cnga-middleware';
import { Request } from 'express';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  constructor(private readonly amqpConnection: AmqpConnection) {}

  private genURLPushNotiNewMessage(threadType: ThreadType, broadcastId?: string) {
    const path = routes.store.message.mobileDetail.replace(':type', getThreadTypeKeyById(threadType));
    if (!broadcastId) {
      return path;
    }
    const searchParams = new URLSearchParams({
      type: 'push',
      broadcastId,
    });
    return `${path}?${searchParams.toString()}`;
  }

  async pushNotiNewMessageToStore(data: PushNotificationDataType, request: Request) {
    try {
      return await this.amqpConnection.publish(
        'taskManager',
        Array.isArray(data.storeId) ? 'notification-bulk-sns' : 'notification',
        {
          task: 'PushNotification',
          content: {
            clientId: data.storeId,
            message: '新着メッセージがあるよ！',
            sound: 'sound1.aiff',
            data: {
              type: 'consulting_message',
              url: this.genURLPushNotiNewMessage(data.threadType, data.broadcastId),
            },
          },
        },
        { headers: this.getNotificationHeaders(request) },
      );
    } catch (err) {
      this.logger.log(`Failed to send notification for store: ${data.storeId}`);
    }
  }

  /**
   * @param request
   * @private
   */
  getNotificationHeaders(request: Request) {
    return {
      'x-request-id': request.headers['x-request-id'],
      'x-client-id': request?.terrySession?.adminId,
      'x-client-type': ACCOUNT_TYPES.Admin,
    };
  }
}
