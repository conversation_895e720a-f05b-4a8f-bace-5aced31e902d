import { NotificationService } from '@modules/shared/notification/notification.service';
import { Module } from '@nestjs/common';
import { QueueProvider } from '@src/providers/queue.provider';
import { SlackService } from './slack.service';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [QueueProvider.register(), HttpModule],
  providers: [NotificationService, SlackService],
  exports: [NotificationService, SlackService],
})
export class NotificationModule {}
