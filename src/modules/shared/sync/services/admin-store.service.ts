import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminsStoresEntity } from '@src/entities/admins-stores.entity';

@Injectable()
export class AdminStoreService {
  constructor(
    @InjectRepository(AdminsStoresEntity) private readonly adminsStoresEntity: Repository<AdminsStoresEntity>,
  ) {}

  public async save(adminsStoresEntity: AdminsStoresEntity): Promise<AdminsStoresEntity> {
    return await this.adminsStoresEntity.save(adminsStoresEntity);
  }

  public async getByFields(storeId: string): Promise<AdminsStoresEntity> {
    return await this.adminsStoresEntity.findOne({
      where: {
        storeId,
      },
    });
  }
}
