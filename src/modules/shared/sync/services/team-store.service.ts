import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TeamsStoresEntity } from '@src/entities/teams-stores.entity';

@Injectable()
export class TeamStoreService {
  constructor(@InjectRepository(TeamsStoresEntity) private readonly teamsStoresEntity: Repository<TeamsStoresEntity>) {}

  public async save(TeamsStoresEntity: TeamsStoresEntity): Promise<TeamsStoresEntity> {
    return await this.teamsStoresEntity.save(TeamsStoresEntity);
  }

  public async getByFields(storeId: string): Promise<TeamsStoresEntity> {
    return await this.teamsStoresEntity.findOne({
      where: {
        storeId,
      },
    });
  }
}
