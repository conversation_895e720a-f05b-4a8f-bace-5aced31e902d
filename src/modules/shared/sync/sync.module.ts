import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminStoreService } from '@src/modules/shared/sync/services/admin-store.service';
import { TeamStoreService } from '@src/modules/shared/sync/services/team-store.service';
import { AdminsStoresEntity, TeamsStoresEntity } from '@src/entities';

@Module({
  imports: [TypeOrmModule.forFeature([AdminsStoresEntity, TeamsStoresEntity])],
  providers: [AdminStoreService, TeamStoreService],
  exports: [AdminStoreService, TeamStoreService],
})
@Global()
export class SyncModule {}
