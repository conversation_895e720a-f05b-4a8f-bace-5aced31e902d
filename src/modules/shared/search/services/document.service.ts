import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InquiryThreadOpenSearchQuery, OpenSearchResponse } from '@common/interface/open-search.interface';
import { CuramaOpenSearchClient } from '@vietnam/curama-opensearch';
import { CURAMA_SEARCH_INDEX_NAME } from '@common/constants/search';
import { FilterField } from '@modules/shared/search/type/filter-field';

/**
 * This service provides methods for interacting with documents in OpenSearch.
 * DocumentService class
 */
@Injectable()
export class DocumentService implements OnModuleInit {
  /**
   * Logger instance
   * @private
   */
  private readonly logger: Logger = new Logger(DocumentService.name);

  /**
   * Constructor for the DocumentService class
   * @param openSearchClient
   */
  constructor(private readonly openSearchClient: CuramaOpenSearchClient) {}

  /**
   * Lifecycle hook, called once the module is initialized
   */
  async onModuleInit() {
    try {
      console.log('onModuleInit: ' + CURAMA_SEARCH_INDEX_NAME);
      const exist = await this.existsIndex(CURAMA_SEARCH_INDEX_NAME);
      if (!exist) {
        this.logger.log(`Index ${CURAMA_SEARCH_INDEX_NAME} not found. Creating...`);
        await this.createIndex(CURAMA_SEARCH_INDEX_NAME);
      }
    } catch (error) {
      this.logger.log(`Index creation error: ${error.message}`);
    }
  }

  /**
   * Creates an index in OpenSearch
   * @param indexName
   */
  async createIndex(indexName: string) {
    this.logger.log(`Creating index ${indexName}...`);
    const indexSetting = await import(`../index-mappings/curama_consulting_message.json`);
    try {
      await this.openSearchClient.indices.create({
        index: indexName,
        body: indexSetting,
      });
      this.logger.log(`Index ${indexName} created.`);
    } catch (e) {
      throw e;
    }
  }

  /**
   * Retrieves the mapping of an index
   * @param index - The index to retrieve mapping from
   * @param queryBody - The query body to use for the search
   */
  async queryIndex(
    index: string,
    queryBody: any,
  ): Promise<OpenSearchResponse<InquiryThreadOpenSearchQuery[]> | boolean> {
    if (!(await this.existsIndex(index))) {
      return false;
    }
    try {
      const result = await this.openSearchClient.search({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: queryBody,
      });
      return result.body as OpenSearchResponse<InquiryThreadOpenSearchQuery[]>;
    } catch (err) {
      this.logger.log(`Search error: ${err.message}`);
      return false;
    }
  }

  /**
   * Deletes documents from OpenSearch based on the threadIds
   * @param conditions - The conditions to filter documents by
   * @return {Promise<boolean>} - A promise resolving to true if the operation is successful
   */
  async deleteDocuments(conditions: any): Promise<boolean> {
    try {
      const result = await this.openSearchClient.deleteByQuery({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: {
          query: {
            terms: conditions,
          },
        },
      });

      if (result.body.errors) {
        this.logger.log('Bulk operation encountered errors:', JSON.stringify(result.body.items));
        return false;
      }

      return true;
    } catch (error) {
      this.logger.log('Error deleting documents from OpenSearch by threadIds:', error);
      return false;
    }
  }

  /**
   * Deletes a single document from OpenSearch by a specific field and value
   * @param field - The field to filter the document
   * @param value - The value of the field to match
   * @return {Promise<boolean>} - A promise resolving to true if the operation is successful
   */
  async deleteDocumentByField(field: string, value: string): Promise<boolean> {
    try {
      const result = await this.openSearchClient.deleteByQuery({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: {
          query: {
            term: {
              [field]: value,
            },
          },
        },
      });

      if (result.body.errors) {
        this.logger.log(`Error deleting document with ${field}: ${value}`, JSON.stringify(result.body.failures));
        return false;
      }

      this.logger.log(`Successfully deleted document with ${field}: ${value}`);
      return true;
    } catch (error) {
      this.logger.log(`Error deleting document with ${field}: ${value}`, error);
      return false;
    }
  }

  /**
   * Creates documents in OpenSearch using bulk operation
   * @param documents - Array of InquiryThreadOpenSearchQuery documents
   * @return {Promise<void>} - A promise resolving when the operation is complete
   */
  async createDocuments(documents: InquiryThreadOpenSearchQuery[]): Promise<boolean> {
    if (!documents || documents.length === 0) {
      return true;
    }

    const result = await this.openSearchClient.bulk({
      body: documents.flatMap((document) => [{ index: { _index: CURAMA_SEARCH_INDEX_NAME } }, document]),
    });

    if (result.body.errors) {
      this.logger.log('Bulk operation encountered errors:', JSON.stringify(result.body.items));
      return false;
    }

    return true;
  }

  /**
   * Creates a single document in OpenSearch
   * @param document - The InquiryThreadOpenSearchQuery document to be created
   * @return {Promise<boolean>} - A promise resolving to true if the operation is successful, otherwise false
   */
  async createDocument(document: InquiryThreadOpenSearchQuery): Promise<boolean> {
    try {
      const result = await this.openSearchClient.index({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: document,
      });

      if (result.body.result !== 'created') {
        this.logger.log('Failed to create document:', JSON.stringify(result.body));
        return false;
      }

      return true;
    } catch (error) {
      this.logger.log('Error creating document:', error);
      return false;
    }
  }

  /**
   * Retrieves the count of documents in an index
   * @param index - The index to count documents in
   * @return {Promise<number>} - A promise that resolves with the count of documents
   */
  async countDocuments(index: string): Promise<number> {
    if (!(await this.existsIndex(index))) {
      return 0;
    }
    try {
      const response = await this.openSearchClient.count({ index });
      this.logger.log(`Document count in index ${index}: ${response.body.count}`);
      return response.body.count;
    } catch (err) {
      return 0;
    }
  }

  /**
   * Checks if an index exists in OpenSearch
   * @param index - The index to check
   * @return {Promise<boolean>} - A promise that resolves with a boolean indicating if the index exists
   */
  async existsIndex(index: string): Promise<boolean> {
    try {
      const exist = await this.openSearchClient.indices.exists({
        index: index,
      });
      return exist.body;
    } catch (e) {
      this.logger.log(e.message, { response: e.meta });
      throw e;
    }
  }

  /**
   * Performs upsert operation on multiple documents in OpenSearch.
   * Each document will either be updated if it exists or inserted if it does not.
   *
   * @param field - The field to check for document existence (e.g., 'team_id', 'admin_id', 'store_id', or 'thread_id').
   * @param documents - An array of documents containing data for upsert operation.
   * @returns {Promise<boolean>} - A promise resolving to true if all operations succeed, false otherwise.
   */
  async updateMultipleDocuments(field: FilterField, documents: InquiryThreadOpenSearchQuery[]): Promise<boolean> {
    if (!documents || documents.length === 0) {
      this.logger.log('No documents provided for upsert.');
      return true;
    }

    try {
      const existingDocs = await this.getExistingDocuments(field, documents);

      const documentsToUpdate = existingDocs
        .map((existing) => {
          const match = documents.find((doc) => doc[field] === existing[field]);
          return match ? { _id: existing._id, doc: match } : null;
        })
        .filter((doc) => doc !== null);

      return await this.bulkUpdateDocuments(documentsToUpdate);
    } catch (error) {
      this.logger.log('Error in upsert operation for multiple documents:', error);
      return false;
    }
  }

  /**
   * Queries existing documents in OpenSearch and maps their field values to IDs.
   * @param field - The field to query by (e.g., thread_id, store_id).
   * @param documents - The list of documents to check for existing entries.
   * @returns A array containing mappings of field values to document IDs.
   */
  async getExistingDocuments(
    field: FilterField,
    documents: InquiryThreadOpenSearchQuery[],
  ): Promise<Array<{ _id: string; [key: string]: string }>> {
    const documentIds = [...new Set(documents.map((doc) => doc[field]))];

    const existingDocsResponse = <OpenSearchResponse<InquiryThreadOpenSearchQuery[]>>await this.queryIndex(
      CURAMA_SEARCH_INDEX_NAME,
      {
        query: { terms: { [field]: documentIds } },
        _source: [field],
        size: 10000,
      },
    );

    if (!existingDocsResponse || existingDocsResponse.hits.total.value === 0) {
      this.logger.log('No documents found to update. Proceeding with inserts only.');
      return [];
    }

    return existingDocsResponse.hits.hits.map((hit: any) => ({
      _id: hit._id,
      ...hit._source,
    }));
  }

  /**
   * Performs bulk update of documents in OpenSearch.
   * @param documentsToUpdate - The list of documents to update, including their IDs.
   * @returns A boolean indicating success or failure of the update operation.
   */
  async bulkUpdateDocuments(documentsToUpdate: { _id: string; doc: InquiryThreadOpenSearchQuery }[]): Promise<boolean> {
    if (documentsToUpdate.length === 0) {
      return true;
    }

    const updateBody = documentsToUpdate.flatMap(({ _id, doc }) => [
      { update: { _index: CURAMA_SEARCH_INDEX_NAME, _id } },
      { doc },
    ]);

    const updateResult = await this.openSearchClient.bulk({ body: updateBody });
    if (updateResult.body.errors) {
      const failedUpdates = updateResult.body.items.filter((item: any) => item.update?.error);
      this.logger.log('Error updating documents:', JSON.stringify(failedUpdates));
      return false;
    }

    this.logger.log(`Updated ${documentsToUpdate.length} documents in OpenSearch.`);
    return true;
  }

  /**
   * Updates documents in an OpenSearch index based on given conditions.
   *
   * @param {Object} condition - Key-value pairs for filtering documents. Example: { field1: 'value1' }.
   * @param {Object} updates - Key-value pairs of fields and new values to update. Example: { field2: 'newValue' }.
   * @returns {Promise<any>} - The result of the update operation.
   *
   * @example
   * await updateDocumentsByCondition('my-index', { field1: 'value1' }, { field2: 'newValue' });
   */
  async updateDocumentsByCondition(
    condition: Partial<Record<FilterField, string | string[]>>,
    updates: Partial<InquiryThreadOpenSearchQuery>,
  ): Promise<boolean> {
    try {
      const query = {
        bool: {
          must: Object.entries(condition).map(([field, value]) => {
            if (Array.isArray(value)) {
              return { terms: { [field]: value } };
            } else {
              return { term: { [field]: value } };
            }
          }),
        },
      };

      const scriptSource = Object.entries(updates)
        .map(([field, value]) => `ctx._source.${field} = params.${field};`)
        .join('\n');

      const params = updates;

      const body = {
        script: {
          source: scriptSource,
          params,
        },
        query,
      };

      const result = await this.openSearchClient.updateByQuery({
        index: CURAMA_SEARCH_INDEX_NAME,
        body,
      });

      if (result.body.errors) {
        this.logger.log('Update by condition failed', { errors: result.body.failures });
        return false;
      }

      this.logger.log(`Successfully updated documents in OpenSearch by condition: ${JSON.stringify(condition)}`);

      return true;
    } catch (error) {
      this.logger.log('Error in updateDocumentsByCondition', { error });
      return false;
    }
  }
}
