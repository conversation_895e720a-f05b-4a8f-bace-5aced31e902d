import { Injectable, Logger } from '@nestjs/common';
import { InquiryThreadOpenSearchQuery } from '@common/interface/open-search.interface';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import {
  AdminsStoresEntity,
  AdminsTeamsEntity,
  InquiryThreadEntity,
  MessageEntity,
  QualityAdminsStoresEntity,
  TeamEntity,
  TeamsStoresEntity,
} from '@src/entities';
import { ThreadType } from '@common/enums/thread-type';
import { DocumentService } from '@modules/shared/search/services/document.service';

/**
 * Sync data service for handling data synchronization between PostgreSQL and OpenSearch.
 * SearchService
 */
@Injectable()
export class SyncDataService {
  /**
   * Logger instance
   * @private
   */
  private readonly logger: Logger = new Logger(SyncDataService.name);

  /**
   * Database schema
   * @private
   */
  private readonly schema: string = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

  /**
   * Constructor for the SyncDataService class
   * @param inquiryThreadRepository
   * @param messageRepository
   * @param dataSource
   * @param documentService
   */
  constructor(
    @InjectRepository(InquiryThreadEntity)
    private readonly inquiryThreadRepository: Repository<InquiryThreadEntity>,
    @InjectRepository(MessageEntity)
    private readonly messageRepository: Repository<MessageEntity>,
    private readonly dataSource: DataSource,
    private readonly documentService: DocumentService,
  ) {}

  /**
   * Synchronizes assignment changes (admin, team, quality admin) for a list of stores.
   *
   * - For each store ID in the list, retrieves or upserts the associated thread data.
   * - Ensures all threads reflect the latest assignments based on current store configurations.
   * - If the list is empty, the function exits early and returns `true`.
   *
   * @param storeIds - Array of store IDs to be processed.
   * @returns {Promise<boolean>} - Resolves to true if all updates are successful or no stores are provided.
   */
  async syncMultipleStoreAssignments(storeIds: string[]): Promise<boolean> {
    try {
      if (storeIds.length === 0) {
        return true;
      }

      const queryQualityThread = this.buildQueryForQualityThread().andWhere('"thread".store_id::text = ANY($1)');
      const queryCuramaThread = this.buildQueryForCuramaThread().andWhere('"thread".store_id::text = ANY($1)');

      const finalQuery = this.buildFinalQuery(queryQualityThread, queryCuramaThread);
      const threads: InquiryThreadOpenSearchQuery[] = await this.dataSource.query(finalQuery, [storeIds]);

      await this.documentService.updateMultipleDocuments('thread_id', threads);

      return true;
    } catch (error) {
      this.logger.error('Error in syncStoreAssignments:', { error });
      return false;
    }
  }

  /**
   * Synchronizes assignment changes (admin, team, quality admin) for a single store.
   *
   * - Updates all threads linked to the specified `storeId` with the latest assignment data.
   *
   * @param storeId - The store ID whose threads should be updated.
   * @returns {Promise<boolean>} - Resolves to true if synchronization is successful.
   */
  async syncStoreAssignments(storeId: string): Promise<boolean> {
    try {
      const queryQualityThread = this.buildQueryForQualityThread().andWhere('"thread".store_id::text = $1');
      const queryCuramaThread = this.buildQueryForCuramaThread().andWhere('"thread".store_id::text = $1');

      const qualityThread = await this.dataSource.query(queryQualityThread.getQuery(), [storeId]);
      const curamaThread = await this.dataSource.query(queryCuramaThread.getQuery(), [storeId]);

      const threads = [...qualityThread, ...curamaThread];

      for (const thread of threads) {
        await this.documentService.updateDocumentsByCondition(
          { thread_id: thread.thread_id },
          { admin_id: thread.admin_id, team_id: thread.team_id },
        );
      }

      return true;
    } catch (error) {
      this.logger.error('Error in syncStoreAssignments:', { error });
      return false;
    }
  }

  /**
   * Synchronizes data when a new message is created.
   * @param messageId - The ID of the created message. If there is no ID, it will update the status of the thread.
   * @param threadId - The ID of the associated thread.
   * @param threadType - The type of the associated thread (e.g., QualityThread or CuramaThread).
   * @param threadStatusId - The status id of the associated thread (e.g., 1 or 2).
   * @param threadUpdatedAt - The updated timestamp of the associated thread.
   * @returns {Promise<boolean>} - Returns true if synchronization is successful, false otherwise.
   */
  async syncMessageCreation(
    messageId: string,
    threadId: string,
    threadType: number,
    threadStatusId: number,
    threadUpdatedAt: string,
  ): Promise<boolean> {
    try {
      let query: SelectQueryBuilder<InquiryThreadEntity> | null = null;

      if (threadType === ThreadType.QualityThread) {
        query = this.buildQueryForQualityThread();
      } else if (threadType === ThreadType.CuramaThread) {
        query = this.buildQueryForCuramaThread();
      }

      if (!query) {
        this.logger.error(`Invalid thread type: ${threadType}`);
        return false;
      }

      query.andWhere('"thread".id::text = $1').addOrderBy('"thread".updated_at', 'ASC');

      const threads: InquiryThreadOpenSearchQuery[] = await this.dataSource.query(query.getQuery(), [threadId]);

      if (!threads.length) {
        this.logger.log(`No threads found for threadId: ${threadId}`);
        return false;
      }

      if (messageId) {
        const message = await this.messageRepository.findOneBy({ id: messageId });
        if (!message) {
          this.logger.error(`No message found for messageId: ${messageId}`);
          return false;
        }

        const document = this.buildDocument(threads[0], message);
        await this.documentService.createDocument(document);
        this.logger.log(`Successfully synchronized message with messageId: ${messageId}`);
      }

      if (threadStatusId || threadUpdatedAt) {
        const updates: Partial<InquiryThreadOpenSearchQuery> = {};
        if (threadStatusId) {
          updates.thread_status_id = threadStatusId;
        }
        if (threadUpdatedAt) {
          updates.thread_updated_at = threadUpdatedAt;
        }
        await this.documentService.updateDocumentsByCondition({ thread_id: threadId }, updates);
        this.logger.log(`Successfully updated thread with threadId: ${threadId}, updates: ${JSON.stringify(updates)}`);
      }

      return true;
    } catch (error) {
      this.logger.error('Error during synchronizeMessageCreation:', { error });
      return false;
    }
  }

  /**
   * Builds a query for retrieving threads associated with quality teams (thread_type = QualityThread).
   * The query joins the necessary tables to fetch the team ID and admin ID for quality threads.
   *
   * @returns {SelectQueryBuilder<InquiryThreadEntity>} The query builder for quality threads.
   */
  buildQueryForQualityThread(): SelectQueryBuilder<InquiryThreadEntity> {
    return this.inquiryThreadRepository
      .createQueryBuilder('thread')
      .select([
        '"thread".id::text                  AS thread_id',
        '"thread".store_id::text            AS store_id',
        '"thread".status                    AS thread_status_id',
        '"thread".updated_at                AS thread_updated_at',
        '"qualityTeam".id::text             AS team_id',
        '"qualityAdminStore".admin_id::text AS admin_id',
      ])
      .leftJoin(QualityAdminsStoresEntity, 'qualityAdminStore', 'qualityAdminStore.storeId = thread.storeId')
      .leftJoin(AdminsTeamsEntity, 'adminTeam', 'adminTeam.adminId = qualityAdminStore.adminId')
      .leftJoin(TeamEntity, 'qualityTeam', 'qualityTeam.id = adminTeam.teamId')
      .where(`thread.thread_type = ${ThreadType.QualityThread}`);
  }

  /**
   * Builds a query for retrieving threads associated with curama teams (thread_type = CuramaThread).
   * The query joins the necessary tables to fetch the team ID and admin ID for curama threads.
   *
   * @returns {SelectQueryBuilder<InquiryThreadEntity>} The query builder for curama threads.
   */
  buildQueryForCuramaThread(): SelectQueryBuilder<InquiryThreadEntity> {
    return this.inquiryThreadRepository
      .createQueryBuilder('thread')
      .select([
        '"thread".id::text           AS thread_id',
        '"thread".store_id::text     AS store_id',
        '"thread".status             AS thread_status_id',
        '"thread".updated_at         AS thread_updated_at',
        '"curamaTeam".id::text       AS team_id',
        '"adminStore".admin_id::text AS admin_id',
      ])
      .leftJoin(TeamsStoresEntity, 'teamStore', 'teamStore.storeId = thread.storeId')
      .leftJoin(TeamEntity, 'curamaTeam', 'curamaTeam.id = teamStore.teamId')
      .leftJoin(AdminsStoresEntity, 'adminStore', 'adminStore.storeId = thread.storeId')
      .where(`thread.thread_type = ${ThreadType.CuramaThread}`);
  }

  /**
   * Combines queries for quality threads and Curama threads into a single SQL query.
   * The combined query retrieves thread data from both sources and orders them by the updated timestamp.
   *
   * @param queryQualityThread - The query builder for quality threads.
   * @param queryCuramaThread - The query builder for Curama threads.
   * @returns {string} - A combined SQL query string.
   */
  buildFinalQuery(
    queryQualityThread: SelectQueryBuilder<InquiryThreadEntity>,
    queryCuramaThread: SelectQueryBuilder<InquiryThreadEntity>,
  ): string {
    const baseQuery = `
      (${queryQualityThread.getQuery()})
      UNION ALL
      (${queryCuramaThread.getQuery()})
    `;

    return `
      SELECT * 
      FROM (${baseQuery}) AS CombinedThreads
    `;
  }

  /**
   * Builds a document to be inserted into OpenSearch
   * @param {InquiryThreadOpenSearchQuery} thread - Inquiry thread data from PostgreSQL
   * @param {MessageEntity[]} message - Item of related messages
   * @returns {InquiryThreadOpenSearchQuery} - The document to be inserted into OpenSearch
   */
  buildDocument(thread: InquiryThreadOpenSearchQuery, message: MessageEntity): InquiryThreadOpenSearchQuery {
    return {
      thread_id: thread.thread_id,
      store_id: thread.store_id,
      team_id: thread.team_id,
      admin_id: thread.admin_id,
      thread_status_id: thread.thread_status_id,
      message_id: message.id,
      message: message.content,
      thread_updated_at: thread.thread_updated_at,
    };
  }
}
