import { CURAM<PERSON>_SEARCH_INDEX_NAME } from '@common/constants/search';
import { CuramaOpenSearchClient } from '@vietnam/curama-opensearch';
import { SearchServiceDto } from '../dtos/search-service.dto';
import * as esb from 'elastic-builder';
import { Injectable, Logger } from '@nestjs/common';
import { InquiryThreadOpenSearchQuery, OpenSearchResponse } from '@common/interface/open-search.interface';

/**
 * This service handles search queries in OpenSearch.
 * SearchService
 */
@Injectable()
export class SearchService {
  private readonly logger = new Logger(SearchService.name);

  /**
   * Constructor for the SearchService class.
   * @param openSearchClient
   */
  constructor(private readonly openSearchClient: CuramaOpenSearchClient) {}

  /**
   * Execute a search query in OpenSearch using the provided search parameters.
   * @param searchQueryDto - Data Transfer Object containing search parameters
   * @returns Promise resolving with the OpenSearch response for InquiryThreadOpenSearchQuery results
   * If an error occurs, logs the error to the console.
   */
  async getThreadsByConditions(
    searchQueryDto: SearchServiceDto,
  ): Promise<OpenSearchResponse<InquiryThreadOpenSearchQuery[]>> {
    const query = await this.buildQueryConditions(searchQueryDto);
    try {
      const result = await this.openSearchClient.search({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: query,
      });
      return result.body as OpenSearchResponse<InquiryThreadOpenSearchQuery[]>;
    } catch (err) {
      this.logger.log({ err: err.message });
      return null;
    }
  }

  /**
   * Build the search query for OpenSearch based on the provided search parameters.
   * @param searchQueryDto - Data Transfer Object containing search parameters
   * @returns A Promise resolving to the constructed RequestBodySearch for OpenSearch
   */
  async buildQueryConditions(searchQueryDto: SearchServiceDto): Promise<any> {
    const { page = 1, perPage = 10, storeId, adminId, teamId, statusId, content, updatedAt, sort } = searchQueryDto;

    const totalDocuments = perPage * page;
    const maxLimit = 10000;

    if (totalDocuments > maxLimit) {
      // TODO: Scroll api for larger datasets opensearch
      this.logger.warn(
        `Cannot retrieve more than ${maxLimit} documents in a single request. Use scroll for larger datasets.`,
      );
    }

    const searchBody = esb
      .requestBodySearch()
      .from((page - 1) * perPage)
      .size(perPage)
      .trackTotalHits(true);

    const boolQuery = esb.boolQuery();
    searchBody.sort(esb.sort('thread_updated_at', sort?.thread_updated_at || 'desc'));

    if (storeId) {
      if (storeId.length === 36) {
        boolQuery.must(esb.termQuery('store_id.keyword', storeId));
      } else {
        boolQuery.must(esb.matchPhraseQuery('store_id', storeId));
      }
    }

    if (adminId) {
      boolQuery.must(esb.termQuery('admin_id', adminId));
    }

    if (teamId) {
      boolQuery.must(esb.termQuery('team_id', teamId));
    }

    if (statusId) {
      boolQuery.must(esb.termQuery('thread_status_id', statusId));
    }

    if (content && content.length > 0) {
      if (content.length > 1) {
        const contentQuery = esb
          .boolQuery()
          .should([
            esb.matchQuery('message', content.join(' ')).operator('and'),
            esb.boolQuery().must(content.map((term) => esb.wildcardQuery('message.keyword', `*${term}*`))),
          ])
          .minimumShouldMatch(1);

        boolQuery.must(contentQuery);
      } else {
        boolQuery.must(esb.wildcardQuery('message.keyword', `*${this.escapeSpecialCharacters(content[0])}*`));
      }
    }

    if (updatedAt) {
      boolQuery.must(esb.termQuery('thread_updated_at', updatedAt));
    }

    searchBody.query(boolQuery);

    const bodyJSON = searchBody.toJSON() as Record<string, any>;
    bodyJSON.collapse = {
      field: 'thread_id',
    };

    bodyJSON.aggs = {
      total_groups: {
        cardinality: {
          field: 'thread_id',
        },
      },
    };

    return bodyJSON;
  }

  /**
   * Escape special characters in the keyword.
   * @param keyword - The keyword to escape special characters in.
   */
  escapeSpecialCharacters(keyword: string): string {
    return keyword.replace(/[?*\\]/g, (match) => `\\${match}`);
  }
}
