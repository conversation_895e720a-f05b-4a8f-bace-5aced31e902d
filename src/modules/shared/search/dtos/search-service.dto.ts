import { OrderType } from '@src/common/enums/order-type.enum';
import { SortTypes } from '@src/common/enums/sort-type.enum';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class SearchServiceDto {
  @IsString()
  @IsOptional()
  storeId?: string;

  @IsString()
  @IsOptional()
  teamId?: string;

  @IsString()
  @IsOptional()
  adminId?: string;

  @IsNumber()
  @IsOptional()
  statusId?: number;

  @IsNumber()
  @IsOptional()
  threadType?: number;

  @IsString()
  @IsOptional()
  content?: string[];

  @IsOptional()
  @IsString()
  updatedAt?: string;

  @IsOptional()
  sort: Partial<Record<SortTypes, OrderType>>;

  @IsOptional()
  page?: number;

  @IsOptional()
  perPage?: number;
}
