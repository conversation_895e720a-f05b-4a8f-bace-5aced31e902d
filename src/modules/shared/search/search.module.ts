import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SearchService } from './services/search.service';
import { SyncDataService } from '@modules/shared/search/services/sync-data.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InquiryThreadEntity, MessageEntity } from '@src/entities';
import { QueueProvider } from '@providers/queue.provider';
import { RedisProvider } from '@providers/redis.provider';
import { DocumentService } from '@modules/shared/search/services/document.service';

@Module({
  imports: [
    QueueProvider.register(),
    TypeOrmModule.forFeature([InquiryThreadEntity, MessageEntity]),
    RedisProvider.register(),
  ],
  exports: [SearchService, SyncDataService, DocumentService],
  providers: [SearchService, SyncDataService, DocumentService],
})
export class SearchModule {}
