import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { ThreadType } from '@src/common/enums/thread-type';
import * as dayjs from 'dayjs';
import { CuramaOpenSearchClient } from '@vietnam/curama-opensearch';
import { MessageType } from '@src/common/enums/message-type';

@Injectable()
export class SyncAllData {
  private readonly logger = new Logger(SyncAllData.name);
  constructor(private readonly openSearchClient: CuramaOpenSearchClient, private dataSource: DataSource) {}

  private async prepareIndex(): Promise<void> {
    this.logger.log(`Preparing index: ${process.env.OPENSEARCH_INDEX}....`);
    const indexExists = await this.openSearchClient.indices.exists({ index: process.env.OPENSEARCH_INDEX });
    if (indexExists) {
      this.logger.log(`Index ${process.env.OPENSEARCH_INDEX} already exists. Deleting index....`);
      await this.openSearchClient.indices.delete({ index: process.env.OPENSEARCH_INDEX });
    }

    this.logger.log(`Creating index ${process.env.OPENSEARCH_INDEX}...`);
    const indexSetting = await import(`../index-mappings/curama_consulting_message.json`);
    try {
      await this.openSearchClient.indices.create({
        index: process.env.OPENSEARCH_INDEX,
        body: indexSetting,
      });
      this.logger.log(`Index ${process.env.OPENSEARCH_INDEX} created.`);
    } catch (e) {
      this.logger.error(`Error creating index ${process.env.OPENSEARCH_INDEX}: ${e.message}`);
      throw e;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public async run(): Promise<void> {
    await this.prepareIndex();
    // tracking timer
    const start = new Date().getTime();
    const schema = `"${process.env.DATABASE_SCHEMA}"` || 'public';
    const materializedView = `${schema}."StoreAssignmentView"`;
    // drop before create materialized view
    this.logger.log('Checking and dropping materialized view if exists');
    await this.dataSource.query(`DROP MATERIALIZED VIEW IF EXISTS ${materializedView};`);
    // create materialized view to get store assignment
    this.logger.log('Creating materialized view');
    await this.dataSource.query(
      `create materialized view ${schema}."StoreAssignmentView" as (
        select coalesce(consultantAdminTeamStore.store_id, qualityAdminTeamStore.store_id) as store_id,
        consultantAdminTeamStore.admin_id as consultant_admin_id, consultantAdminTeamStore.admin_name as consultant_admin_name,
        consultantAdminTeamStore.team_id as consultant_team_id, consultantAdminTeamStore.team_name as consultant_team_name,
        qualityAdminTeamStore.admin_id as quality_admin_id, qualityAdminTeamStore.admin_name as quality_admin_name,
        qualityAdminTeamStore.team_id as quality_team_id, qualityAdminTeamStore.team_name as quality_team_name
        from (
          select COALESCE(curamaAdmins.store_id, teamStores.store_id) AS store_id, curamaAdmins.admin_id, curamaAdmins.admin_name, 
            teamStores.team_id, teamStores.team_name from (
            (
              select adminStore.store_id as store_id, admins.id as admin_id, admins.name as admin_name from ${schema}."Admins" admins
              inner join ${schema}."Admins_Stores" adminStore on admins.id = adminstore.admin_id
            ) as curamaAdmins
            full outer join (
              select teamStore.store_id as store_id, team.id as team_id, team.name as team_name from ${schema}."Teams" team
              inner join ${schema}."Teams_Stores" teamStore on team.id = teamStore.team_id
            ) as teamStores on curamaAdmins.store_id = teamStores.store_id
          )
        )
        consultantAdminTeamStore full outer join (
          select qualityAdminStores.store_id as store_id, qualityAdmin.id as admin_id, qualityAdmin.name as admin_name,
          teams.id as team_id, teams.name as team_name from ${schema}."Admins" qualityAdmin
          inner join ${schema}."QualityAdmins_Stores" qualityAdminStores on qualityAdmin.id = qualityAdminStores.admin_id
          left join ${schema}."Admins_Teams" adminTeams on qualityAdmin.id = adminTeams.admin_id
          left join ${schema}."Teams" teams on adminTeams.team_id = teams.id
        ) as qualityAdminTeamStore on consultantAdminTeamStore.store_id = qualityAdminTeamStore.store_id
      );`,
    );
    this.logger.log('Materialized view created. Get all data from materialized view');
    const adminAssignment = await this.dataSource.query(`SELECT * FROM ${materializedView}`);
    const storeAssignmentDataCollection = {};
    this.logger.log('Processing store assignment data...grouping by store_id');
    for (const assignment of adminAssignment) {
      if (!storeAssignmentDataCollection[assignment.store_id]) {
        storeAssignmentDataCollection[assignment.store_id] = {};
      }
      storeAssignmentDataCollection[assignment.store_id] = assignment;
    }

    this.logger.log('Count messages and chunk data to sync to opensearch');
    // get count all messages
    const countMessages = await this.dataSource.query(
      `SELECT COUNT(*) FROM ${schema}."Messages" where type = ${MessageType.Message} and label is null`,
    );
    // chunk messages to 2500 messages per request
    const chunkSize = 1000;
    const totalChunks = Math.ceil(countMessages[0].count / chunkSize);
    this.logger.log(
      `Total messages: ${countMessages[0].count}, chunk size: ${chunkSize}, total chunks: ${totalChunks}`,
    );
    let processedDataCount = 0;
    let successBatchCount = 0;
    let errorBatchCount = 0;
    this.logger.log('Start processing chunk data');
    for (let i = 0; i < totalChunks; i++) {
      try {
        this.logger.log(`[OpenSearchSyncProcess] Processing chunk data ${i}`);
        // get message and inner join with thread to get store_id in thread
        const messages = await this.dataSource.query(
          `SELECT m.*, t.store_id, t.thread_type, t.status as thread_status, t.updated_at as thread_updated_at
           FROM ${schema}."Messages" m INNER JOIN ${schema}."InquiryThreads" t ON m.thread_id = t.id
           WHERE m.type = ${MessageType.Message} and m.label is null ORDER BY m.created_at LIMIT ${chunkSize} OFFSET ${
            i * chunkSize
          }`,
        );

        if (!messages || messages.length === 0) {
          this.logger.log('No more data to process');
          break;
        }

        const messageData = messages.map((message: any) => {
          return {
            thread_id: message.thread_id,
            store_id: message.store_id,
            team_id:
              message.thread_type == ThreadType.CuramaThread
                ? storeAssignmentDataCollection[message.store_id]?.consultant_team_id
                : storeAssignmentDataCollection[message.store_id]?.quality_team_id,
            admin_id:
              message.thread_type == ThreadType.CuramaThread
                ? storeAssignmentDataCollection[message.store_id]?.consultant_admin_id
                : storeAssignmentDataCollection[message.store_id]?.quality_admin_id,
            thread_status_id: message.thread_status,
            message_id: message.id,
            message: message.content,
            thread_updated_at: dayjs(message.thread_updated_at).toDate(),
          };
        });

        processedDataCount += messageData.length;

        const body = messageData.flatMap((doc: any) => [{ index: { _index: process.env.OPENSEARCH_INDEX } }, doc]);
        await this.openSearchClient.bulk({ refresh: true, body: body });
        successBatchCount++;
      } catch (e) {
        this.logger.error('[OpenSearchSyncProcess] Error processing chunk data', i);
        this.logger.error('[OpenSearchSyncProcess] Error:', e?.message);
        errorBatchCount++;
        continue;
      }
    }

    this.logger.log(`Sync completed: ${processedDataCount} messages in ${totalChunks} chunks`);
    this.logger.log(`Success batch: ${successBatchCount}, Error batch: ${errorBatchCount}`);
    const end = new Date().getTime();
    this.logger.log(`Total time: ${((end - start) / 1000).toFixed(2)}s`);
  }
}
