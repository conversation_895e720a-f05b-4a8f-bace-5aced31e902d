export enum RabbitMQEventType {
  UpsertAdminTeamStores = 'UpsertAdminStores',
  CreateAdminInfoForNewStore = 'CreateAdminInfoForNewStore',
  AdminCreated = 'AdminCreated',
  TeamUpserted = 'TeamUpserted',
  StoreChanged = 'StoreChanged',
  RemoveAdminFromStore = 'RemoveAdminFromStore',
  UpdateAdminTeamStore = 'UpdateAdminTeamStore',
  ConsultingMessageCreated = 'ConsultingMessageCreated',
  ScheduledBroadcastMessage = 'ScheduledBroadcastMessage',
  HandleCSVUpload = 'HandleCSVUpload',
  CreateReportBroadcastMessage = 'CreateReportBroadcastMessage',
}
