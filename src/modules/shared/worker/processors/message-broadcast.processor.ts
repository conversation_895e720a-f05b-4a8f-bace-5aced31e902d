import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { DataSource, EntityManager } from 'typeorm';
import { NotificationService } from '@src/modules/shared/notification/notification.service';
import { AttachmentEntity, InquiryThreadEntity, MessageEntity } from '@src/entities';
import { ThreadType } from '@common/enums/thread-type';
import { v4 as uuidv4 } from 'uuid';
import { InquiryThreadStatus } from '@common/enums/inquiry-thread-status';
import { MessageType } from '@common/enums/message-type';
import { AttachmentFileUploadType } from '@common/types/attachment-file-upload.type';
import { StoresInfo } from '@modules/terry/types/csv-record';
import { MessageLabel } from '@common/enums/message-label';
import { BULLMQ_MESSAGE_BROADCAST_QUEUE } from '@src/common/constants';
import { BullMQMessageBroadcastJobData } from '../types/bullmq-message-broadcast';
import { Request } from 'express';

@Processor(BULLMQ_MESSAGE_BROADCAST_QUEUE)
export class MessageBroadcastProcessor extends WorkerHost {
  private readonly logger = new Logger(MessageBroadcastProcessor.name);

  constructor(private readonly dataSource: DataSource, private readonly notificationService: NotificationService) {
    super();
  }

  async process(job: Job<BullMQMessageBroadcastJobData>): Promise<void> {
    const { batch, broadcastMessageId, headers, batchIndex, broadcastData } = job.data;

    this.logger.log(
      `Processing message broadcast batch ${batchIndex} for broadcast ${broadcastMessageId} with ${batch.length} stores`,
    );

    const storeIds = batch.map((store) => store.id);

    try {
      const { threadsToSave, messagesToInsert, attachmentsToInsert } = this.processStores(
        batch,
        broadcastData,
        headers['x-client-id'],
      );

      await this.performBulkOperations(threadsToSave, messagesToInsert, attachmentsToInsert);

      const request: Request = this.createRequest(headers);
      await this.notificationService.pushNotiNewMessageToStore(
        {
          storeId: storeIds,
          threadType: ThreadType.CuramaThread,
        },
        request,
      );

      this.logger.log(`Successfully processed batch ${batchIndex} for broadcast ${broadcastMessageId}`);
    } catch (error) {
      this.logger.error(`Batch processing error for batch ${batchIndex}:`, error);
      throw error;
    }
  }

  /**
   * Process each store in the batch and prepare data for bulk inserts/updates.
   */
  processStores(
    batch: StoresInfo[],
    broadcastData: {
      content: string;
      attachments: AttachmentFileUploadType[];
    },
    adminId: string,
  ): {
    threadsToSave: InquiryThreadEntity[];
    messagesToInsert: MessageEntity[];
    attachmentsToInsert: AttachmentFileUploadType[];
  } {
    const threadsToSave: InquiryThreadEntity[] = [];
    const messagesToInsert: MessageEntity[] = [];
    const attachmentsToInsert: AttachmentFileUploadType[] = [];

    for (const store of batch) {
      // Update existing thread or create a new one if not found
      const thread = this.createThread(store, adminId);

      // Create a new message associated with the thread
      const message = this.createMessage(broadcastData.content, adminId);
      thread.lastestMessageId = message.id;

      messagesToInsert.push(message);
      threadsToSave.push(thread);

      // Handle attachments
      if (broadcastData.attachments && broadcastData.attachments.length > 0) {
        for (const attachment of broadcastData.attachments) {
          attachmentsToInsert.push({
            ...attachment,
            relationId: message.id,
          });
        }
      }
    }

    return { threadsToSave, messagesToInsert, attachmentsToInsert };
  }

  /**
   * Create a new thread for the store.
   */
  private createThread(store: StoresInfo, adminId: string): InquiryThreadEntity {
    const thread = new InquiryThreadEntity();
    thread.id = uuidv4();
    thread.storeId = store.id;
    thread.storeName = store.name;
    thread.threadType = ThreadType.CuramaThread;
    thread.isRead = false;
    thread.status = InquiryThreadStatus.AlreadyRead;
    thread.updatedBy = adminId;
    thread.updatedAt = new Date();
    return thread;
  }

  /**
   * Create a new message.
   */
  private createMessage(content: string, adminId: string): MessageEntity {
    const message = new MessageEntity();
    message.id = uuidv4();
    message.content = content;
    message.type = MessageType.Message;
    message.label = MessageLabel.System;
    message.fromAdminId = adminId;
    message.createdAt = new Date();
    message.updatedAt = new Date();
    return message;
  }

  /**
   * Perform bulk inserts or updates for threads, messages, and attachments.
   * Matches the original ConcurrentMessageBatch implementation exactly.
   */
  async performBulkOperations(
    threadsToSave: InquiryThreadEntity[],
    messagesToInsert: MessageEntity[],
    attachmentsToInsert: AttachmentFileUploadType[],
  ): Promise<void> {
    try {
      await this.dataSource.transaction(async (entityManager: EntityManager) => {
        if (threadsToSave.length) {
          // First upsert threads and get the identifiers
          const data = await this.upsertData(
            entityManager,
            InquiryThreadEntity,
            threadsToSave,
            ['store_id', 'thread_type'],
            ['store_name', 'draft_id', 'lastest_message_id', 'is_read', 'updated_by', 'updated_at'],
          );

          // CRITICAL FIX: Set threadId for messages using the upserted thread identifiers
          if (messagesToInsert.length) {
            messagesToInsert.forEach((message, index) => {
              message.threadId = data.identifiers[index].id;
            });
            await this.upsertData(
              entityManager,
              MessageEntity,
              messagesToInsert,
              ['id'],
              ['thread_id', 'content', 'from_admin_id', 'type', 'label'],
            );
          }

          if (attachmentsToInsert.length) {
            await this.upsertData(
              entityManager,
              AttachmentEntity,
              attachmentsToInsert,
              ['id'],
              ['path', 'attachment_type', 'relation_id'],
            );
          }
        }
      });
    } catch (error) {
      this.logger.error('Error in bulk operations:', error);
      throw error;
    }
  }

  /**
   * Upsert data using conflict resolution.
   * Matches the original ConcurrentMessageBatch implementation exactly.
   */
  private async upsertData(
    entityManager: EntityManager,
    entity: any,
    values: any[],
    conflictPaths: string[],
    upsertPaths: string[],
  ) {
    return await entityManager
      .createQueryBuilder()
      .insert()
      .into(entity)
      .values(values)
      .orUpdate([...conflictPaths, ...upsertPaths], conflictPaths, {
        skipUpdateIfNoValuesChanged: true,
        upsertType: 'on-conflict-do-update',
      })
      .execute();
  }

  /**
   * Create a mock request object from headers.
   * Matches the original ConcurrentMessageBatch implementation.
   */
  private createRequest(headers: any): Request {
    return {
      headers,
      terrySession: {
        adminId: headers['x-client-id'],
      },
    } as unknown as Request;
  }
}
