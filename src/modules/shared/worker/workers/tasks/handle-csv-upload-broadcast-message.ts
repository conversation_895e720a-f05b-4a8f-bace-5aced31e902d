import { WorkerHandler } from '@modules/shared/worker/workers/WorkerHandler';
import { CuramaWorker } from '@vietnam/curama-worker/decorators/Worker.decorator';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { BroadcastMessageService } from '@modules/shared/broadcast-message/broadcast-message.service';

@CuramaWorker(RabbitMQEventType.HandleCSVUpload)
export class HandleCsvCheckStore extends WorkerHandler {
  constructor(private readonly broadcastMessageService: BroadcastMessageService) {
    super();
  }

  /**
   * Handles the scheduled message sending when the scheduled time arrives.
   * Retrieve messages to be sent based on the scheduled time and processes them.
   * @param payload - Contains information for the scheduled task (broadcast message ID).
   * @returns Success message if messages are sent.
   */
  async handle(payload: { broadcastMessageId: string }): Promise<any> {
    try {
      const broadcastMessage = await this.broadcastMessageService.findOneById(payload.broadcastMessageId);

      if (!broadcastMessage) {
        this.logger.log('There is no message to send.');
        return;
      }
      if (!broadcastMessage.importFilePath) {
        this.logger.log('There is no csv to process.');
        return;
      }
      try {
        const stream = await this.broadcastMessageService.getStreamFromS3(broadcastMessage.importFilePath);
        const csvData = await this.broadcastMessageService.processStoreCount(stream);
        broadcastMessage.canSendStoreCount = csvData.totalStoresSuccess;
        broadcastMessage.cannotSendStoreCount = csvData.totalRow - csvData.totalStoresSuccess;
        await broadcastMessage.save();
        this.logger.log('The message was successfully processed.');
      } catch (sendError) {
        this.logger.log(
          `Failed to process the message: ${broadcastMessage.id} at time: ${broadcastMessage.scheduledSendTime}`,
          sendError,
        );
      }
    } catch (error) {
      this.logger.log('An error occurred while processing the scheduled message.', error);
    }
  }
  /**
   * Validates the task payload to ensure it is in the correct format.
   * @param payload - The task payload to validate.
   * @throws Error if the payload is invalid.
   */
  validateTask(payload: any): boolean {
    if (!payload) {
      throw new Error('The payload is invalid.');
    }
    return true;
  }
}
