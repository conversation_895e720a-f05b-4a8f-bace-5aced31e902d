import { <PERSON><PERSON>and<PERSON> } from '@modules/shared/worker/workers/WorkerHandler';
import { CuramaWorker } from '@vietnam/curama-worker/decorators/Worker.decorator';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { AdminsStoresEntity, TeamsStoresEntity } from '@src/entities';
import { QualityAdminsStoresEntity } from '@src/entities/quality-admins-stores.entity';
import { DataSource, In } from 'typeorm';
import { AdminTeamStoreMessage } from '@modules/shared/worker/types/admin-team-store-message';
import { SyncDataService } from '@modules/shared/search/services/sync-data.service';

@CuramaWorker(RabbitMQEventType.UpsertAdminTeamStores)
export class UpsertAdminTeamStore extends WorkerHandler {
  constructor(private dataSource: DataSource, private readonly syncDataService: SyncDataService) {
    super();
  }
  async handle(payload: AdminTeamStoreMessage, header?: any): Promise<any> {
    if (!payload) {
      this.logger.error('Process event team admin store: invalid content');
      return;
    }

    const {
      listStoreIdAdmin,
      listStoreIdTeam,
      listStoreIdQualityAdmin,
      listAdminStore,
      listTeamStore,
      listQualityAdminStore,
    } = payload.admin_assignments.reduce(
      (acc, item) => {
        if (item.admin_id) {
          const adminStoreEntity = new AdminsStoresEntity();
          adminStoreEntity.storeId = item.store_id;
          adminStoreEntity.adminId = item.admin_id;
          acc.listAdminStore.push(adminStoreEntity);
          acc.listStoreIdAdmin.push(item.store_id);
        }

        if (item.team_id) {
          const teamStoreEntity = new TeamsStoresEntity();
          teamStoreEntity.storeId = item.store_id;
          teamStoreEntity.teamId = item.team_id;
          acc.listTeamStore.push(teamStoreEntity);
          acc.listStoreIdTeam.push(item.store_id);
        }

        if (item.quality_admin_id) {
          const qualityAdminsStoresEntity = new QualityAdminsStoresEntity();
          qualityAdminsStoresEntity.storeId = item.store_id;
          qualityAdminsStoresEntity.adminId = item.quality_admin_id;
          acc.listQualityAdminStore.push(qualityAdminsStoresEntity);
          acc.listStoreIdQualityAdmin.push(item.store_id);
        }

        return acc;
      },
      {
        listStoreIdAdmin: [],
        listStoreIdTeam: [],
        listStoreIdQualityAdmin: [],
        listAdminStore: [],
        listTeamStore: [],
        listQualityAdminStore: [],
      },
    );

    const transactionResult = await this.dataSource.transaction(async (transactionalEntityManager) => {
      await Promise.all([
        transactionalEntityManager.delete(AdminsStoresEntity, { storeId: In(listStoreIdAdmin) }),
        transactionalEntityManager.delete(TeamsStoresEntity, { storeId: In(listStoreIdTeam) }),
        transactionalEntityManager.delete(QualityAdminsStoresEntity, { storeId: In(listStoreIdQualityAdmin) }),
      ]);

      await Promise.all([
        transactionalEntityManager.save(listAdminStore),
        transactionalEntityManager.save(listTeamStore),
        transactionalEntityManager.save(listQualityAdminStore),
      ]);

      return true;
    });

    if (transactionResult) {
      try {
        const uniqueStoreIds = [...new Set(payload.admin_assignments.map((item) => item.store_id))];
        await this.syncDataService.syncMultipleStoreAssignments(uniqueStoreIds);
      } catch (error) {
        this.logger.log('Error syncing store assignments:', error);
      }
    }

    return true;
  }

  validateTask(payload: AdminTeamStoreMessage): boolean {
    for (const item of payload.admin_assignments) {
      if (!item.store_id) {
        throw new Error('Invalid payload');
      }
    }
    return true;
  }
}
