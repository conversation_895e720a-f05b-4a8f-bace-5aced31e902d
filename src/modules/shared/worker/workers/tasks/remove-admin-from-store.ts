import { <PERSON><PERSON>and<PERSON> } from '@modules/shared/worker/workers/WorkerHandler';
import { <PERSON>urama<PERSON>orker } from '@vietnam/curama-worker/decorators/Worker.decorator';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { DocumentService } from '@modules/shared/search/services/document.service';
import { AdminChangedDocument } from '@modules/shared/worker/types/admin-changed-document';

@CuramaWorker(RabbitMQEventType.RemoveAdminFromStore)
export class RemoveAdminFromStore extends WorkerHandler {
  /**
   * Inject dependencies through constructor
   * @param documentService - The DocumentService to handle document operations
   */
  constructor(private readonly documentService: DocumentService) {
    super();
  }

  /**
   * Handles synchronizing documents to OpenSearch using bulk operations
   * @param payload - Contains an array of ThreadDocumentType documents
   * @returns {Promise<any>}
   */
  async handle(payload: AdminChangedDocument): Promise<any> {
    await this.documentService.updateDocumentsByCondition(
      { admin_id: payload.admin_id },
      { admin_id: '', team_id: '' },
    );
  }

  /**
   * Validates the payload to ensure it contains valid documents array
   * @param payload - SyncDataDocument containing documents to be validated
   * @returns {boolean}
   */
  validateTask(payload: AdminChangedDocument): boolean {
    if (!payload || !payload.admin_id) {
      throw new Error('Invalid payload structure');
    }
    return true;
  }
}
