import { WorkerHandler } from '@modules/shared/worker/workers/WorkerHandler';
import { CuramaWorker } from '@vietnam/curama-worker/decorators/Worker.decorator';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { TeamEntity } from '@src/entities';
import { DataSource } from 'typeorm';
import { TeamMessage } from '../../types/team-message';

@CuramaWorker(RabbitMQEventType.TeamUpserted)
export class TeamUpserted extends WorkerHandler {
  constructor(private dataSource: DataSource) {
    super();
  }
  async handle(payload: TeamMessage): Promise<any> {
    if (!payload) {
      this.logger.error('Process event team upserted: invalid content');
      return;
    }

    const team = new TeamEntity();
    team.id = payload.team_id;
    team.name = payload.name;
    team.departmentId = payload.department_id;
    team.isLast = true;
    return this.dataSource.getRepository(TeamEntity).upsert(team, ['name', 'departmentId']);
  }

  validateTask(payload: TeamMessage): boolean {
    if (!payload || !payload.team_id || !payload.name || !payload.department_id) {
      throw new Error('Invalid payload');
    }
    return true;
  }
}
