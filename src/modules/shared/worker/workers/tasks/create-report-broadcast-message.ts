import { WorkerHandler } from '@modules/shared/worker/workers/WorkerHandler';
import { CuramaWorker } from '@vietnam/curama-worker/decorators/Worker.decorator';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { BroadcastMessageService } from '@modules/shared/broadcast-message/broadcast-message.service';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import {
  CONCURRENT_MESSAGE_FILENAME_SENT_FAILED,
  CONCURRENT_MESSAGE_FILENAME_SENT_SUCCESS,
  CONCURRENT_MESSAGE_FOLDER_NAME,
  BULLMQ_MESSAGE_BROADCAST_QUEUE,
} from '@common/constants';
import { AttachmentFileUploadType } from '@common/types/attachment-file-upload.type';
import { AttachmentService } from '@modules/shared/attachment/attachment.service';
import { debug } from '@src/common/utils/debugger';
import { Py3CommonRequestHeadersInterface } from '@vietnam/cnga-http-request';

@CuramaWorker(RabbitMQEventType.CreateReportBroadcastMessage)
export class CreateReportBroadcastMessage extends WorkerHandler {
  constructor(
    @InjectQueue(BULLMQ_MESSAGE_BROADCAST_QUEUE) private readonly messageBroadcastQueue: Queue,
    private readonly broadcastMessageService: BroadcastMessageService,
    private readonly attachmentService: AttachmentService,
  ) {
    super();
  }

  /**
   * Handles the scheduled message sending when the scheduled time arrives.
   * Retrieve messages to be sent based on the scheduled time and processes them.
   * @param payload - Contains information for the scheduled task (broadcast message ID).
   * @returns Success message if messages are sent.
   */
  async handle(payload: { broadcastMessageId: string }, _headers: Py3CommonRequestHeadersInterface): Promise<any> {
    try {
      const broadcastMessageId = payload.broadcastMessageId;

      // Get job counts and store data from BullMQ
      const { completedJobs, failedJobs, totalJobs, successStoreIds, failStoreIds } = await this.getBroadcastJobData(
        broadcastMessageId,
      );

      debug('createReport', {
        totalJobs,
        completedJobs: completedJobs.length,
        failedJobs: failedJobs.length,
        broadcastMessageId,
      });

      const totalProcessedJobs = completedJobs.length + failedJobs.length;

      // Check if all jobs have been processed
      if (totalJobs > 0 && totalProcessedJobs === totalJobs) {
        // Create success report if there are successful stores
        if (successStoreIds.length > 0) {
          await this.createReportCsvFile(successStoreIds, broadcastMessageId, CONCURRENT_MESSAGE_FILENAME_SENT_SUCCESS);
        }

        // Create failure report if there are failed stores
        if (failStoreIds.length > 0) {
          await this.createReportCsvFile(failStoreIds, broadcastMessageId, CONCURRENT_MESSAGE_FILENAME_SENT_FAILED);
        }

        // Update broadcast message status
        await this.broadcastMessageService.updateSentCountsAndStatus(
          broadcastMessageId,
          successStoreIds.length,
          failStoreIds.length,
        );

        // Clean up BullMQ jobs for this broadcast
        await this.cleanupBroadcastJobs(broadcastMessageId);

        this.logger.log('Created report successfully');
      } else {
        this.logger.log('Create report fail, data for failing report', {
          totalJobs,
          totalProcessedJobs,
          broadcastMessageId,
        });
      }
    } catch (error) {
      this.logger.log('An error occurred while processing the scheduled message.', error);
    }
  }

  /**
   * Get job data from BullMQ for a specific broadcast message.
   * Returns completed and failed jobs along with store IDs.
   */
  private async getBroadcastJobData(broadcastMessageId: string): Promise<{
    completedJobs: any[];
    failedJobs: any[];
    totalJobs: number;
    successStoreIds: string[];
    failStoreIds: string[];
  }> {
    try {
      // Get completed and failed jobs
      const [completedJobsData, failedJobsData] = await Promise.all([
        this.messageBroadcastQueue.getJobs(['completed'], 0, -1),
        this.messageBroadcastQueue.getJobs(['failed'], 0, -1),
      ]);

      // Filter jobs that match our broadcast ID pattern
      const completedJobs = completedJobsData.filter(
        (job) => job.id && job.id.toString().startsWith(`${broadcastMessageId}-batch-`),
      );

      const failedJobs = failedJobsData.filter(
        (job) => job.id && job.id.toString().startsWith(`${broadcastMessageId}-batch-`),
      );

      // Get total jobs by checking all job states
      const [activeJobsData, waitingJobsData, delayedJobsData] = await Promise.all([
        this.messageBroadcastQueue.getJobs(['active'], 0, -1),
        this.messageBroadcastQueue.getJobs(['waiting'], 0, -1),
        this.messageBroadcastQueue.getJobs(['delayed'], 0, -1),
      ]);

      const activeJobs = activeJobsData.filter(
        (job) => job.id && job.id.toString().startsWith(`${broadcastMessageId}-batch-`),
      ).length;

      const waitingJobs = waitingJobsData.filter(
        (job) => job.id && job.id.toString().startsWith(`${broadcastMessageId}-batch-`),
      ).length;

      const delayedJobs = delayedJobsData.filter(
        (job) => job.id && job.id.toString().startsWith(`${broadcastMessageId}-batch-`),
      ).length;

      const totalJobs = completedJobs.length + failedJobs.length + activeJobs + waitingJobs + delayedJobs;

      // Extract store IDs from job data
      const successStoreIds: string[] = [];
      const failStoreIds: string[] = [];

      // Get store IDs from completed jobs
      for (const job of completedJobs) {
        if (job.data && job.data.batch) {
          const storeIds = job.data.batch.map((store: any) => store.id);
          successStoreIds.push(...storeIds);
        }
      }

      // Get store IDs from failed jobs
      for (const job of failedJobs) {
        if (job.data && job.data.batch) {
          const storeIds = job.data.batch.map((store: any) => store.id);
          failStoreIds.push(...storeIds);
        }
      }

      return {
        completedJobs,
        failedJobs,
        totalJobs,
        successStoreIds,
        failStoreIds,
      };
    } catch (error) {
      this.logger.error(`Error getting job data for broadcast ${broadcastMessageId}:`, error);
      return {
        completedJobs: [],
        failedJobs: [],
        totalJobs: 0,
        successStoreIds: [],
        failStoreIds: [],
      };
    }
  }

  /**
   * Clean up BullMQ jobs for a specific broadcast message.
   * Removes completed and failed jobs to free up memory.
   */
  private async cleanupBroadcastJobs(broadcastMessageId: string): Promise<void> {
    try {
      // Get all jobs for this broadcast
      const [completedJobs, failedJobs] = await Promise.all([
        this.messageBroadcastQueue.getJobs(['completed'], 0, -1),
        this.messageBroadcastQueue.getJobs(['failed'], 0, -1),
      ]);

      // Filter jobs that match our broadcast ID pattern
      const jobsToRemove = [
        ...completedJobs.filter((job) => job.id && job.id.toString().startsWith(`${broadcastMessageId}-batch-`)),
        ...failedJobs.filter((job) => job.id && job.id.toString().startsWith(`${broadcastMessageId}-batch-`)),
      ];

      // Remove jobs
      const removePromises = jobsToRemove.map((job) => job.remove());
      await Promise.all(removePromises);

      this.logger.log(`Cleaned up ${jobsToRemove.length} BullMQ jobs for broadcast ${broadcastMessageId}`);
    } catch (error) {
      this.logger.error(`Error cleaning up jobs for broadcast ${broadcastMessageId}:`, error);
    }
  }

  /**
   * Create a CSV file report of store IDs that failed to receive the message
   * @param storeIds The list of store IDs
   * @param broadcastMessageId The ID of Broadcast Message
   * @param fileName The name of the file sent successfully or in error
   * @returns The uploaded file information
   */
  async createReportCsvFile(
    storeIds: string[],
    broadcastMessageId: string,
    fileName: string,
  ): Promise<AttachmentFileUploadType> {
    try {
      const csvContent = `id\n${storeIds.join('\n')}`;
      const buffer = Buffer.from(csvContent, 'utf-8');

      const csvFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'file.csv',
        encoding: 'utf8',
        mimetype: 'text/csv',
        buffer,
        size: buffer.length,
        stream: null,
        destination: null,
        filename: null,
        path: null,
      };

      return this.attachmentService.uploadFile(
        csvFile,
        null,
        `${CONCURRENT_MESSAGE_FOLDER_NAME}/${broadcastMessageId}`,
        fileName,
      );
    } catch (error) {
      this.logger.log(`Upload file report error for broadcast message ${broadcastMessageId}:`, error);
    }
  }

  /**
   * Validates the task payload to ensure it is in the correct format.
   * @param payload - The task payload to validate.
   * @throws Error if the payload is invalid.
   */
  validateTask(payload: any): boolean {
    if (!payload) {
      throw new Error('The payload is invalid.');
    }
    return true;
  }
}
