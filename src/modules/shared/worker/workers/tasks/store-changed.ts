import { <PERSON><PERSON>and<PERSON> } from '@modules/shared/worker/workers/WorkerHandler';
import { CuramaWorker } from '@vietnam/curama-worker/decorators/Worker.decorator';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { InquiryThreadEntity } from '@src/entities';
import { DataSource } from 'typeorm';
import { StoreChangedMessage } from '../../types/store-changed-message';
import { StoreEntity } from '@src/entities/store.entity';

@CuramaWorker(RabbitMQEventType.StoreChanged)
export class StoreChanged extends WorkerHandler {
  constructor(private dataSource: DataSource) {
    super();
  }
  async handle(payload: StoreChangedMessage): Promise<any> {
    await this.dataSource.transaction(async (transactionalEntityManager) => {
      const store = await transactionalEntityManager.findOne(StoreEntity, { where: { id: payload.store_id } });

      if (!store) {
        await transactionalEntityManager.save(StoreEntity, {
          id: payload.store_id,
          status: payload.store_status_id,
          name: payload.store_name,
        });
      } else {
        await transactionalEntityManager.update(
          StoreEntity,
          { id: payload.store_id },
          {
            name: payload.store_name,
            status: payload.store_status_id,
          },
        );
      }

      await transactionalEntityManager.update(
        InquiryThreadEntity,
        { storeId: payload.store_id },
        {
          storeName: payload.store_name,
          updatedAt: () => '"updated_at"',
        },
      );
    });
    return true;
  }

  validateTask(payload: StoreChangedMessage): boolean {
    if (!payload || !payload.store_id || !payload.store_name) {
      throw new Error('Invalid payload');
    }
    return true;
  }
}
