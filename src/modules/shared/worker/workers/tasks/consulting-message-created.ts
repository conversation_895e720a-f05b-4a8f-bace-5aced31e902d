import { <PERSON><PERSON><PERSON><PERSON> } from '@modules/shared/worker/workers/WorkerHandler';
import { <PERSON>ura<PERSON><PERSON>orker } from '@vietnam/curama-worker/decorators/Worker.decorator';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { SyncDataService } from '@modules/shared/search/services/sync-data.service';
import { MessageCreatedDocument } from '@modules/shared/worker/types/message-created-document';

@CuramaWorker(RabbitMQEventType.ConsultingMessageCreated)
export class ConsultingMessageCreated extends WorkerHandler {
  /**
   * Inject dependencies through constructor
   * @param syncDataService - The SyncDataService to handle data syncing
   */
  constructor(private readonly syncDataService: SyncDataService) {
    super();
  }

  /**
   * Handles synchronizing documents to OpenSearch using bulk operations
   * @param payload - Contains an array of ThreadDocumentType documents
   * @returns {Promise<any>}
   */
  async handle(payload: MessageCreatedDocument): Promise<any> {
    await this.syncDataService.syncMessageCreation(
      payload.message_id,
      payload.thread_id,
      payload.thread_type,
      payload.thread_status_id,
      payload.thread_updated_at,
    );
  }

  /**
   * Validates the payload to ensure it contains valid documents array
   * @param payload - SyncDataDocument containing documents to be validated
   * @returns {boolean}
   */
  validateTask(payload: MessageCreatedDocument): boolean {
    if (!payload || !payload.thread_id || !payload.thread_type || !payload.thread_status_id) {
      throw new Error('Invalid payload structure');
    }
    return true;
  }
}
