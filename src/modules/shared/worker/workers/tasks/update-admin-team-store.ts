import { <PERSON><PERSON>and<PERSON> } from '@modules/shared/worker/workers/WorkerHandler';
import { <PERSON>urama<PERSON>orker } from '@vietnam/curama-worker/decorators/Worker.decorator';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { SyncDataService } from '@modules/shared/search/services/sync-data.service';
import { StoreChangedDocument } from '@modules/shared/worker/types/store-changed-document';

@CuramaWorker(RabbitMQEventType.UpdateAdminTeamStore)
export class UpdateAdminTeamStore extends WorkerHandler {
  /**
   * Inject dependencies through constructor
   * @param syncDataService - The SyncDataService to handle data syncing
   */
  constructor(private readonly syncDataService: SyncDataService) {
    super();
  }

  /**
   * Handles synchronizing documents to OpenSearch using bulk operations
   * @param payload - Contains an array of ThreadDocumentType documents
   * @returns {Promise<any>}
   */
  async handle(payload: StoreChangedDocument): Promise<any> {
    await this.syncDataService.syncStoreAssignments(payload.store_id);
  }

  /**
   * Validates the payload to ensure it contains valid documents array
   * @param payload - SyncDataDocument containing documents to be validated
   * @returns {boolean}
   */
  validateTask(payload: StoreChangedDocument): boolean {
    if (!payload || !payload.store_id) {
      throw new Error('Invalid payload structure');
    }
    return true;
  }
}
