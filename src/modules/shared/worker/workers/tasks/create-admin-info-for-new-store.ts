import { <PERSON><PERSON>andler } from '@modules/shared/worker/workers/WorkerHandler';
import { CuramaWorker } from '@vietnam/curama-worker/decorators/Worker.decorator';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { AdminsStoresEntity, CanaryReleaseStoreEntity, TeamsStoresEntity } from '@src/entities';
import { QualityAdminsStoresEntity } from '@src/entities/quality-admins-stores.entity';
import { DataSource } from 'typeorm';
import { AdminInfoForNewStoreMessage } from '../../types/admin-info-for-new-store-message';
import { StoreEntity } from '@src/entities/store.entity';
import { StoreStatus } from '@src/common/constants';

@CuramaWorker(RabbitMQEventType.CreateAdminInfoForNewStore)
export class CreateAdminInfoForNewStore extends WorkerHandler {
  constructor(private dataSource: DataSource) {
    super();
  }

  async handle(payload: AdminInfoForNewStoreMessage, header?: any): Promise<any> {
    if (!payload) {
      this.logger.error('Process event team admin store: invalid content');
      return;
    }

    return this.dataSource.transaction(async (transactionalEntityManager) => {
      const operationsDelete = [];
      const operationsSave = [];

      if (payload.admin_id) {
        operationsDelete.push(transactionalEntityManager.delete(AdminsStoresEntity, { storeId: payload.store_id }));

        const adminStoreEntity = new AdminsStoresEntity();
        adminStoreEntity.storeId = payload.store_id;
        adminStoreEntity.adminId = payload.admin_id;
        operationsSave.push(transactionalEntityManager.save(adminStoreEntity));
      }

      if (payload.team_id) {
        operationsDelete.push(transactionalEntityManager.delete(TeamsStoresEntity, { storeId: payload.store_id }));

        const teamsStoresEntity = new TeamsStoresEntity();
        teamsStoresEntity.storeId = payload.store_id;
        teamsStoresEntity.teamId = payload.team_id;
        operationsSave.push(transactionalEntityManager.save(teamsStoresEntity));
      }

      if (payload.quality_admin_id) {
        operationsDelete.push(
          transactionalEntityManager.delete(QualityAdminsStoresEntity, { storeId: payload.store_id }),
        );

        const qualityAdminsStoresEntity = new QualityAdminsStoresEntity();
        qualityAdminsStoresEntity.storeId = payload.store_id;
        qualityAdminsStoresEntity.adminId = payload.quality_admin_id;
        operationsSave.push(transactionalEntityManager.save(qualityAdminsStoresEntity));
      }
      if (payload.store_id) {
        const checkStoreExists = await transactionalEntityManager.exists(StoreEntity, {
          where: {
            id: payload.store_id,
          },
        });
        if (!checkStoreExists) {
          const storeEntity = new StoreEntity();
          storeEntity.id = payload.store_id;
          storeEntity.name = payload.store_name ?? '';
          storeEntity.status = StoreStatus.NewlyApplied;
          operationsSave.push(transactionalEntityManager.insert(StoreEntity, storeEntity));
        }
      }

      const canaryReleaseStoreEntity = new CanaryReleaseStoreEntity();
      canaryReleaseStoreEntity.storeId = payload.store_id;
      operationsSave.push(transactionalEntityManager.save(canaryReleaseStoreEntity));

      await Promise.all(operationsDelete);
      await Promise.all(operationsSave);

      return true;
    });
  }

  validateTask(payload: AdminInfoForNewStoreMessage): boolean {
    if (!payload.store_id) {
      throw new Error('Invalid payload');
    }
    return true;
  }
}
