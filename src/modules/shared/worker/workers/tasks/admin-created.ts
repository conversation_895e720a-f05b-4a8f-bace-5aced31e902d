import { WorkerHand<PERSON> } from '@modules/shared/worker/workers/WorkerHandler';
import { CuramaWorker } from '@vietnam/curama-worker/decorators/Worker.decorator';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { AdminEntity } from '@src/entities';
import { DataSource } from 'typeorm';
import { UpdateAdminMessage } from '../../types/update-admin-message';

@CuramaWorker(RabbitMQEventType.AdminCreated)
export class AdminCreated extends WorkerHandler {
  constructor(private dataSource: DataSource) {
    super();
  }
  async handle(payload: UpdateAdminMessage): Promise<any> {
    if (!payload) {
      this.logger.error('Process event admin created: invalid content');
      return;
    }

    const newAdmin = new AdminEntity();
    newAdmin.id = payload.admin_id;
    newAdmin.status = payload.status;
    newAdmin.email = payload.email;
    payload?.name && (newAdmin.name = payload.name);

    return this.dataSource.getRepository(AdminEntity).upsert(newAdmin, ['email']);
  }

  validateTask(payload: UpdateAdminMessage): boolean {
    if (!payload || !payload.admin_id || !payload.status || !payload.email) {
      throw new Error('Invalid payload');
    }
    return true;
  }
}
