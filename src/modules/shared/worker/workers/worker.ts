import { Injectable, Logger, UseInterceptors } from '@nestjs/common';
import { WorkerEventManager } from '@vietnam/curama-worker/WorkerEventManager/WorkerEventManager';
import { RabbitRPC, RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { WorkerLoggerInterceptor } from '@vietnam/cnga-middleware';
import { CuramaWorkerInterceptor } from '@vietnam/curama-worker';
import { ConsumeMessage } from 'amqplib';
import * as process from 'process';
import { RabbitMQExceptionHandler } from '@exceptions/rabbitmq-exception-handler';
import { RabbitMQMessage } from '@modules/shared/worker/types/rabbitmq-payload';
import { MessageHeader } from '@modules/shared/worker/types/message-header';
import { WorkerHandler } from '@modules/shared/worker/workers/WorkerHandler';
import { SYNC_OPENSEARCH_MESSAGE_MQ_TASK } from '@src/common/constants';
import { CONCURRENT_MESSAGE_MQ_NAME } from '@src/common/constants';

@Injectable()
export class Worker {
  private readonly logger = new Logger(Worker.name);

  constructor(private workerEventManager: WorkerEventManager) {}

  @RabbitRPC({
    queue: 'consulting-message',
    exchange: 'eventManager',
    routingKey: 'all',
    errorHandler: RabbitMQExceptionHandler,
    allowNonJsonMessages: true,
  })
  @UseInterceptors(WorkerLoggerInterceptor, CuramaWorkerInterceptor)
  async handler(payload: RabbitMQMessage, raw: ConsumeMessage) {
    await this.commonHandle(payload, raw);
  }

  @RabbitSubscribe({
    queue: CONCURRENT_MESSAGE_MQ_NAME,
    exchange: 'taskManager',
    routingKey: CONCURRENT_MESSAGE_MQ_NAME,
    errorHandler: RabbitMQExceptionHandler,
    allowNonJsonMessages: true,
  })
  @UseInterceptors(WorkerLoggerInterceptor, CuramaWorkerInterceptor)
  async taskHandle(payload: RabbitMQMessage, raw: ConsumeMessage) {
    await this.commonHandle(payload, raw);
  }

  @RabbitSubscribe({
    queue: SYNC_OPENSEARCH_MESSAGE_MQ_TASK,
    exchange: 'taskManager',
    routingKey: SYNC_OPENSEARCH_MESSAGE_MQ_TASK,
    errorHandler: RabbitMQExceptionHandler,
    allowNonJsonMessages: true,
  })
  @UseInterceptors(WorkerLoggerInterceptor, CuramaWorkerInterceptor)
  async syncOpenSearchData(payload: RabbitMQMessage, raw: ConsumeMessage) {
    await this.commonHandle(payload, raw);
  }

  /**
   * @param payload
   * @param raw
   */
  async commonHandle(payload: RabbitMQMessage, raw: ConsumeMessage) {
    const workerEvents = await this.workerEventManager.getAllEvents();
    if (!workerEvents.has(payload.event)) {
      this.logger.log(`Event ${payload.event} not supported`);
      return;
    }

    const workerEventHandler = <WorkerHandler>workerEvents.get(payload.event);

    if (!workerEventHandler || !workerEventHandler.handle || typeof workerEventHandler.handle != 'function') {
      this.logger.error(`Event ${payload.event} not have a handler`);
      return;
    }

    const header = raw.properties.headers as MessageHeader;
    if (process.env.API_FW_PRINCIPAL_KEY) {
      header['x-api-fw-principal-key'] = process.env.API_FW_PRINCIPAL_KEY || '';
    }

    const content = payload.content;
    // if validate error throw exception
    const validated = await workerEventHandler.validateTask(content);
    if (!validated) {
      return;
    }

    return await workerEventHandler.handle(content, header);
  }
}
