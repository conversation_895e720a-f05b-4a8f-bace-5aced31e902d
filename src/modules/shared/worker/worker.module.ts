import { Module } from '@nestjs/common';
import { SyncModule } from '@src/modules/shared/sync/sync.module';
import { CuramaWorkerModule } from '@vietnam/curama-worker';
import { Worker } from '@src/modules/shared/worker/workers/worker';
import { UpsertAdminTeamStore } from '@modules/shared/worker/workers/tasks/upsert-admin-team-store';
import { CreateAdminInfoForNewStore } from '@modules/shared/worker/workers/tasks/create-admin-info-for-new-store';
import { AdminModule } from '../admin/admin.module';
import { TeamModule } from '../team/team.module';
import { AdminCreated } from './workers/tasks/admin-created';
import { TeamUpserted } from './workers/tasks/team-upserted';
import { StoreChanged } from './workers/tasks/store-changed';
import { UpdateAdminTeamStore } from './workers/tasks/update-admin-team-store';
import { CuramaOpensearchModule, OpenSearchConfig } from '@vietnam/curama-opensearch';
import { ConfigService } from '@nestjs/config';
import { SearchModule } from '@modules/shared/search/search.module';
import { QueueProvider } from '@providers/queue.provider';
import { BullMQProvider } from '@providers/bullmq.provider';
import { ConsultingMessageCreated } from './workers/tasks/consulting-message-created';
import { RemoveAdminFromStore } from './workers/tasks/remove-admin-from-store';
import { MessageBroadcastProcessor } from './processors/message-broadcast.processor';
import { NotificationModule } from '@modules/shared/notification/notification.module';
import { AttachmentModule } from '@modules/shared/attachment/attachment.module';
import { BULLMQ_MESSAGE_BROADCAST_QUEUE } from '@src/common/constants';
import { RedisProvider } from '@providers/redis.provider';
import { BroadcastMessageModule } from '@modules/shared/broadcast-message/broadcast-message.module';
import { ScheduledBroadcastMessage } from '@modules/shared/worker/workers/tasks/scheduled-broadcast-message';
import { HandleCsvCheckStore } from './workers/tasks/handle-csv-upload-broadcast-message';
import { InquiryThreadModule } from '@modules/shared/inquiry-thread/inquiry-thread.module';
import { CreateReportBroadcastMessage } from './workers/tasks/create-report-broadcast-message';
import { MessageBroadcastEventsListener } from './listeners/message-broadcast-events.listener';

@Module({
  imports: [
    SyncModule,
    CuramaWorkerModule,
    AdminModule,
    TeamModule,
    NotificationModule,
    AttachmentModule,
    BroadcastMessageModule,
    InquiryThreadModule,
    SearchModule,
    RedisProvider.register(),
    QueueProvider.register(),
    BullMQProvider.register(),
    BullMQProvider.forFeature(BULLMQ_MESSAGE_BROADCAST_QUEUE),
    CuramaOpensearchModule.forRootAsync({
      useFactory: async (configService: ConfigService) => {
        const opensearchConfig = configService.get<OpenSearchConfig>('opensearch');
        if (!opensearchConfig) throw new Error('Opensearch config not found');
        return opensearchConfig;
      },
      inject: [ConfigService],
    }),
  ],
  providers: [
    Worker,
    UpsertAdminTeamStore,
    CreateAdminInfoForNewStore,
    AdminCreated,
    TeamUpserted,
    StoreChanged,
    MessageBroadcastProcessor,
    MessageBroadcastEventsListener,
    ScheduledBroadcastMessage,
    HandleCsvCheckStore,
    CreateReportBroadcastMessage,
    RemoveAdminFromStore,
    UpdateAdminTeamStore,
    ConsultingMessageCreated,
  ],
})
export class WorkerModule {}
