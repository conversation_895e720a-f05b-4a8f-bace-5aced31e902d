import { QueueEventsListener, QueueEventsHost, OnQueueEvent } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { CONCURRENT_MESSAGE_MQ_NAME, BULLMQ_MESSAGE_BROADCAST_QUEUE } from '@src/common/constants';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

@QueueEventsListener(BULLMQ_MESSAGE_BROADCAST_QUEUE)
export class MessageBroadcastEventsListener extends QueueEventsHost {
  private readonly logger = new Logger(MessageBroadcastEventsListener.name);

  constructor(
    private readonly amqpConnection: AmqpConnection,
    @InjectQueue(BULLMQ_MESSAGE_BROADCAST_QUEUE) private readonly messageBroadcastQueue: Queue,
  ) {
    super();
  }

  /**
   * Handle job completion events
   * This is triggered when a job successfully completes
   */
  @OnQueueEvent('completed')
  async onJobCompleted({
    jobId,
    returnvalue: _returnvalue,
    prev: _prev,
  }: {
    jobId: string;
    returnvalue: string;
    prev?: string;
  }) {
    try {
      this.logger.log(`Job completed: ${jobId}`);

      // Extract broadcast message ID from job ID pattern: ${broadcastMessageId}-batch-${batchIndex}
      const broadcastMessageId = this.extractBroadcastMessageId(jobId);
      if (!broadcastMessageId) {
        this.logger.warn(`Could not extract broadcast message ID from job ID: ${jobId}`);
        return;
      }

      // Get the job to access its data (headers)
      const job = await this.messageBroadcastQueue.getJob(jobId);
      if (!job || !job.data) {
        this.logger.warn(`Could not retrieve job data for job ID: ${jobId}`);
        return;
      }

      // Check if all jobs for this broadcast are complete
      await this.checkBroadcastCompletion(broadcastMessageId, job.data.headers);
    } catch (error) {
      this.logger.error(`Error handling job completion for job ${jobId}:`, error);
    }
  }

  /**
   * Handle job failure events
   * This is triggered when a job fails
   */
  @OnQueueEvent('failed')
  async onJobFailed({ jobId, failedReason, prev: _prev }: { jobId: string; failedReason: string; prev?: string }) {
    try {
      this.logger.error(`Job failed: ${jobId}, reason: ${failedReason}`);

      // Extract broadcast message ID from job ID
      const broadcastMessageId = this.extractBroadcastMessageId(jobId);
      if (!broadcastMessageId) {
        this.logger.warn(`Could not extract broadcast message ID from failed job ID: ${jobId}`);
        return;
      }

      // Get the job to access its data (headers)
      const job = await this.messageBroadcastQueue.getJob(jobId);
      if (!job || !job.data) {
        this.logger.warn(`Could not retrieve job data for failed job ID: ${jobId}`);
        return;
      }

      // Check if all jobs for this broadcast are complete (including failed ones)
      await this.checkBroadcastCompletion(broadcastMessageId, job.data.headers);
    } catch (error) {
      this.logger.error(`Error handling job failure for job ${jobId}:`, error);
    }
  }

  /**
   * Extract broadcast message ID from job ID pattern
   * Job ID format: ${broadcastMessageId}-batch-${batchIndex}
   */
  private extractBroadcastMessageId(jobId: string): string | null {
    const match = jobId.match(/^(.+)-batch-\d+$/);
    return match ? match[1] : null;
  }

  /**
   * Check if all jobs for a broadcast are complete and trigger report creation if needed
   */
  private async checkBroadcastCompletion(broadcastMessageId: string, headers: any): Promise<void> {
    try {
      // Get job counts using BullMQ job tracking
      const { completedJobs, failedJobs, totalJobs } = await this.getBroadcastJobCounts(broadcastMessageId);
      const processedJobs = completedJobs + failedJobs;

      if (totalJobs === 0) {
        this.logger.log(`No jobs found for broadcast ${broadcastMessageId}`);
        return;
      }

      this.logger.log(
        `Broadcast ${broadcastMessageId}: ${processedJobs}/${totalJobs} jobs processed (${completedJobs} completed, ${failedJobs} failed)`,
      );

      // Check if all jobs have been processed
      if (processedJobs >= totalJobs) {
        this.logger.log(`All jobs processed for broadcast ${broadcastMessageId}. Triggering report creation.`);

        // Trigger report creation
        await this.amqpConnection.publish(
          'taskManager',
          CONCURRENT_MESSAGE_MQ_NAME,
          {
            event: RabbitMQEventType.CreateReportBroadcastMessage,
            content: { broadcastMessageId },
          },
          { headers },
        );
      }
    } catch (error) {
      this.logger.error(`Error checking broadcast completion for ${broadcastMessageId}:`, error);
    }
  }

  /**
   * Get job counts for a specific broadcast using BullMQ job tracking.
   * Uses the job ID pattern: ${broadcastMessageId}-batch-${batchIndex}
   */
  private async getBroadcastJobCounts(broadcastMessageId: string): Promise<{
    completedJobs: number;
    failedJobs: number;
    totalJobs: number;
  }> {
    try {
      // Get completed and failed jobs
      const [completedJobsData, failedJobsData] = await Promise.all([
        this.messageBroadcastQueue.getJobs(['completed'], 0, -1),
        this.messageBroadcastQueue.getJobs(['failed'], 0, -1),
      ]);

      // Filter jobs that match our broadcast ID pattern
      const completedJobs = completedJobsData.filter(
        (job) => job.id && job.id.toString().startsWith(`${broadcastMessageId}-batch-`),
      ).length;

      const failedJobs = failedJobsData.filter(
        (job) => job.id && job.id.toString().startsWith(`${broadcastMessageId}-batch-`),
      ).length;

      // Get total jobs by checking all job states
      const [activeJobsData, waitingJobsData, delayedJobsData] = await Promise.all([
        this.messageBroadcastQueue.getJobs(['active'], 0, -1),
        this.messageBroadcastQueue.getJobs(['waiting'], 0, -1),
        this.messageBroadcastQueue.getJobs(['delayed'], 0, -1),
      ]);

      const activeJobs = activeJobsData.filter(
        (job) => job.id && job.id.toString().startsWith(`${broadcastMessageId}-batch-`),
      ).length;

      const waitingJobs = waitingJobsData.filter(
        (job) => job.id && job.id.toString().startsWith(`${broadcastMessageId}-batch-`),
      ).length;

      const delayedJobs = delayedJobsData.filter(
        (job) => job.id && job.id.toString().startsWith(`${broadcastMessageId}-batch-`),
      ).length;

      const totalJobs = completedJobs + failedJobs + activeJobs + waitingJobs + delayedJobs;

      return {
        completedJobs,
        failedJobs,
        totalJobs,
      };
    } catch (error) {
      this.logger.error(`Error getting job counts for broadcast ${broadcastMessageId}:`, error);
      return {
        completedJobs: 0,
        failedJobs: 0,
        totalJobs: 0,
      };
    }
  }
}
