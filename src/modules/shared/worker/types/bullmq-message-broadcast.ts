import { StoresInfo } from '@modules/terry/types/csv-record';
import { Py3CommonRequestHeadersInterface } from '@vietnam/cnga-http-request';
import { AttachmentFileUploadType } from '@common/types/attachment-file-upload.type';

export interface BullMQMessageBroadcastJobData {
  batch: StoresInfo[];
  broadcastMessageId: string;
  headers: Py3CommonRequestHeadersInterface;
  batchIndex: number;
  broadcastData: {
    content: string;
    attachments: AttachmentFileUploadType[];
  };
}
