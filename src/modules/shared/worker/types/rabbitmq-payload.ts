import { RabbitMQEventType } from '../enums/rabbitmq-event-type';
import { AdminTeamStoreMessage } from '@src/modules/shared/worker/types/admin-team-store-message';
import { UpdateAdminMessage } from './update-admin-message';
import { TeamMessage } from './team-message';
import { AdminInfoForNewStoreMessage } from './admin-info-for-new-store-message';
import { AdminDisabled } from './admin-disabled';
import { StoreChangedMessage } from './store-changed-message';
import { StoreChangedDocument } from './store-changed-document';
import { AdminChangedDocument } from './admin-changed-document';
import { ConcurrentMessage } from './concurrent-message';

export type RabbitMQMessage = {
  event: RabbitMQEventType;

  content:
    | UpdateAdminMessage
    | TeamMessage
    | AdminTeamStoreMessage
    | AdminInfoForNewStoreMessage
    | AdminDisabled
    | StoreChangedMessage
    | AdminChangedDocument
    | StoreChangedDocument
    | ConcurrentMessage;
};
