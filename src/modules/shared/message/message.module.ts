import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InquiryThreadEntity, MessageEntity } from '@src/entities';
import { MessageService } from '@src/modules/shared/message/message.service';
import { AuthModule } from '@curama-auth/auth.module';
import { AttachmentModule } from '@modules/shared/attachment/attachment.module';
import { SearchModule } from '@modules/shared/search/search.module';

@Module({
  exports: [MessageService],
  providers: [MessageService],
  imports: [TypeOrmModule.forFeature([MessageEntity, InquiryThreadEntity]), AuthModule, AttachmentModule, SearchModule],
})
export class MessageModule {}
