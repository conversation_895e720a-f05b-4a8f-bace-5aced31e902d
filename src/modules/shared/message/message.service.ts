import { HttpResponseFormatter } from '@common/response/response';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PAGINATOR } from '@src/common/constants/paginator';
import { MessageType } from '@src/common/enums/message-type';
import { InquiryThreadEntity, MessageEntity } from '@src/entities';
import { PagingMessageQueryDto } from '@src/modules/terry/dtos/paging-message.dto';
import { FindManyOptions, Repository } from 'typeorm';
import { DocumentService } from '@modules/shared/search/services/document.service';

@Injectable()
export class MessageService {
  private readonly logger = new Logger(MessageService.name);

  constructor(
    @InjectRepository(MessageEntity) private readonly messageRepo: Repository<MessageEntity>,
    @InjectRepository(InquiryThreadEntity) private readonly inquiryThreadRepo: Repository<InquiryThreadEntity>,
    private readonly documentService: DocumentService,
  ) {}

  async getPagingMessages(query: PagingMessageQueryDto) {
    try {
      const [messages, total] = await this.findAndCount({
        where: {
          threadId: query.threadId,
        },
        order: {
          createdAt: 'DESC',
        },
        take: query.limit,
        skip: query.offset,
        withDeleted: true,
        relations: ['admin', 'attachments', 'adminDelete'],
      });

      return HttpResponseFormatter.formatSuccessResponse({
        data: [
          messages.map((e) => {
            if (e.deletedAt) {
              e.content = '（このメッセージは削除されました）';
              e.attachments = [];
            }
            e.content = e.content.escape().urlize();
            return e;
          }),
          total,
        ],
      });
    } catch (err) {
      this.logger.error({ err: err.message });
      return HttpResponseFormatter.responseInternalError({ errors: err.message || err });
    }
  }

  async delete(messageId: string, adminId: string) {
    try {
      const result = await this.messageRepo.update(
        { id: messageId },
        {
          deletedAt: new Date(),
          deletedBy: adminId,
        },
      );

      try {
        await this.documentService.deleteDocumentByField('message_id', messageId);
      } catch (err) {
        this.logger.log(`Failed to delete from OpenSearch for message_id: ${messageId}`, err.message);
      }

      return HttpResponseFormatter.formatSuccessResponse({
        data: result,
      });
    } catch (err) {
      this.logger.error({ err: err.message });
      return HttpResponseFormatter.responseInternalError({ errors: err.message });
    }
  }

  findByOptions(options: FindManyOptions<MessageEntity>) {
    return this.messageRepo.find(options);
  }

  findAndCount(options: FindManyOptions<MessageEntity>) {
    return this.messageRepo.findAndCount(options);
  }

  /**
   * Get list message paginated by inquiryThread
   * @param inquiryThreadId
   * @param perPage
   * @param page
   */
  public async getByInquiryThread(
    inquiryThreadId: string,
    perPage?: number,
    page?: number,
  ): Promise<{ pagination: object; list: MessageEntity[] }> {
    if (!perPage) {
      perPage = PAGINATOR.TERRY;
    }

    const pageNumber = parseInt(String(page ? page : 1));

    const [list, total] = await this.messageRepo.findAndCount({
      where: {
        threadId: inquiryThreadId,
        type: MessageType.Message,
      },
      take: perPage,
      skip: page > 0 ? (page - 1) * perPage : 0,
      order: {
        createdAt: 'desc',
      },
      relations: ['attachments'],
      withDeleted: true,
    });

    for (const message of list) {
      message.content = message.content.escape().urlize();
      if (message.deletedAt) {
        message.content = '（このメッセージは削除されました）';
        message.attachments = [];
      }
    }

    return { list: list, pagination: { pageNumber, total, perPage } };
  }
}
