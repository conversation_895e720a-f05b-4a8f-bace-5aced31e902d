import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { HttpResponseFormatter } from '@src/common/response/response';
import { AdminEntity } from '@src/entities';
import { FindManyOptions, Repository } from 'typeorm';

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  constructor(
    @InjectRepository(AdminEntity)
    private readonly adminRepo: Repository<AdminEntity>,
  ) {}

  async getAdmins(teamId: string, select: string[] = []): Promise<any> {
    try {
      const query = this.adminRepo
        .createQueryBuilder('admin')
        .innerJoin('admin.adminsTeams', 'adminsTeams')
        .where('adminsTeams.team_id = :teamId', { teamId });
      if (select.length) {
        query.select(select.map((s) => `admin.${s}`));
      }
      const data = await query.getMany();
      return HttpResponseFormatter.responseOK({ data });
    } catch (err) {
      this.logger.error({ err: err.message });
      return HttpResponseFormatter.responseInternalError({ errors: err.message });
    }
  }

  async find(options?: FindManyOptions<AdminEntity>): Promise<AdminEntity[]> {
    try {
      return await this.adminRepo.find(options);
    } catch (err) {
      return [];
    }
  }

  async all(): Promise<AdminEntity[]> {
    try {
      return await this.adminRepo.find({
        relations: ['teams'],
      });
    } catch (err) {
      return [];
    }
  }

  async findOneWithRelation(conditions: any, relations: string[] = []): Promise<AdminEntity | undefined> {
    return await this.adminRepo.findOne({
      where: conditions,
      relations: relations,
    });
  }
}
