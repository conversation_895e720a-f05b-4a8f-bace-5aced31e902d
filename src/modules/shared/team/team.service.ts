import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TeamEntity } from '@src/entities/team.entity';
import { FindManyOptions, Repository } from 'typeorm';

@Injectable()
export class TeamService {
  constructor(
    @InjectRepository(TeamEntity)
    private readonly teamRepo: Repository<TeamEntity>,
  ) {}

  async find(options?: FindManyOptions<TeamEntity>): Promise<TeamEntity[]> {
    try {
      return await this.teamRepo.find(options);
    } catch (err) {
      return [];
    }
  }

  async findOne(options?: FindManyOptions<TeamEntity>): Promise<TeamEntity> {
    return await this.teamRepo.findOne(options);
  }
}
