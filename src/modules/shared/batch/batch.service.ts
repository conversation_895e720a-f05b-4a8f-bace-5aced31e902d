import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { LessThanOrEqual, Repository } from 'typeorm';
import * as dayjs from 'dayjs';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { BroadcastMessageEntity } from '@src/entities/broadcast-message.entity';
import { BroadcastMessageStatus, CONCURRENT_MESSAGE_MQ_NAME } from '@common/constants';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';

@Injectable()
export class BatchService {
  private readonly logger = new Logger(BatchService.name);

  constructor(
    @InjectRepository(BroadcastMessageEntity)
    private readonly broadcastMessagesRepository: Repository<BroadcastMessageEntity>,
    private readonly amqpConnection: AmqpConnection,
  ) {}

  /**
   * Runs the batch service by invoking the method to publish scheduled messages.
   * Note: processDelayMessages() removed as BullMQ handles delays internally.
   */
  async run() {
    this.logger.log('Running CronJob Batchserver');
    // await this.findSendingMessages();
    await this.publishScheduledMessages();
    this.logger.log('Finished CronJob Batchserver');
  }

  /**
   * Publishes scheduled messages that match the current hour and minute.
   * Queries the database for messages with `scheduled_send_time` matching
   * the current time and a status of `SendingScheduled`. If found, each
   * message is published.
   */
  async publishScheduledMessages() {
    try {
      const now = dayjs();
      const messagesToPublish = await this.broadcastMessagesRepository.find({
        where: {
          scheduledSendTime: LessThanOrEqual(now.toDate()),
          status: BroadcastMessageStatus.SendingScheduled,
        },
      });
      // Publish each message
      for (const message of messagesToPublish) {
        await this.publishMessage(message);
      }
    } catch (error) {
      this.logger.log('Error publishing scheduled messages', error);
      throw error;
    }
  }

  /**
   * Publishes a message to the RabbitMQ queue and updates its status.
   * After publishing the message to RabbitMQ, it updates the message's status
   * to `Sent` in the database.
   *
   * @param broadcastMessage - The broadcast message entity to publish
   */
  private async publishMessage(broadcastMessage: BroadcastMessageEntity) {
    try {
      this.logger.log(`Publishing message with ID: ${broadcastMessage.id}`);

      // Publish the message to RabbitMQ with the consulting-message routing key
      this.amqpConnection.publish('taskManager', CONCURRENT_MESSAGE_MQ_NAME, {
        event: RabbitMQEventType.ScheduledBroadcastMessage,
        content: { broadcastMessageId: broadcastMessage.id },
      });

      // Update the message status to 'Sending'
      await this.broadcastMessagesRepository.update(broadcastMessage.id, {
        status: BroadcastMessageStatus.Sending,
      });

      this.logger.log(`Broadcast message with ID: ${broadcastMessage.id} published successfully`);
    } catch (error) {
      this.logger.error(`Error publishing message with ID: ${broadcastMessage.id}`, error);
    }
  }
}
