import { Module } from '@nestjs/common';
import { BatchService } from './batch.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BroadcastMessageEntity } from '@src/entities/broadcast-message.entity';
import { QueueProvider } from '@src/providers/queue.provider';

@Module({
  imports: [TypeOrmModule.forFeature([BroadcastMessageEntity]), QueueProvider.register()],
  providers: [BatchService],
  exports: [BatchService],
})
export class BatchModule {}
