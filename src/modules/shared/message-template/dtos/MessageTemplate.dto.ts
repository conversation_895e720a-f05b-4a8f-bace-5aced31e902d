import { ApiProperty } from '@nestjs/swagger';
import { i18nValidationMessage } from 'nestjs-i18n';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { Transform } from 'class-transformer';

export class MessageTemplateDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.invalidUUID') })
  id?: string;
  @ApiProperty()
  @IsNotEmpty({ message: i18nValidationMessage('validation.isNotEmpty') })
  name: string;
  @ApiProperty()
  @IsNotEmpty({ message: i18nValidationMessage('validation.isNotEmpty') })
  content: string;
  @ApiProperty()
  largeCategoryId?: string;
  @ApiProperty()
  @IsNotEmpty({ message: i18nValidationMessage('validation.isNotEmpty') })
  subCategoryId?: string;
}
