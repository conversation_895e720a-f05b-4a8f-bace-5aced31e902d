import { Type } from 'class-transformer';
import { IsInt, IsOptional, IsString } from 'class-validator';
import { PAGINATOR } from '@src/common/constants/paginator';

export class PaginateMessageTemplateDto {
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  page: number;

  @IsOptional()
  @IsString()
  search: string;

  get offset(): number {
    return this.page > 0 ? (this.page - 1) * PAGINATOR.TEMPLATE : 0;
  }

  get pageNumber(): number {
    return parseInt(String(this.page ? this.page : 1));
  }

  get perPage(): number {
    return PAGINATOR.TEMPLATE;
  }
}
