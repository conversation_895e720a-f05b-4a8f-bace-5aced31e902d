import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MessageTemplateEntity } from '@src/entities/message-template.entity';
import { MessageTemplateGroupEntity } from '@src/entities/message-template-group.entity';
import { MessageTemplateService } from './message-template.service';
import { CuramaPy3ApiModule } from '@vietnam/curama-py3-api';

@Module({
  imports: [TypeOrmModule.forFeature([MessageTemplateGroupEntity, MessageTemplateEntity]), CuramaPy3ApiModule],
  providers: [MessageTemplateService],
  exports: [MessageTemplateService],
})
export class MessageTemplateModule {}
