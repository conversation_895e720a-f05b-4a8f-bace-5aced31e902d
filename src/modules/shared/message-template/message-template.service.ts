import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { HttpResponseFormatter } from '@src/common/response/response';
import { MessageTemplateEntity } from '@src/entities/message-template.entity';
import { MessageTemplateGroupEntity } from '@src/entities/message-template-group.entity';
import { IsNull, Repository, ILike, Not } from 'typeorm';
import { PaginateMessageTemplateDto } from './dtos/paginate-message-template.dto';
import { MessageTemplateConditionsFilter } from './types/message-template-conditions-filter';
import { MessageTemplateDto } from './dtos/MessageTemplate.dto';
import { Request } from 'express';

@Injectable()
export class MessageTemplateService {
  constructor(
    @InjectRepository(MessageTemplateEntity)
    private readonly messageTemplateRepo: Repository<MessageTemplateEntity>,
    @InjectRepository(MessageTemplateGroupEntity)
    private readonly messageTemplateGroupRepo: Repository<MessageTemplateGroupEntity>,
  ) {}
  async getLargeCategories() {
    try {
      return await this.messageTemplateGroupRepo.find({ where: { parentId: IsNull() }, order: { sort: 'ASC' } });
    } catch (err) {
      return [];
    }
  }

  async getAllSubCategories() {
    try {
      return await this.messageTemplateGroupRepo.find({
        where: { parentId: Not(IsNull()) },
        relations: ['groupParent'],
        order: { sort: 'ASC' },
      });
    } catch (err) {
      return [];
    }
  }

  async getSubCategories(parentId: string) {
    try {
      const data = await this.messageTemplateGroupRepo.find({ where: { parentId }, order: { sort: 'ASC' } });
      return HttpResponseFormatter.responseOK({ data });
    } catch (err) {
      return HttpResponseFormatter.responseInternalError({ errors: err });
    }
  }
  async getTemplates(groupId?: string, search?: string) {
    try {
      const data = await this.messageTemplateRepo.find({
        where: {
          ...(groupId && { groupId }),
          ...(search && { name: ILike(`%${search.replace(/%/g, '\\%').replace(/_/g, '\\_')}%`) }),
        },
        order: { sort: 'ASC' },
      });
      return HttpResponseFormatter.responseOK({ data });
    } catch (err) {
      return HttpResponseFormatter.responseInternalError({ errors: err });
    }
  }

  async getMessageTemplateGroupByCategory(search?: string) {
    try {
      // Fetch all templates
      const templates = await this.messageTemplateRepo.find({
        where: {
          ...(search && { name: ILike(`%${search.replace(/%/g, '\\%').replace(/_/g, '\\_')}%`) }),
        },
        order: { sort: 'ASC' },
      });

      // return list of templates if search is provided (skip the tree structure)
      if (search) {
        return HttpResponseFormatter.responseOK({ data: templates });
      }

      // Fetch all large categories
      const largeCategories = await this.messageTemplateGroupRepo.find({
        where: { parentId: IsNull() },
        order: { sort: 'ASC' },
      });

      // Fetch all subcategories
      const subCategories = await this.messageTemplateGroupRepo.find({
        where: { parentId: Not(IsNull()) },
        order: { sort: 'ASC' },
      });

      // Initialize the tree structure
      const tree = largeCategories
        .map((largeCategory) => {
          const subCategoryTree = subCategories
            .filter((subCategory) => subCategory.parentId === largeCategory.id)
            .map((subCategory) => {
              const subCategoryTemplates = templates.filter((template) => template.groupId === subCategory.id);

              return {
                ...subCategory,
                templates: subCategoryTemplates,
              };
            })
            .filter((subCategory) => subCategory !== null);

          return {
            ...largeCategory,
            subCategories: subCategoryTree,
          };
        })
        .filter((largeCategory) => largeCategory !== null);

      return HttpResponseFormatter.responseOK({ data: tree });
    } catch (err) {
      return HttpResponseFormatter.responseInternalError({ errors: err });
    }
  }

  /**
   * Get all message template
   * @param paginateMessageTemplateDto: PaginateMessageTemplateDto
   */
  public async getAll(
    paginateMessageTemplateDto: PaginateMessageTemplateDto,
    request: Request,
  ): Promise<{
    messageTemplate: MessageTemplateEntity[];
    pagination: any;
    listLargeCategory: MessageTemplateGroupEntity[];
    listSubCategory: MessageTemplateGroupEntity[];
  }> {
    const { search, offset, pageNumber, perPage } = paginateMessageTemplateDto;

    // For condition where
    const condition: MessageTemplateConditionsFilter = {};
    if (search) {
      condition.name = ILike(`%${search.replace(/%/g, '\\%').replace(/_/g, '\\_')}%`);
    }

    // For sort
    const ordersBy: any = {
      updatedAt: 'DESC',
    };

    const [[messageTemplate, total], listLargeCategory, listSubCategory] = await Promise.all([
      this.messageTemplateRepo.findAndCount({
        where: Object.keys(condition).length > 0 ? [condition] : {},
        take: perPage,
        skip: offset,
        order: ordersBy,
        relations: ['groupParent', 'groupParent.groupParent'],
      }),
      this.getLargeCategories(),
      this.getAllSubCategories(),
    ]);
    return {
      messageTemplate,
      pagination: { perPage, pageNumber, total, request: { query: request.query, path: request.path } },
      listLargeCategory,
      listSubCategory,
    };
  }

  /**
   * Save message template
   * @param messageTemplateDto: MessageTemplateDto
   */
  public async save(messageTemplateDto: MessageTemplateDto) {
    try {
      const { name, content, subCategoryId, id } = messageTemplateDto;

      if (id) {
        return await this.update(messageTemplateDto);
      }

      const messageTemplate = new MessageTemplateEntity();
      messageTemplate.name = name;
      messageTemplate.content = content;
      messageTemplate.groupId = subCategoryId;

      return await this.messageTemplateRepo.save(messageTemplate);
    } catch (err) {
      return { success: false, error: err };
    }
  }

  /**
   * Find message template by id
   * @param id: string
   */
  public async getById(id: string, relations: string[] = []): Promise<MessageTemplateDto | undefined> {
    return await this.messageTemplateRepo.findOne({
      where: { id },
      relations: relations,
    });
  }

  /**
   * Update message template
   * @param messageTemplateDto: MessageTemplateDto
   * @param id: string
   */
  public async update(messageTemplateDto: MessageTemplateDto) {
    const { name, content, subCategoryId, id } = messageTemplateDto;
    try {
      const messageTemplate = await this.messageTemplateRepo.findOne({
        where: { id },
      });
      if (!messageTemplate) {
        throw new Error('Message template not found');
      }

      messageTemplate.name = name;
      messageTemplate.content = content;
      messageTemplate.groupId = subCategoryId;
      messageTemplate.updatedAt = new Date();

      return await this.messageTemplateRepo.save(messageTemplate);
    } catch (err) {
      return { success: false, error: err };
    }
  }

  /**
   * Delete message template
   * @param messageTemplateId: string
   */
  public async delete(messageTemplateId: string) {
    try {
      const result = await this.messageTemplateRepo.delete({ id: messageTemplateId });
      return HttpResponseFormatter.formatSuccessResponse({
        data: result,
      });
    } catch (err) {
      return HttpResponseFormatter.responseInternalError({ errors: err.message });
    }
  }
}
