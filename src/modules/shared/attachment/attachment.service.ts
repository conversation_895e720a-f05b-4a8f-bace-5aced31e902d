import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { AttachmentType } from '@src/common/enums/attachment-type';
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { randomUUID } from 'crypto';
import * as sharp from 'sharp';
import { AttachmentFileUploadType } from '@common/types/attachment-file-upload.type';

@Injectable()
export class AttachmentService {
  private readonly logger = new Logger(AttachmentService.name);

  async uploadFile(
    file: Express.Multer.File,
    relationId: string,
    threadId: string,
    fileName?: string,
  ): Promise<AttachmentFileUploadType> {
    const newId = randomUUID();
    let ext = file.mimetype.split('/')[1];
    const type = file.mimetype.startsWith('image') ? AttachmentType.Image : AttachmentType.Pdf;
    if (ext === 'png') {
      file.buffer = await sharp(file.buffer).webp().toBuffer();
      ext = 'webp';
      file.mimetype = 'image/webp';
    }

    const fileBaseName = fileName ? fileName : newId;
    const path = `consulting-message/${threadId}/${fileBaseName}.${ext}`;
    const s3Client = new S3Client();
    const s3PutCommand = new PutObjectCommand({
      Bucket: process.env.CONSULTING_MESSAGE_S3_BUCKET_NAME,
      Key: path,
      Body: file.buffer,
      ContentType: file.mimetype,
    });

    try {
      await s3Client.send(s3PutCommand);
      return {
        id: newId,
        path: path,
        relationId,
        attachmentType: type,
      };
    } catch (e) {
      this.logger.log(e);
      throw new InternalServerErrorException('UPLOAD_FILE_ERROR');
    }
  }
}
