import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AttachmentEntity } from '@src/entities';
import { AttachmentService } from '@modules/shared/attachment/attachment.service';
import { ApiClientModule } from '@vietnam/cnga-http-request';

@Module({
  imports: [TypeOrmModule.forFeature([AttachmentEntity]), ApiClientModule.forRoot()],
  providers: [AttachmentService],
  exports: [AttachmentService],
})
export class AttachmentModule {}
