import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, Repository } from 'typeorm';
import { WorkTypeEntity } from '../../../entities/work-type.entity';

@Injectable()
export class WorkTypeService {
  private readonly logger = new Logger(WorkTypeService.name);
  constructor(
    @InjectRepository(WorkTypeEntity)
    private readonly workTypeRepository: Repository<WorkTypeEntity>,
  ) {}

  async getById(id: string, relations: string[] = []) {
    return await this.workTypeRepository.findOne({
      where: { id },
      relations: relations,
    });
  }

  async find(options?: FindManyOptions<WorkTypeEntity>) {
    try {
      return await this.workTypeRepository.find(options);
    } catch (err) {
      return [];
    }
  }
}
