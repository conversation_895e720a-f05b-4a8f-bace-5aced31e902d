import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager, FindManyOptions, Not, Repository } from 'typeorm';
import { TasksQueryRequest } from './dtos/tasks-query-request.do';
import { Request } from 'express';
import { TaskStatusMapping } from '../../../common/constants';
import { HttpResponseFormatter } from '@src/common/response/response';
import { TeamService } from '../team/team.service';
import { AdminService } from '../../shared/admin/admin.service';
import { WorkTypeService } from '../work-type/work-type.service';
import { PagingTaskQueryDto } from '@src/modules/terry/dtos/paging-task.dto';
import { TaskStatus } from '@src/common/enums/task-status';
import { InquiryThreadService } from '../inquiry-thread/inquiry-thread.service';
import { UpsertTaskDto } from './dtos/upsert-task.dto';
import { MessageType } from '@src/common/enums/message-type';
import { MessageEntity, TaskEntity } from '@src/entities';
import { formatDateWithComparisonYear } from '@src/common/utils/date-util';
import { PaginationTasks } from './dtos/pagination-tasks.dto';
import { SessionAdmin } from '@curama-auth/sessions/session-admin';

@Injectable()
export class TaskService {
  private readonly logger = new Logger(TaskService.name);
  constructor(
    @InjectRepository(TaskEntity)
    private readonly taskRepository: Repository<TaskEntity>,
    private readonly teamService: TeamService,
    private readonly adminService: AdminService,
    private readonly workTypeService: WorkTypeService,
    private readonly dataSource: DataSource,
    private readonly inquiryThreadService: InquiryThreadService,
  ) {}

  /**
   * Get all message template
   * @param searchQuery: TasksQueryRequest
   */
  public async getTaskWithPaginate(searchQuery: TasksQueryRequest): Promise<{
    tasks: TaskEntity[];
    pagination: any;
  }> {
    const {
      storeId,
      assignmentTeamId,
      assignmentAdminId,
      workTypeId,
      status,
      nextConfirmationDate,
      updatedAt,
      offset,
      pageNumber,
      perPage,
      orderNextConfirmationDate,
      sortByNextConfirmationDate,
    } = searchQuery;

    let queryBuilder = this.taskRepository.createQueryBuilder('t');
    queryBuilder.leftJoinAndSelect('t.assignmentAdmin', 'assignmentAdmin');
    queryBuilder.leftJoinAndSelect('t.assignmentTeam', 'assignmentTeam');
    queryBuilder.leftJoinAndSelect('t.workType', 'workType');
    queryBuilder.leftJoinAndSelect('t.thread', 'thread');

    queryBuilder.orderBy('t.updatedAt', 'DESC');

    if (storeId) {
      queryBuilder = queryBuilder.andWhere('thread.store_id::text = :storeId', { storeId });
    }

    if (assignmentTeamId) {
      queryBuilder = queryBuilder.andWhere('t.assignmentTeamId = :assignmentTeamId', { assignmentTeamId });
    }

    if (assignmentAdminId) {
      queryBuilder = queryBuilder.andWhere('t.assignmentAdminId = :assignmentAdminId', { assignmentAdminId });
    }

    if (workTypeId) {
      queryBuilder = queryBuilder.andWhere('t.workTypeId = :workTypeId', { workTypeId });
    }

    if (status) {
      queryBuilder = queryBuilder.andWhere('t.status = :status', { status });
    }

    if (nextConfirmationDate) {
      queryBuilder = queryBuilder.andWhere('t.nextConfirmationDate = :nextConfirmationDate', { nextConfirmationDate });
    }

    if (updatedAt) {
      queryBuilder = queryBuilder.andWhere(`TO_CHAR(t.updated_at, 'YYYY-MM-DD') = :updatedAt`, { updatedAt });
    }

    if (orderNextConfirmationDate) {
      queryBuilder = queryBuilder.orderBy('t.nextConfirmationDate', sortByNextConfirmationDate as 'ASC' | 'DESC');
    }

    const total = await queryBuilder.getCount();
    if (perPage && pageNumber) {
      queryBuilder = queryBuilder.skip(offset).take(perPage);
    }

    const tasks = await queryBuilder.getMany();

    return {
      tasks,
      pagination: {
        perPage: perPage,
        pageNumber: pageNumber,
        total,
      },
    };
  }

  public async getListOfTask(searchQuery: TasksQueryRequest, request: Request): Promise<object> {
    try {
      const tasks = await this.getTaskWithPaginate(searchQuery);
      tasks.pagination = {
        ...tasks.pagination,
        request: { query: request.query, path: request.path },
      };

      return new PaginationTasks(tasks, request);
    } catch (err) {
      this.logger.error({ err: err.message });
      return new PaginationTasks({ tasks: [], request });
    }
  }

  findAndCount(options: FindManyOptions<TaskEntity>) {
    return this.taskRepository.findAndCount(options);
  }
  countTotal(threadId: string) {
    return this.taskRepository.countBy({ threadId });
  }
  async upsertTask(data: UpsertTaskDto, adminId: string) {
    const {
      id,
      workTypeId,
      assignmentTeamId,
      assignmentAdminId,
      nextConfirmationDate,
      memo,
      storeId,
      status,
      threadType,
      storeName,
    } = data;
    const thread = await this.inquiryThreadService.getOrCreateThread(storeId, storeName, Number(threadType));
    const taskNo = await this.generateTaskNo(thread.id);

    try {
      return await this.dataSource.transaction(async (entityManager: EntityManager) => {
        let task;
        let workType;
        let content;
        if (id) {
          task = await entityManager.findOneBy(TaskEntity, { id: id });
          await entityManager.update(
            TaskEntity,
            { id: id },
            {
              workTypeId: task.workTypeId,
              memo: memo ?? '',
              assignmentAdminId: assignmentAdminId || null,
              assignmentTeamId: assignmentTeamId,
              status: Number(status),
              taskNo: task.taskNo,
              nextConfirmationDate: nextConfirmationDate || null,
              updatedBy: adminId,
            },
          );

          const taskUpdated = await entityManager.findOne(TaskEntity, {
            where: { id: id },
            relations: ['workType'],
          });

          content = `タスク${taskUpdated.taskNo}を変更しました。\n業務：${taskUpdated.workType.name} \nステータス：${
            TaskStatusMapping[status]
          } \n次回確認日：${formatDateWithComparisonYear(taskUpdated.nextConfirmationDate) ?? ''} \nメモ：`;
          if (taskUpdated.memo) {
            content += `\n${taskUpdated.memo}`;
          }
        } else {
          const newTask = entityManager.create(TaskEntity, {
            threadId: thread.id,
            workTypeId: workTypeId,
            memo: memo,
            assignmentAdminId: assignmentAdminId || null,
            assignmentTeamId: assignmentTeamId,
            status: Number(status),
            taskNo: taskNo,
            nextConfirmationDate: nextConfirmationDate || null,
            createdBy: adminId,
            updatedBy: adminId,
          });

          task = await entityManager.save(newTask);
          workType = await this.workTypeService.getById(workTypeId);
          content = `タスク${task.taskNo}を作成しました。\n業務：${workType.name} \nステータス：${
            TaskStatusMapping[status]
          } \n次回確認日：${formatDateWithComparisonYear(task.nextConfirmationDate) ?? ''} \nメモ：`;
          if (task.memo) {
            content += `\n${task.memo}`;
          }
        }

        // create new record of message table
        const message = entityManager.create(MessageEntity, {
          threadId: thread.id,
          content: content,
          type: MessageType.Memo,
          fromAdminId: adminId,
        });
        await entityManager.save(message);

        return { success: true };
      });
    } catch (err) {
      return { success: false };
    }
  }

  async getDataForCreate(user: SessionAdmin): Promise<object> {
    try {
      const teamId = user?.teams[0]?.id;
      const { status, teams, workTypes, admins } = await this.getStaticDataForUpsert();

      const adminFilter = admins.filter((admin) => {
        return admin.teams.some((team) => team.id === teamId);
      });

      return HttpResponseFormatter.formatSuccessResponse({
        data: {
          status,
          teams,
          workTypes,
          admins: adminFilter,
          teamId,
          adminId: user.id,
        },
      });
    } catch (err) {
      this.logger.error({ err: err.message });
      return HttpResponseFormatter.responseInternalError({ errors: err.message });
    }
  }

  async getDataForUpdate(taskId: string): Promise<object> {
    try {
      const task = await this.taskRepository.findOne({
        where: { id: taskId },
        relations: ['workType'],
      });

      const { status, teams, workTypes, admins } = await this.getStaticDataForUpsert();

      const adminFilter = admins.filter((admin) => {
        return admin.teams.some((e) => e.id === task?.assignmentTeamId) || task?.assignmentAdminId === admin.id;
      });

      return {
        task: task ?? null,
        status,
        teams,
        workTypes,
        admins: adminFilter,
      };
    } catch (err) {
      this.logger.error({ err: err.message });
      return HttpResponseFormatter.responseInternalError({ errors: err.message || err });
    }
  }

  async generateTaskNo(threadId: string): Promise<number> {
    const maxTaskNo = await this.taskRepository
      .createQueryBuilder('t')
      .select('MAX(t.taskNo)', 'maxTaskNo')
      .where('t.threadId = :threadId', { threadId })
      .getRawOne();

    return maxTaskNo.maxTaskNo ? maxTaskNo.maxTaskNo + 1 : 1;
  }
  async getPagingTasks(query: PagingTaskQueryDto) {
    try {
      const [tasks, total] = await this.findAndCount({
        where: {
          threadId: query.threadId,
          ...(!JSON.parse(query.withCompleted) && { status: Not(TaskStatus.Finish) }),
        },
        order: {
          updatedAt: 'DESC',
        },
        take: query.limit,
        skip: query.offset,
        relations: ['workType', 'assignmentTeam', 'assignmentAdmin'],
      });

      return HttpResponseFormatter.formatSuccessResponse({
        data: [
          tasks.map((e) => {
            e.memo = e.memo?.escape().urlize();
            return e;
          }),
          total,
        ],
      });
    } catch (err) {
      this.logger.error({ err: err.message });
      return HttpResponseFormatter.responseInternalError({ errors: err.message || err });
    }
  }

  async getStaticDataForUpsert() {
    const [status, teams, workTypes, admins] = await Promise.all([
      Object.entries(TaskStatusMapping).map((e) => ({ id: e[0], name: e[1] })),
      this.teamService.find({ where: { isLast: true }, select: ['id', 'name'] }),
      this.workTypeService.find({ select: ['id', 'name'] }),
      this.adminService.find({ select: ['id', 'name'], relations: ['teams'] }),
    ]);

    return {
      status,
      teams,
      workTypes,
      admins,
    };
  }
}
