import { Modu<PERSON> } from '@nestjs/common';
import { TaskService } from './task.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TaskEntity } from '@src/entities/task.entity';
import { CuramaPy3ApiModule } from '@vietnam/curama-py3-api';
import { TeamModule } from '../team/team.module';
import { AdminModule } from '../../shared/admin/admin.module';
import { WorkTypeModule } from '../work-type/work-type.module';
import { InquiryThreadModule } from '../inquiry-thread/inquiry-thread.module';

@Module({
  imports: [
    CuramaPy3ApiModule,
    TeamModule,
    AdminModule,
    WorkTypeModule,
    InquiryThreadModule,
    TypeOrmModule.forFeature([TaskEntity]),
  ],
  providers: [TaskService],
  exports: [TaskService],
})
export class TaskModule {}
