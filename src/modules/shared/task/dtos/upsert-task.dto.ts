import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsUUID } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';

export class UpsertTaskDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.invalidUUID') })
  id: string;

  @ApiProperty()
  workTypeId: string;

  @ApiProperty()
  status: number;

  @ApiProperty()
  assignmentTeamId: string;

  @ApiProperty()
  assignmentAdminId: string;

  @ApiProperty()
  nextConfirmationDate: string;

  @ApiProperty()
  memo: string;

  @ApiProperty()
  storeId: string;

  @ApiProperty()
  storeName: string;

  @ApiProperty()
  threadType: number;
}
