import { Transform, Type } from 'class-transformer';
import { IsIn, IsInt, IsOptional, IsString, IsUUID } from 'class-validator';
import { PAGINATOR } from '@src/common/constants/paginator';
import { i18nValidationMessage } from 'nestjs-i18n';
import { TaskStatus } from '@src/common/enums/task-status';
import { SORT } from '@src/common/constants/sort';

const getTypeSort = (type: number): string => {
  switch (type) {
    case (type = SORT.ASCENDING):
      return 'ASC';
    case (type = SORT.DESCENDING):
      return 'DESC';
  }
};

export class TasksQueryRequest {
  @IsOptional()
  @IsString()
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.invalidUUID') })
  storeId?: string;

  @IsOptional()
  @IsString()
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.formatUUID') })
  assignmentTeamId?: string;

  @IsOptional()
  @IsString()
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.formatUUID') })
  assignmentAdminId?: string;

  @IsOptional()
  @IsString()
  @Transform((data) => (data.value === '' ? undefined : data.value))
  @IsUUID('4', { message: i18nValidationMessage('validation.formatUUID') })
  workTypeId?: string;

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Transform((data) => (!data.value ? undefined : parseInt(data.value)))
  @IsIn(Object.values(TaskStatus))
  status?: number;

  @IsOptional()
  @IsString()
  nextConfirmationDate?: string;

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  orderNextConfirmationDate?: number;

  @IsOptional()
  @IsString()
  updatedAt?: string;

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  page?: number;

  get offset(): number {
    return this.page > 0 ? (this.page - 1) * PAGINATOR.TERRY : 0;
  }

  get pageNumber(): number {
    return parseInt(String(this.page ? this.page : 1));
  }

  get perPage(): number {
    return PAGINATOR.TERRY;
  }

  get sortByNextConfirmationDate(): string {
    return getTypeSort(this.orderNextConfirmationDate);
  }
}
