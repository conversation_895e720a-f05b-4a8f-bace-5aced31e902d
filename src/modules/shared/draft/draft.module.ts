import { Modu<PERSON> } from '@nestjs/common';
import { DraftService } from './draft.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DraftEntity } from '@src/entities/draft.entity';
import { InquiryThreadModule } from '../inquiry-thread/inquiry-thread.module';
import { NgWordModule } from '../ng-word/ng-word.module';
import { AuthModule } from '@curama-auth/auth.module';
import { AttachmentModule } from '../attachment/attachment.module';
import { NotificationModule } from '../notification/notification.module';
import { CuramaPy3ApiModule } from '@vietnam/curama-py3-api';
import { QueueProvider } from '@providers/queue.provider';

@Module({
  imports: [
    TypeOrmModule.forFeature([DraftEntity]),
    AuthModule,
    InquiryThreadModule,
    NgWordModule,
    AttachmentModule,
    NotificationModule,
    CuramaPy3ApiModule,
    QueueProvider.register(),
  ],
  providers: [DraftService],
  exports: [DraftService],
})
export class DraftModule {}
