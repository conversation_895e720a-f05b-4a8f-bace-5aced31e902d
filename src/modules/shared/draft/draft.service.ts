import { Injectable, Logger } from '@nestjs/common';
import { AttachmentEntity, InquiryThreadEntity, MessageEntity } from '@src/entities';
import { DraftEntity } from '@src/entities/draft.entity';
import { DataSource, EntityManager } from 'typeorm';
import { UpsertDraftDto } from './dtos/upsert-draft.dto';
import { InquiryThreadService } from '../inquiry-thread/inquiry-thread.service';
import { NgWordService } from '../ng-word/ng-word.service';
import { HttpResponseFormatter } from '@src/common/response/response';
import { MessageType } from '@src/common/enums/message-type';
import { StoppedStoreStatuses, SYNC_OPENSEARCH_MESSAGE_MQ_TASK, ThreadStatus } from '@src/common/constants';
import { AttachmentService } from '../attachment/attachment.service';
import { NotificationService } from '../notification/notification.service';
import { randomUUID } from 'crypto';
import { Request } from 'express';
import * as dayjs from 'dayjs';
import { StoreDetailDto } from '@vietnam/curama-py3-api';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';
import { getLatestMessageCacheKey } from '@src/common/utils/helper';
import { debug } from '@src/common/utils/debugger';

@Injectable()
export class DraftService {
  private readonly logger = new Logger(DraftService.name);
  constructor(
    private readonly dataSource: DataSource,
    private readonly inquiryThreadService: InquiryThreadService,
    @InjectRedis() private readonly redis: Redis,
    private readonly ngWordService: NgWordService,
    private readonly attachmentService: AttachmentService,
    private readonly notificationService: NotificationService,
    private readonly amqpConnection: AmqpConnection,
  ) {}
  async delete(draftId: string) {
    try {
      const thread = await this.inquiryThreadService.getByCondition({ draftId });
      return await this.dataSource.transaction(async (entityManager: EntityManager) => {
        await Promise.all([
          entityManager.delete(AttachmentEntity, { relationId: draftId }),
          entityManager.update(InquiryThreadEntity, { draftId }, { draftId: null, updatedAt: thread?.updatedAt }),
          entityManager.delete(DraftEntity, { id: draftId }),
        ]);
        return { success: true };
      });
    } catch (err) {
      return { success: false };
    }
  }

  async upsertDraft(files: Express.Multer.File[], data: UpsertDraftDto, adminId: string, store: StoreDetailDto) {
    const canSendMsg = !StoppedStoreStatuses.includes(store.status_id);
    if (!canSendMsg) {
      return HttpResponseFormatter.responseConflict({ errors: 'Can not send message to stopped store' });
    }

    const { uploadedFile, status, storeId, draftId, content, threadType, storeName } = data;

    if (content) {
      if (content.length > 2000) {
        return HttpResponseFormatter.responseBadRequest({ errors: 'Please enter 2000 characters or less.' });
      }
      const ngWords = await this.ngWordService.checkNGWord(content);
      if (!!ngWords.length) {
        return HttpResponseFormatter.responseBadRequest({ errors: 'NG_WORD_ERROR', meta: ngWords });
      }
    }
    const uploadedFileIds = JSON.parse(uploadedFile)?.map((e) => e.id);
    try {
      let currentDraftId = draftId;
      if (!currentDraftId) {
        currentDraftId = randomUUID();
      }
      const thread = await this.inquiryThreadService.getOrCreateThread(storeId, storeName, Number(threadType));
      const response = await this.dataSource.transaction(async (entityManager: EntityManager) => {
        let draft;
        if (draftId) {
          draft = await entityManager.findOne(DraftEntity, {
            where: { id: draftId },
          });

          const deleteAttachmentQb = entityManager
            .createQueryBuilder()
            .delete()
            .from(AttachmentEntity)
            .where('relationId = :draftId', { draftId });
          if (uploadedFileIds.length) {
            deleteAttachmentQb.andWhere('id NOT IN (:...uploadedFileIds)', {
              uploadedFileIds,
            });
          }
          await deleteAttachmentQb.execute();
          await entityManager.update(
            DraftEntity,
            { id: draftId },
            {
              content,
              status: status,
              fromAdminId: adminId,
            },
          );
        } else {
          const newDraft = entityManager.create(DraftEntity, {
            threadId: thread.id,
            content,
            status: status,
            fromAdminId: adminId,
            id: currentDraftId,
          });
          draft = await entityManager.save(newDraft);
        }
        if (files.length) {
          const uploadResponse = await Promise.all(
            files.map((file) => this.attachmentService.uploadFile(file, draft.id, draft.threadId)),
          );
          const newFiles = entityManager.create(AttachmentEntity, uploadResponse);
          await entityManager.save(newFiles);
        }
        const data = await entityManager
          .createQueryBuilder(DraftEntity, 'draft')
          .leftJoinAndSelect('draft.admin', 'admin')
          .leftJoinAndSelect('draft.attachments', 'attachments')
          .where('draft.id = :id', { id: draft.id })
          .getOne();
        return {
          success: true,
          draft: { ...data },
          threadId: thread.id,
        };
      });
      thread.draftId = currentDraftId;
      thread.updatedAt = thread.updatedAt;
      await this.inquiryThreadService.updateThreadById(thread.id, thread);
      return response;
    } catch (err) {
      return { success: false, error: err?.response?.message || err || '' };
    }
  }

  async saveDraftToMessage(draftId: string, adminId: string, request: Request) {
    try {
      let messageId: string;
      let updatedStatus = false;
      let thread: InquiryThreadEntity;

      const result = await this.dataSource.transaction(async (entityManager: EntityManager) => {
        const draft = await entityManager.findOne(DraftEntity, {
          where: { id: draftId },
        });
        thread = await entityManager.findOneBy(InquiryThreadEntity, { id: draft.threadId });
        if (thread) {
          if (draft.content) {
            const createMessageDate = dayjs().toDate();
            const newMessage = entityManager.create(MessageEntity, {
              id: draft.id,
              threadId: draft.threadId,
              content: draft.content,
              type: MessageType.Message,
              fromAdminId: adminId,
              createdAt: createMessageDate,
              updatedAt: createMessageDate,
            });
            thread.isRead = false;
            await Promise.all([
              entityManager.save(newMessage),
              this.notificationService.pushNotiNewMessageToStore(
                {
                  storeId: thread.storeId,
                  threadType: thread.threadType,
                },
                request,
              ),
            ]);
            messageId = newMessage.id;
          }
          if (draft.status && thread.status !== draft.status) {
            const createMemoDate = dayjs().toDate();
            const newMemo = entityManager.create(MessageEntity, {
              threadId: draft.threadId,
              content: `ステータスを「${ThreadStatus[draft.status]}」に変更しました。`,
              type: MessageType.Memo,
              fromAdminId: adminId,
              createdAt: createMemoDate,
              updatedAt: createMemoDate,
            });
            await entityManager.save(newMemo);
            updatedStatus = true;
          }
          draft.content && (thread.lastestMessageId = draft.id);
          thread.status = draft.status;
          thread.draftId = null;
          thread.updatedBy = adminId;
          await entityManager.save(thread);
          await entityManager.delete(DraftEntity, { id: draftId });
        }
        return { success: true, status: draft.status, statusDisplay: ThreadStatus[draft.status] || '' };
      });

      if (messageId || updatedStatus) {
        if (messageId) {
          debug(`[saveDraftToMessage] Deleting latest message cache`, { storeId: thread.storeId });
          await this.redis.del(getLatestMessageCacheKey(thread.storeId));
        }

        await this.amqpConnection.publish('taskManager', SYNC_OPENSEARCH_MESSAGE_MQ_TASK, {
          event: RabbitMQEventType.ConsultingMessageCreated,
          content: {
            message_id: messageId || '',
            thread_id: thread.id,
            thread_type: thread.threadType,
            thread_status_id: thread.status,
            thread_updated_at: thread.updatedAt,
          },
        });
      }

      return result;
    } catch (err) {
      return { success: false, error: err };
    }
  }
}
