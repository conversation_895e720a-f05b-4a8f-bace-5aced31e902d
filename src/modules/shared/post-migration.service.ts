import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DatabaseConfig } from '@src/common/interface/database-config';
import { AddIndexToThreadIdAndCreatedAtOnMessagesTable1744174628199 } from '@src/migrations/1744174628199-add_index_to_thead_id_and_created_at_on_messages_table';
import { AddIndexToRelationIdOnAttachmentsTable1744277042771 } from '@src/migrations/1744277042771-add_index_to_relation_id_on_attachments_table';
import { DataSource, DataSourceOptions } from 'typeorm';

@Injectable()
export class PostMigrationService {
  private readonly logger = new Logger(PostMigrationService.name);
  constructor(private readonly configService: ConfigService) {}

  private getDatabaseConfig(): DataSourceOptions {
    const databaseConfig: DatabaseConfig = this.configService.get<DatabaseConfig>('database');
    return {
      name: 'PostMigrationConnection',
      type: databaseConfig.type,
      ...databaseConfig,
      migrations: [
        AddIndexToThreadIdAndCreatedAtOnMessagesTable1744174628199,
        AddIndexToRelationIdOnAttachmentsTable1744277042771,
      ],
    };
  }
  async runMigrations() {
    this.logger.log('Checking post migrations....');
    const dataSourceOptions: DataSourceOptions = this.getDatabaseConfig();
    const dataSource = new DataSource(dataSourceOptions);
    await dataSource.initialize();

    dataSource
      .runMigrations()
      .then((migrations) => {
        if (migrations && migrations.length) {
          this.logger.log(
            `Migrations executed successfully: ${migrations.map((migration) => migration.name).join(', ')}`,
          );
        } else {
          this.logger.log('No migrations executed');
        }
        dataSource.destroy();
        this.logger.log('Post migrations completed. Destroying PostMigrationConnection connection.');
      })
      .catch((error) => {
        this.logger.log('Error running migrations', error);
        dataSource.destroy();
        this.logger.log('Post migrations completed with error. Destroying PostMigrationConnection connection.');
      });
  }
}
