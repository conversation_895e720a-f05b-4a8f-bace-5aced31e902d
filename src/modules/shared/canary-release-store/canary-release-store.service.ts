import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { CannaryReleaseStoreConfig } from '@src/configs/cannary-release-store';
import { CanaryReleaseStoreEntity } from '@src/entities/canary-release-store.entity';

@Injectable()
export class CanaryReleaseStoreService {
  private readonly logger = new Logger(CanaryReleaseStoreService.name);

  constructor(
    @InjectRepository(CanaryReleaseStoreEntity)
    private readonly canaryReleaseStoreRepository: Repository<CanaryReleaseStoreEntity>,
    private readonly configService: ConfigService,
  ) {}

  /**
   * @param storeId
   */
  async checkRelease(storeId: string): Promise<boolean> {
    try {
      const releaseStoreConfig = this.configService.get<CannaryReleaseStoreConfig>('cannaryReleaseStore');
      let canUse = true;

      if (releaseStoreConfig && releaseStoreConfig.isEnable) {
        const releaseStoreObj = await this.canaryReleaseStoreRepository.findOneBy({ storeId });
        canUse = !!releaseStoreObj;
      }

      return canUse;
    } catch (err) {
      this.logger.error({ err: err.message });
      return false;
    }
  }
}
