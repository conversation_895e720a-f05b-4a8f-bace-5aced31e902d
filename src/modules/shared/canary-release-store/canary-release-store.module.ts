import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CanaryReleaseStoreEntity } from '@src/entities';
import { CanaryReleaseStoreService } from '@modules/shared/canary-release-store/canary-release-store.service';

@Module({
  imports: [TypeOrmModule.forFeature([CanaryReleaseStoreEntity])],
  providers: [CanaryReleaseStoreService],
  exports: [CanaryReleaseStoreService],
})
export class CanaryReleaseStoreModule {}
