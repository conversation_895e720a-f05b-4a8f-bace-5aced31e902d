import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository, In } from 'typeorm';
import {
  AdminEntity,
  AdminsStoresEntity,
  AdminsTeamsEntity,
  AttachmentEntity,
  InquiryThreadEntity,
  MessageEntity,
  QualityAdminsStoresEntity,
  TeamEntity,
  TeamsStoresEntity,
} from '@src/entities';
import { MessageType } from '@src/common/enums/message-type';
import { InquiryThreadStatus } from '@src/common/enums/inquiry-thread-status';
import { HttpResponseFormatter } from '@common/response/response';
import { MessageDto } from '@src/modules/shop/dtos/message.dto';
import { InquiryThreadQueryRequest } from '@src/modules/terry/dtos/inquiry-thread-query-request.dto';
import { v4 as uuidv4 } from 'uuid';
import { ThreadType, ThreadTypeName } from '@src/common/enums/thread-type';
import {
  REDIS_CACHE_LATEST_MESSAGE_EXPIRED_TIME,
  SYNC_OPENSEARCH_MESSAGE_MQ_TASK,
  ThreadStatus,
} from '@common/constants';
import { StoreDto } from '@vietnam/curama-py3-api';
import { AttachmentService } from '../attachment/attachment.service';
import { SearchService } from '../search/services/search.service';
import { SearchServiceDto } from '../search/dtos/search-service.dto';
import { OrderType } from '@src/common/enums/order-type.enum';
import { SortTypes } from '@src/common/enums/sort-type.enum';
import { InquiryThreadOpenSearchQuery } from '@src/common/interface/open-search.interface';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';
import { getLatestMessageCacheKey } from '@src/common/utils/helper';
import { debug } from '@src/common/utils/debugger';

@Injectable()
export class InquiryThreadService {
  private readonly logger = new Logger(InquiryThreadService.name);

  /**
   * Constructor InquiryThreadService
   * @param dataSource
   * @param inquiryThreadRepo
   * @param attachmentService
   * @param searchService
   * @param amqpConnection
   */
  constructor(
    private dataSource: DataSource,
    @InjectDataSource('replicaConnection')
    private replicaDataSource: DataSource,
    @InjectRepository(InquiryThreadEntity)
    private readonly inquiryThreadRepo: Repository<InquiryThreadEntity>,
    @InjectRedis() private readonly redis: Redis,
    private readonly attachmentService: AttachmentService,
    private readonly searchService: SearchService,
    private readonly amqpConnection: AmqpConnection,
  ) {}

  /**
   * Get all threads
   * @param inquiryThreadQueryRequest
   */
  public async getAll(
    inquiryThreadQueryRequest: InquiryThreadQueryRequest,
  ): Promise<{ threads: InquiryThreadEntity[]; pagination: any }> {
    const { storeId, assignmentTeamId, assignmentAdminId, status, updatedAt, search, pageNumber, perPage } =
      inquiryThreadQueryRequest;

    let totalRow = 0;

    let threadIds: string[] = [];
    const searchQueryDto: SearchServiceDto = {
      page: pageNumber,
      perPage: perPage,
      sort: { [SortTypes.ThreadUpdatedAt]: OrderType.DESC },
      ...(storeId && { storeId }),
      ...(assignmentTeamId && { teamId: assignmentTeamId }),
      ...(assignmentAdminId && { adminId: assignmentAdminId }),
      ...(status && { statusId: status }),
      ...(updatedAt && { updatedAt }),
      ...(search?.trim() && {
        content: search.trim().split(/\s+/).filter(Boolean),
      }),
    };

    const openSearchResult = await this.searchService.getThreadsByConditions(searchQueryDto);

    threadIds =
      openSearchResult?.hits.hits.map((hit) => {
        const item = <InquiryThreadOpenSearchQuery>(<unknown>hit._source);
        return item.thread_id;
      }) || [];

    totalRow = openSearchResult?.aggregations?.total_groups.value || 0;

    if (threadIds.length == 0) {
      return {
        threads: [],
        pagination: {
          perPage,
          pageNumber,
          total: totalRow,
        },
      };
    }

    const queryBuilder = this.inquiryThreadRepo
      .createQueryBuilder('thread')
      .select([
        'thread.id             AS thread_id',
        'thread.store_id::text AS store_id',
        'thread.store_name     AS store_name',
        'thread.thread_type    AS thread_type',
        'thread.status         AS thread_status_id',
        'thread.updated_at     AS thread_updated_at',
      ])
      .addSelect(
        `CASE WHEN "thread"."thread_type" = ${ThreadType.QualityThread} THEN "qualityTeam".id::text ELSE "curamaTeam".id::text END as team_id`,
      )
      .addSelect(
        `CASE WHEN "thread"."thread_type" = ${ThreadType.QualityThread} THEN "qualityTeam".name ELSE "curamaTeam".name END as team_name`,
      )
      .addSelect(
        `CASE WHEN "thread"."thread_type" = ${ThreadType.QualityThread} THEN "qualityAdminStore"."admin_id"::text ELSE "adminStore"."admin_id"::text END as admin_id`,
      )
      .addSelect(
        `CASE WHEN "thread"."thread_type" = ${ThreadType.QualityThread} THEN "qualityAdmin"."name" ELSE "curamaAdmin"."name" END as admin_name`,
      )
      .leftJoin(TeamsStoresEntity, 'teamStore', 'teamStore.storeId = thread.storeId')
      .leftJoin(AdminsStoresEntity, 'adminStore', 'adminStore.storeId = thread.storeId')
      .leftJoin(QualityAdminsStoresEntity, 'qualityAdminStore', 'qualityAdminStore.storeId = thread.storeId')
      .leftJoin(TeamEntity, 'curamaTeam', 'curamaTeam.id = teamStore.teamId')
      .leftJoin(AdminEntity, 'curamaAdmin', 'curamaAdmin.id = adminStore.adminId')
      .leftJoin(AdminEntity, 'qualityAdmin', 'qualityAdmin.id = qualityAdminStore.adminId')
      .leftJoin(AdminsTeamsEntity, 'adminTeam', 'adminTeam.adminId = qualityAdminStore.adminId')
      .leftJoin(TeamEntity, 'qualityTeam', 'qualityTeam.id = adminTeam.teamId')
      .where(`thread.id in (:...threadIds)`, { threadIds })
      .orderBy('thread.updated_at', 'DESC');

    const listThread = await queryBuilder.getRawMany();

    return {
      threads:
        listThread?.map((item) => {
          return {
            ...item,
            store_name: item.store_name?.escape().urlize(),
            thread_status_name: ThreadStatus[item.thread_status_id],
          };
        }) || [],
      pagination: {
        perPage,
        pageNumber,
        total: totalRow,
      },
    };
  }

  /**
   * Get the thread with the latest message
   * @param storeId
   */
  public async getLatestMessageForEachType(storeId: string) {
    const cacheKey = getLatestMessageCacheKey(storeId);
    const cacheData = await this.redis.get(cacheKey);
    if (cacheData) {
      try {
        debug(`[getLatestMessageForEachType] Cache hit`, { cacheKey, cacheData });
        return JSON.parse(cacheData);
      } catch (error) {
        this.logger.log('[getLatestMessageForEachType] Failed to parse cache data', error);
      }
    }

    const latestMessageBuilder = this.inquiryThreadRepo
      .createQueryBuilder('thread')
      .select('DISTINCT ON (thread.thread_type) thread.thread_type')
      .addSelect('thread.is_read')
      .addSelect('msg.content as latest_message')
      .addSelect('msg.created_at as latest_time')
      .innerJoin(MessageEntity, 'msg', 'msg.threadId = thread.id')
      .where('thread.store_id = :storeId', { storeId })
      .andWhere('msg.type = :type', { type: MessageType.Message })
      .orderBy('thread.thread_type', 'ASC')
      .addOrderBy('msg.created_at', 'DESC');

    const sqlQuery = latestMessageBuilder.getSql();
    const parameters = Object.values(latestMessageBuilder.getParameters());

    const latestMessages = await this.replicaDataSource.query(sqlQuery, parameters);

    await this.redis.set(cacheKey, JSON.stringify(latestMessages), 'EX', REDIS_CACHE_LATEST_MESSAGE_EXPIRED_TIME);
    return latestMessages;
  }

  /**
   * Create message by store
   * @param messageRequest
   * @param store
   * @param attachments
   * @param existedThread
   */
  async createMessageByStore(
    messageRequest: MessageDto,
    store: StoreDto,
    attachments: Express.Multer.File[],
    existedThread?: InquiryThreadEntity,
  ) {
    if (messageRequest.message.length > 2000) {
      return HttpResponseFormatter.responseBadRequest({ errors: 'Please enter 2000 characters or less.' });
    }
    let thread = existedThread;
    if (!thread) {
      thread = new InquiryThreadEntity();
      thread.id = uuidv4();
      thread.storeId = store.id;
      thread.threadType = messageRequest.threadTypeId;
    }
    thread.isRead = true;
    thread.status = InquiryThreadStatus.Unread;
    thread.updatedBy = store.id;
    thread.storeName = store.name;

    const messageId = uuidv4();
    thread.lastestMessageId = messageId;

    const message = new MessageEntity();
    message.content = messageRequest.message;
    message.type = MessageType.Message;
    message.id = messageId;
    message.attachments = [];
    message.threadId = thread.id;

    for (const attachment of attachments) {
      const uploadedFile = await this.attachmentService.uploadFile(attachment, messageId, thread.id);
      const attachmentEntity = new AttachmentEntity();
      attachmentEntity.id = uploadedFile.id;
      attachmentEntity.path = uploadedFile.path;
      attachmentEntity.attachmentType = uploadedFile.attachmentType;
      attachmentEntity.relationId = uploadedFile.relationId;
      message.attachments.push(attachmentEntity);
    }

    try {
      await this.dataSource.manager.transaction(async (transactionalEntityManager) => {
        await Promise.all([transactionalEntityManager.save(thread), transactionalEntityManager.save(message)]);
      });
      await message.reload();
      for (const attachment of message.attachments) {
        await attachment.reload();
      }
    } catch (error) {
      this.logger.log('createMessageByStore: error while creating message and reload');
      throw error;
    }

    debug(`[createMessageByStore] Deleting latest message cache`, { storeId: store.id });
    this.redis.del(getLatestMessageCacheKey(store.id));

    await this.amqpConnection.publish('taskManager', SYNC_OPENSEARCH_MESSAGE_MQ_TASK, {
      event: RabbitMQEventType.ConsultingMessageCreated,
      content: {
        message_id: message.id,
        thread_id: thread.id,
        thread_type: thread.threadType,
        thread_status_id: thread.status,
        thread_updated_at: thread.updatedAt,
      },
    });

    return HttpResponseFormatter.responseOK({
      data: { thread, message: { ...message, content: message.content.escape().urlize() } },
    });
  }

  /**
   * Get list thread by store
   * @param storeId
   * @param currentPathName
   * @param page
   * @param perPage
   * @param relations
   */
  public async getByStore(
    storeId: string,
    currentPathName: string,
    page: number,
    perPage: number,
    relations?: string[],
  ): Promise<object> {
    const pageNumber = parseInt(String(page ? page : 1));

    const [list, total] = await this.inquiryThreadRepo.findAndCount({
      where: {
        storeId: storeId,
      },
      take: perPage,
      skip: page > 0 ? (page - 1) * perPage : 0,
      relations: relations ?? [],
    });

    return { list, pagination: { pageNumber, total, perPage, currentPathName } };
  }

  /**
   * Get inquiryThread by id
   * @param id Id of inquiryThread
   * @param relations Entities relation with inquiryThread
   */
  public async getById(id: string, relations: string[] = []): Promise<InquiryThreadEntity | undefined> {
    return await this.inquiryThreadRepo.findOne({
      where: { id },
      relations: relations,
    });
  }

  public getByCondition(conditions: any, relations: string[] = []): Promise<InquiryThreadEntity | undefined> {
    return this.inquiryThreadRepo.findOne({
      where: conditions,
      relations: relations,
    });
  }

  async getOrCreateThread(storeId: string, storeName: string, threadType: ThreadType) {
    let thread = await this.inquiryThreadRepo.findOne({
      where: { storeId, threadType },
    });
    if (!thread) {
      const newThread = this.inquiryThreadRepo.create({
        storeId,
        threadType,
        storeName,
        isRead: true,
      });
      thread = await this.inquiryThreadRepo.save(newThread);
    }
    return thread;
  }

  saveThread(thread: InquiryThreadEntity) {
    return this.inquiryThreadRepo.save(thread);
  }

  updateThreadById(id: string, thread: InquiryThreadEntity) {
    return this.inquiryThreadRepo.update({ id }, thread);
  }

  public async getThreadByType(storeId: string, threadType: number, relations: string[] = []) {
    return await this.inquiryThreadRepo.findOne({
      where: {
        storeId: storeId,
        threadType: threadType,
      },
      relations,
    });
  }

  public getThreadTypeObject(threadTypeId: number) {
    const threadTypeName = ThreadTypeName[threadTypeId] ?? undefined;
    if (!threadTypeName) {
      return null;
    }

    return {
      id: threadTypeId,
      name: threadTypeName,
    };
  }

  public isShowHelpFeelModal(threadTypeId, listMessage: MessageEntity[]) {
    if (threadTypeId === ThreadType.QualityThread) {
      return false;
    }

    if (!listMessage || listMessage.length === 0) {
      return true;
    }

    const lastMessage = listMessage[0];
    return new Date().getTime() - lastMessage.createdAt.getTime() > 3 * 24 * 3600000;
  }

  /**
   * Retrieves a map of InquiryThreadEntity instances based on the given store IDs and thread type.
   *
   * This method queries the InquiryThread repository to find all threads that match the provided
   * store IDs and thread type. It returns a map where the store ID is the key, and the corresponding
   * InquiryThreadEntity is the value.
   *
   * @param storeIds - An array of store IDs to filter the threads.
   * @param threadType - The thread type to filter by.
   * @returns A Promise that resolves to a map of InquiryThreadEntity instances keyed by their store ID.
   */
  async getThreadsByStoreIdsAndType(storeIds: string[], threadType: ThreadType): Promise<InquiryThreadEntity[]> {
    return this.inquiryThreadRepo.find({
      where: {
        storeId: In(storeIds),
        threadType,
      },
    });
  }

  /**
   * Check if thread is not read (isRead = false) then update it to read isRead = true
   * @param thread InquiryThreadEntity
   * @returns {Promise<void>}
   */
  async updateThreadIsRead(thread: InquiryThreadEntity): Promise<void> {
    if (!thread.isRead) {
      await Promise.all([
        this.inquiryThreadRepo.update({ id: thread.id }, { isRead: true, updatedAt: thread.updatedAt }),
        this.redis.del(getLatestMessageCacheKey(thread.storeId)),
      ]);
    }
  }
}
