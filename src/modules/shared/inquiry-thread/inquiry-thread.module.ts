import { InquiryThreadService } from '@modules/shared/inquiry-thread/inquiry-thread.service';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InquiryThreadEntity, MessageEntity } from '@src/entities';
import { CuramaPy3ApiModule } from '@vietnam/curama-py3-api';
import { AttachmentModule } from '../attachment/attachment.module';
import { SearchModule } from '../search/search.module';
import { QueueProvider } from '@providers/queue.provider';

@Module({
  controllers: [],
  exports: [InquiryThreadService],
  providers: [InquiryThreadService],
  imports: [
    TypeOrmModule.forFeature([InquiryThreadEntity, MessageEntity]),
    CuramaPy3ApiModule,
    AttachmentModule,
    SearchModule,
    QueueProvider.register(),
  ],
})
export class InquiryThreadModule {}
