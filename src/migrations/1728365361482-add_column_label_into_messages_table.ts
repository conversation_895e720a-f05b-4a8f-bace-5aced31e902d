import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddColumnLabelIntoMessagesTable1728365361482 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('Messages', [
      new TableColumn({
        name: 'label',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns('Messages', ['label']);
  }
}
