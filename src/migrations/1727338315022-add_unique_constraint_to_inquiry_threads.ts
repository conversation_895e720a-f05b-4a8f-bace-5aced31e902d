import { MigrationInterface, QueryRunner } from 'typeorm';

const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

export class AddUniqueConstraintToInquiryThreads1727338315022 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE ${schema}."InquiryThreads"
          ADD CONSTRAINT "unique_store_thread" UNIQUE ("store_id", "thread_type");`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE ${schema}."InquiryThreads" DROP CONSTRAINT "unique_store_thread";`);
  }
}
