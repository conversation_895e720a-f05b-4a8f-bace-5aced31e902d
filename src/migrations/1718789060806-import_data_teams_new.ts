import { MigrationInterface, QueryRunner } from 'typeorm';
import * as fs from 'fs';
import { parse } from 'csv-parse';

const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;
export class ImportDataTeamsNew1718789060806 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const path = __dirname + '/csv/teams_new.csv';
    return new Promise((resolve, reject) => {
      const records = [];
      fs.createReadStream(path)
        .pipe(parse({ delimiter: ',', from_line: 2 }))
        .on('data', function (row) {
          const id = row[0];
          const name = row[1];
          const departmentId = row[2];
          const isLast = row[3]?.toLowerCase();
          records.push({
            id,
            name,
            departmentId: departmentId === 'null' ? null : departmentId,
            isLast: isLast === 'true' ? true : false,
          });
        })
        .on('error', (e) => reject(e))
        .on('end', async function () {
          const insert = [];
          for (const team of records) {
            const existedTeam = await queryRunner.query(`SELECT id FROM ${schema}."Teams" WHERE id = '${team.id}'`);
            if (!existedTeam.length) {
              const teamName = team.name.replaceAll("'", "''");
              insert.push(
                `('${team.id}', '${teamName}', ${team.departmentId ? `'${team.departmentId}'` : 'NULL'}, ${
                  team.isLast
                })`,
              );
            }
          }
          if (insert.length) {
            await queryRunner.query(`
              INSERT INTO ${schema}."Teams" (id, name, department_id, is_last)
              VALUES ${insert.join(',')}
            `);
          }

          resolve();
        });
    });
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
