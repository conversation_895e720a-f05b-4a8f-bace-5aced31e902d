import { MigrationInterface, QueryRunner } from 'typeorm';
const schemaName = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

export class AddDataWorkType1692608558670 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const types = [
      '品質管理:予約対応',
      '品質管理:サービスページ・登録情報',
      '品質管理:メッセージ',
      '品質管理:作業・料金報告',
      '品質管理:ユーザー対応',
      '品質管理:請求',
      'コンサル:提案',
      'コンサル:フォロー',
      '審査:出店審査',
      '審査:基本情報変更申請',
      '審査:資格許認可',
    ];
    const dataWorkType = types.map((type) => `('${type}')`);
    await queryRunner.query(`INSERT INTO ${schemaName}."WorkTypes" (name)
            VALUES ${dataWorkType.join(', ')};`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`TRUNCATE TABLE ${schemaName}."WorkTypes" CASCADE;`);
  }
}
