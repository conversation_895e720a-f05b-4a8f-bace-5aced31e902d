import { MigrationInterface, QueryRunner } from 'typeorm';
const schemaName = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;
const templateGroups = [{ AIメッセージパトロール: 'AIメッセージパトロール' }];
const WorkType = ['AIメッセージパトロール'];

export class AddMessageTemplatePatrol1722854400108 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const currentIndex = await queryRunner.query(
      `SELECT MAX(sort) FROM ${schemaName}."MessageTemplateGroups" mtg WHERE parent_id IS NULL;`,
    );
    const dataLargeGroup = [...new Set(templateGroups.map((e) => Object.keys(e)[0]))].map(
      (data, index) => `('${data}', ${currentIndex[0]['max'] + index + 1})`,
    );
    await queryRunner.query(
      `INSERT INTO ${schemaName}."MessageTemplateGroups" (name, sort) VALUES ${dataLargeGroup.join(', ')};`,
    );
    const data = await queryRunner.query(
      `SELECT id, name FROM ${schemaName}."MessageTemplateGroups" WHERE parent_id IS NULL;`,
    );
    const dataSubGroup = [];
    data.forEach((largeGroup) => {
      const subGroup = templateGroups
        .filter((e) => Object.keys(e)[0] === largeGroup.name)
        .map((e) => Object.values(e)[0]);
      if (subGroup.length) {
        dataSubGroup.push(...subGroup.map((ele, index) => `('${ele}', ${index + 1}, '${largeGroup.id}')`));
      }
    });
    await queryRunner.query(
      `INSERT INTO ${schemaName}."MessageTemplateGroups" (name, sort, parent_id) VALUES ${dataSubGroup.join(', ')};`,
    );

    const workTypeData = WorkType.map((e) => `('${e}')`);
    await queryRunner.query(`INSERT INTO ${schemaName}."WorkTypes" (name) VALUES ${workTypeData.join(', ')};`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const dataSubGroup = templateGroups.map((e) => Object.values(e)[0]);
    await queryRunner.query(
      `DELETE FROM ${schemaName}."MessageTemplateGroups" 
                WHERE 
                  name IN (${dataSubGroup.join(', ')})
                  AND parent_id IS NOT NULL;`,
    );

    const dataGroup = templateGroups.map((e) => Object.keys(e)[0]);
    await queryRunner.query(
      `DELETE FROM ${schemaName}."MessageTemplateGroups" 
                WHERE 
                  name IN (${dataGroup.join(', ')}) 
                  AND parent_id IS NULL;`,
    );

    await queryRunner.query(
      `DELETE FROM ${schemaName}."WorkTypes" 
                WHERE 
                  name IN (${WorkType.join(', ')});`,
    );
  }
}
