import { MigrationInterface, QueryRunner } from 'typeorm';
import { parse } from 'csv-parse';
import * as fs from 'fs';

const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `public`;
const table = `${schema}."QualityAdmins_Stores"`;

export class ImportQualityAdminsStoresFromCsv1711099912813 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const existAdminIds = await queryRunner
      .query(`SELECT id FROM ${schema}."Admins";`)
      .then((res) => res.map((r: { id: string }) => r.id));

    const path = __dirname + '/csv/quality_admins_stores.csv';
    return new Promise((resolve, reject) => {
      const records = [];
      fs.createReadStream(path)
        .pipe(parse({ delimiter: ',', from_line: 2 }))
        .on('data', function (row) {
          const storeId = row[0];
          const qualityAdminId = row[1];
          if (existAdminIds.includes(qualityAdminId)) {
            records.push(`('${qualityAdminId}', '${storeId}')`);
          }
        })
        .on('error', (e) => reject(e))
        .on('end', async function () {
          if (records.length) {
            const values = records.join(',');
            const query = `INSERT INTO ${table} (admin_id, store_id) VALUES ${values};`;
            await queryRunner.query(query);
          }

          resolve();
        });
    });
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`TRUNCATE TABLE ${table};`);
  }
}
