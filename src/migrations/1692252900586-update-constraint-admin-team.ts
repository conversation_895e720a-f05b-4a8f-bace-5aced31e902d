import { MigrationInterface, QueryRunner } from 'typeorm';

const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

export class UpdateConstraintAdminTeam1692252900586 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE ${schema}."Admins_Teams" DROP CONSTRAINT "FK_Admins_Teams_Team";
      ALTER TABLE ${schema}."Admins_Teams" ADD CONSTRAINT "FK_Admins_Teams_Team" FOREIGN KEY (team_id) REFERENCES ${schema}."Teams"(id) ON UPDATE CASCADE;
      
      ALTER TABLE ${schema}."Admins_Teams" DROP CONSTRAINT "FK_Admins_Teams_Admin";
      ALTER TABLE ${schema}."Admins_Teams" ADD CONSTRAINT "FK_Admins_Teams_Admin" FOREIGN KEY (admin_id) REFERENCES ${schema}."Admins"(id) ON UPDATE CASCADE;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE ${schema}."Admins_Teams" DROP CONSTRAINT "FK_Admins_Teams_Admin";
      ALTER TABLE ${schema}."Admins_Teams" ADD CONSTRAINT "FK_Admins_Teams_Admin" FOREIGN KEY (admin_id) REFERENCES ${schema}."Admins"(id);
  
      ALTER TABLE ${schema}."Admins_Teams" DROP CONSTRAINT "FK_Admins_Teams_Team";
      ALTER TABLE ${schema}."Admins_Teams" ADD CONSTRAINT "FK_Admins_Teams_Team" FOREIGN KEY (team_id) REFERENCES ${schema}."Teams"(id);
    `);
  }
}
