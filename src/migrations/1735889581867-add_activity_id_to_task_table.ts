import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';
export class AddActivityIdToTaskTable1735889581867 implements MigrationInterface {
  private readonly tableName = 'Tasks';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns(this.tableName, [
      new TableColumn({
        name: 'activity_id',
        type: 'uuid',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.tableName, 'activity_id');
  }
}
