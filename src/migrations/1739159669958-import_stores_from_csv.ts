import { MigrationInterface, QueryRunner } from 'typeorm';
import { parse } from 'csv-parse';
import * as fs from 'fs';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `public`;
const table = `${schema}."Stores"`;
export class ImportStoresFromCsv1739159669958 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const path = __dirname + '/csv/stores.csv';
    const existTeamIds = await queryRunner
      .query(`SELECT id FROM ${schema}."Stores";`)
      .then((res) => res.map((r: { id: string }) => r.id));

    return new Promise((resolve, reject) => {
      const records = [];
      fs.createReadStream(path)
        .pipe(parse({ delimiter: ',', from_line: 2 }))
        .on('data', function (row) {
          const id = row[0];
          const status_id = row[2];
          const name = row[1].replace(/'/g, "''").trim();
          if (!existTeamIds.includes(id)) {
            records.push(`('${id}', '${name}', '${status_id}')`);
          }
        })
        .on('error', (e) => reject(e))
        .on('end', async function () {
          if (records.length) {
            const batchSize = 100; // Adjust as needed
            for (let i = 0; i < records.length; i += batchSize) {
              const batch = records.slice(i, i + batchSize).join(',');
              const query = `INSERT INTO ${table} (id, name, status) VALUES ${batch}`;
              await queryRunner.query(query);
            }
          }
          resolve();
        });
    });
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`TRUNCATE TABLE ${table};`);
  }
}
