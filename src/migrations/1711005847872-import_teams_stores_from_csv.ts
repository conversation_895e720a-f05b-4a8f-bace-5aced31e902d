import { MigrationInterface, QueryRunner } from 'typeorm';
import { parse } from 'csv-parse';
import * as fs from 'fs';

const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `public`;
const table = `${schema}."Teams_Stores"`;

export class ImportTeamsStoresFromCsv1711005847872 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const path = __dirname + '/csv/teams_stores.csv';
    const existTeamIds = await queryRunner
      .query(`SELECT id FROM ${schema}."Teams";`)
      .then((res) => res.map((r: { id: string }) => r.id));

    return new Promise((resolve, reject) => {
      const records = [];
      fs.createReadStream(path)
        .pipe(parse({ delimiter: ',', from_line: 2 }))
        .on('data', function (row) {
          const id = row[0];
          const store_id = row[1];
          const team_id = row[2];
          if (existTeamIds.includes(team_id)) {
            records.push(`('${id}', '${team_id}', '${store_id}')`);
          }
        })
        .on('error', (e) => reject(e))
        .on('end', async function () {
          if (records.length) {
            const values = records.join(',');
            const query = `INSERT INTO ${table} (id, team_id, store_id) VALUES ${values};`;
            await queryRunner.query(`TRUNCATE TABLE ${table};`);
            await queryRunner.query(query);
          }

          resolve();
        });
    });
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`TRUNCATE TABLE ${table};`);
  }
}
