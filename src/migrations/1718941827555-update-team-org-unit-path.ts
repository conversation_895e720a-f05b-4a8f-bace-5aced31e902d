import { MigrationInterface, QueryRunner } from 'typeorm';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

export class UpdateTeamOrgUnitPath1718941827555 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const allTeam = await queryRunner.query(`SELECT id, name, department_id FROM ${schema}."Teams"`);

    const getOrgUnitPath = (department_id, name) => {
      if (!department_id) return `/${name}`;
      let currentParentId = department_id;
      const paths = [name];
      while (currentParentId) {
        const parent = allTeam.find((e) => e.id === currentParentId);
        if (!parent) break;
        paths.push(parent.name);
        currentParentId = parent.department_id;
      }
      return `/${paths.reverse().join('/')}`;
    };
    const update = [];
    for (const team of allTeam) {
      const orgUnitPath = getOrgUnitPath(team.department_id, team.name);
      update.push(
        queryRunner.query(`UPDATE ${schema}."Teams" SET orgunit_path = '${orgUnitPath}' WHERE id = '${team.id}'`),
      );
    }
    if (update.length) {
      await Promise.all(update);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
