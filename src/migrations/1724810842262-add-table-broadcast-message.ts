import { MigrationInterface, QueryRunner } from 'typeorm';

const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

export class AddTableBroadcastMessage1724810842262 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS ${schema}."BroadcastMessages" (
        id uuid NOT NULL DEFAULT uuid_generate_v4(),
        title varchar(255) NOT NULL,
        "content" varchar(2000) NOT NULL,
        description varchar(2000) NOT NULL,
        status int2 NOT NULL DEFAULT 1,
        scheduled_send_time timestamp NULL,
        import_file_path varchar NULL,
        csv_file_name varchar NULL,
        can_send_store_count int4 NULL,
        cannot_send_store_count int4 NULL,
        store_sent_success_count int4 NULL,
        store_sent_fail_count int4 NULL,
        created_by uuid NOT NULL,
        approved_by uuid NULL,
        sending_by uuid NULL,
        created_at timestamp NOT NULL DEFAULT now(),
        updated_at timestamp NOT NULL DEFAULT now(),
        approved_at timestamp NULL,
        CONSTRAINT "PK_BroadcastMessages" PRIMARY KEY (id),
	      CONSTRAINT "FK_BroadcastMessages_CreatedAdmin" FOREIGN KEY (created_by) REFERENCES ${schema}."Admins"(id),
	      CONSTRAINT "FK_BroadcastMessages_ApprovedAdmin" FOREIGN KEY (approved_by) REFERENCES ${schema}."Admins"(id),
        CONSTRAINT "FK_BroadcastMessages_SendingAdmin" FOREIGN KEY (sending_by) REFERENCES ${schema}."Admins"(id)
      );

      CREATE TABLE IF NOT EXISTS ${schema}."Stores" (
        id uuid NOT NULL DEFAULT uuid_generate_v4(),
        status int2 NOT NULL,
        "name" varchar NOT NULL,
        created_at timestamp NOT NULL DEFAULT now(),
        updated_at timestamp NOT NULL DEFAULT now(),
        CONSTRAINT "PK_Stores" PRIMARY KEY (id)
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP TABLE IF EXISTS ${schema}."BroadcastMessages";
      DROP TABLE IF EXISTS ${schema}."Stores";
    `);
  }
}
