import { MigrationInterface, QueryRunner } from 'typeorm';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

export class AddQualityTeamNew1719543111983 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const newQualityTeams = [
      {
        id: 'fc2cbbc2-ad37-4ea5-b7a6-70ca4bc6b5d1',
        name: '品質管理部',
        departmentId: 'cdc8132a-d1d3-4bdd-bb99-87b077363a29',
        isLast: true,
        orgUnitPath: '/マーケットプレイス事業部/コンサルティング本部/品質管理部',
      },
      {
        id: 'ba2cc048-a868-48da-8d04-d72b23dced4c',
        name: 'マーケットプレイス',
        departmentId: '8ad4447d-b2ed-4797-b7b0-88f6bbecd81c',
        isLast: true,
        orgUnitPath: '/マーケットプレイス事業部/マーケットプレイス',
      },
    ];
    const insert = newQualityTeams.map(
      (team) => `('${team.id}', '${team.name}', '${team.departmentId}', ${team.isLast}, '${team.orgUnitPath}')`,
    );
    await queryRunner.query(`
      INSERT INTO ${schema}."Teams" (id, name, department_id, is_last, orgunit_path)
      VALUES ${insert.join(',')}
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
