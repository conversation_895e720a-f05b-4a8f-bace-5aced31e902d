import { MigrationInterface, QueryRunner } from 'typeorm';

const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;
export class AlterTableTeams1718760880216 implements MigrationInterface {
  public async up(queryRunner: QueryRunner) {
    const migrations = await queryRunner.query(
      `SELECT column_name FROM information_schema.columns WHERE table_schema = '${process.env.DATABASE_SCHEMA}' AND table_name = 'Teams' AND column_name = 'is_last'`,
    );

    if (migrations.length) return;
    await queryRunner.query(`
      ALTER TABLE ${schema}."Teams"
      DROP CONSTRAINT IF EXISTS "Teams_department_id_3d88b57ee51ce0cd_fk_Departments_id";

      ALTER TABLE ${schema}."Teams"
      ALTER COLUMN department_id DROP NOT NULL;  

      ALTER TABLE ${schema}."Teams"
      ADD COLUMN "is_last" boolean DEFAULT true;

      DROP TABLE IF EXISTS ${schema}."Departments";
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
