import { MigrationInterface, QueryRunner } from 'typeorm';
import { parse } from 'csv-parse';
import * as fs from 'fs';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `public`;
const table = `${schema}."Teams"`;
export class ImportTeamsFromCsv1700539668075 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const path = __dirname + '/csv/teams.csv';
    return new Promise((resolve, reject) => {
      const records = [];
      fs.createReadStream(path)
        .pipe(parse({ delimiter: ',', from_line: 2 }))
        .on('data', function (row) {
          const id = row[0];
          const name = row[1];
          const departmentId = row[2];
          records.push(`('${id}', '${name}', '${departmentId}')`);
        })
        .on('error', (e) => reject(e))
        .on('end', async function () {
          const values = records.join(',');
          const query = `INSERT INTO ${table} (id, name, department_id) VALUES ${values};`;
          await queryRunner.query(query);
          resolve();
        });
    });
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`TRUNCATE TABLE ${table};`);
  }
}
