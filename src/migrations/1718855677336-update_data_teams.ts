import { MigrationInterface, QueryRunner } from 'typeorm';
import * as fs from 'fs';
import { parse } from 'csv-parse';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

export class UpdateDataTeams1718855677336 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const path = __dirname + '/csv/teams_update.csv';
    return new Promise((resolve, reject) => {
      const records = [];
      fs.createReadStream(path)
        .pipe(parse({ delimiter: ',', from_line: 2 }))
        .on('data', function (row) {
          const id = row[0];
          const name = row[1];
          const departmentId = row[2];
          const isLast = row[3]?.toLowerCase();
          records.push({
            id,
            name,
            departmentId: departmentId === 'null' ? null : departmentId,
            isLast: isLast === 'true' ? true : false,
          });
        })
        .on('error', (e) => reject(e))
        .on('end', async function () {
          const update = [];
          for (const team of records) {
            const existedTeam = await queryRunner.query(`SELECT id FROM ${schema}."Teams" WHERE id = '${team.id}'`);
            if (existedTeam.length) {
              const teamName = team.name.replaceAll("'", "''");
              update.push(
                queryRunner.query(`
                  UPDATE ${schema}."Teams"
                  SET department_id = ${team.departmentId ? `'${team.departmentId}'` : 'NULL'}, is_last = ${
                  team.isLast
                }, name = '${teamName}'
                  WHERE id = '${team.id}';
                `),
              );
            }
          }
          if (update.length) {
            await Promise.all(update);
          }
          resolve();
        });
    });
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
