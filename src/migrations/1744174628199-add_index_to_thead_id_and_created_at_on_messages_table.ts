import { MigrationInterface, QueryRunner } from 'typeorm';

const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;
const tableName = 'Messages';
const indexName = `IDX_${tableName}_Thread_Id_Created_At`;

export class AddIndexToThreadIdAndCreatedAtOnMessagesTable1744174628199 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const indexExists = await this.indexExists(queryRunner, indexName);
    if (!indexExists) {
      await queryRunner.query(`CREATE INDEX ${indexName} ON ${schema}."${tableName}" (thread_id,created_at DESC);`);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const indexExists = await this.indexExists(queryRunner, indexName);
    if (indexExists) {
      await queryRunner.dropIndex(tableName, indexName);
    }
  }

  private async indexExists(queryRunner: QueryRunner, indexName: string): Promise<boolean> {
    const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;
    const qualifiedIndex = `${schema}."${indexName}"`;
    const result = await queryRunner.query(`SELECT to_regclass($1) as name`, [qualifiedIndex]);
    return result[0]?.name !== null;
  }
}
