import { MigrationInterface, QueryRunner } from 'typeorm';
const schemaName = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

export class AddDataMessageTemplateGroup1692608574828 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const templateGroups = [
      { 違反: '予約対応' },
      { 違反: '予約対応（指定見積）' },
      { 違反: '登録情報' },
      { 違反: 'サービスページの違反' },
      { 違反: 'メッセージの違反' },
      { 違反: '指摘に対応しない' },
      { 違反: '出店継続審議' },
      { 違反: 'スコアリング超過' },
      { 調査: '作業調査' },
      { 調査: '料金調査' },
      { ユーザー対応: '連絡なしキャンセル・遅刻' },
      { ユーザー対応: 'お客様からのご意見' },
      { ユーザー対応: '不適切な対応' },
      { ユーザー対応: '損害賠償補償' },
      { '作業・料金の自主報告': '状況確認' },
      { '作業・料金の自主報告': '通知のみ' },
      { 請求: '支払い遅延' },
      { 請求: '請求に関する通知' },
      { 提案: '新規出店店舗' },
      { 提案: 'ページ・設定提案' },
      { 提案: 'アポイント' },
      { フォロー: 'ユーザークレーム・対応催促' },
      { フォロー: 'OLカード決済' },
      { フォロー: 'キャンセル施策' },
      { フォロー: 'メッセージパトロール' },
      { フォロー: '低評価口コミ' },
      { フォロー: '予約獲得フォロー' },
      { フォロー: '予約見積放置' },
      { 問い合わせ: 'サイトの仕組み' },
      { 問い合わせ: '設定方法' },
      { 問い合わせ: '予約対応' },
      { 外壁塗装アサイン: '現地調査店舗向け' },
      { 外壁塗装アサイン: '施工店舗向け' },
      { 出店審査: '審査開始前' },
      { 出店審査: '審査開始' },
      { 出店審査: '審査開始後' },
      { 出店審査: '完了案内' },
      { 出店審査: '審査不通過' },
      { 基本情報変更申請: '却下' },
      { 基本情報変更申請: '確認' },
      { 基本情報変更申請: '通知' },
    ];
    const dataLargeGroup = [...new Set(templateGroups.map((e) => Object.keys(e)[0]))].map(
      (data, index) => `('${data}', ${index + 1})`,
    );
    await queryRunner.query(`INSERT INTO ${schemaName}."MessageTemplateGroups" (name, sort)
                  VALUES ${dataLargeGroup.join(', ')};`);
    const data = await queryRunner.query(
      `SELECT id, name FROM ${schemaName}."MessageTemplateGroups" WHERE parent_id IS NULL;`,
    );
    const dataSubGroup = [];
    data.forEach((largeGroup) => {
      const subGroup = templateGroups
        .filter((e) => Object.keys(e)[0] === largeGroup.name)
        .map((e) => Object.values(e)[0]);
      if (subGroup.length) {
        dataSubGroup.push(...subGroup.map((ele, index) => `('${ele}', ${index + 1}, '${largeGroup.id}')`));
      }
    });
    await queryRunner.query(
      `INSERT INTO ${schemaName}."MessageTemplateGroups" (name, sort, parent_id) VALUES ${dataSubGroup.join(', ')};`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`TRUNCATE TABLE ${schemaName}."MessageTemplateGroups" CASCADE;`);
  }
}
