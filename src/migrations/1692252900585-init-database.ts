import { MigrationInterface, QueryRunner } from 'typeorm';

const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;
const dbName = process.env.DATABASE_NAME ? `"${process.env.DATABASE_NAME}"` : `"consulting-message"`;
const tz = process.env.TZ ? `"${process.env.TZ}"` : `"Asia/Tokyo"`;
export class InitDatabase1692252900585 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER DATABASE ${dbName} SET timezone TO ${tz};

        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

        CREATE TABLE IF NOT EXISTS ${schema}."Admins" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            "name" varchar NOT NULL,
            email varchar NOT NULL,
            status int2 NOT NULL DEFAULT 1,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_Admins" PRIMARY KEY (id),
            CONSTRAINT "UQ_Admin_Name" UNIQUE (email)
        );
        
        
        CREATE TABLE IF NOT EXISTS ${schema}."Attachments" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            "path" varchar NOT NULL,
            attachment_type int2 NOT NULL,
            relation_id uuid NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_Attachments" PRIMARY KEY (id)
        );
        
        
        CREATE TABLE IF NOT EXISTS ${schema}."CanaryReleaseStores" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            store_id varchar NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_CanaryReleaseStores" PRIMARY KEY (id)
        );
        
        
        CREATE TABLE IF NOT EXISTS ${schema}."NgWords" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            "content" varchar NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_NgWords" PRIMARY KEY (id),
            CONSTRAINT "UQ_NgWords_Content" UNIQUE (content)
        );
        
        CREATE TABLE IF NOT EXISTS ${schema}."Teams" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            "name" text NOT NULL,
            department_id uuid NULL,
            is_last bool NOT NULL DEFAULT true,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "Teams_name_2fde54e90194e03b_uniq" UNIQUE (name, department_id),
            CONSTRAINT "Teams_pkey" PRIMARY KEY (id)
        );
        
        CREATE TABLE IF NOT EXISTS ${schema}."WorkTypes" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            "name" varchar NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_WorkTypes" PRIMARY KEY (id)
        );
        
        
        CREATE TABLE IF NOT EXISTS ${schema}."Admins_Stores" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            store_id uuid NOT NULL,
            admin_id uuid NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_Admins_Stores" PRIMARY KEY (id),
            CONSTRAINT "FK_Admins_Stores_Admin" FOREIGN KEY (admin_id) REFERENCES ${schema}."Admins"(id)
        );
        
        
        CREATE TABLE IF NOT EXISTS ${schema}."Admins_Teams" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            admin_id uuid NOT NULL,
            team_id uuid NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_Admins_Teams" PRIMARY KEY (id, admin_id, team_id),
            CONSTRAINT "FK_Admins_Teams_Team" FOREIGN KEY (team_id) REFERENCES ${schema}."Teams"(id),
            CONSTRAINT "FK_Admins_Teams_Admin" FOREIGN KEY (admin_id) REFERENCES ${schema}."Admins"(id)
        );
        
        
        CREATE TABLE IF NOT EXISTS ${schema}."MessageTemplateGroups" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            "name" varchar NOT NULL,
            parent_id uuid NULL,
            sort int4 NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_MessageTemplateGroups" PRIMARY KEY (id),
            CONSTRAINT "FK_MessageTemplateGroups_SELF" FOREIGN KEY (parent_id) REFERENCES ${schema}."MessageTemplateGroups"(id)
        );
        
        
        CREATE TABLE IF NOT EXISTS ${schema}."MessageTemplates" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            "name" varchar NOT NULL,
            "content" varchar NOT NULL,
            sort int4 NOT NULL DEFAULT 99,
            group_id uuid NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_MessageTemplates" PRIMARY KEY (id),
            CONSTRAINT "FK_MessageTemplates_Group" FOREIGN KEY (group_id) REFERENCES ${schema}."MessageTemplateGroups"(id)
        );
        
        
        CREATE TABLE IF NOT EXISTS ${schema}."QualityAdmins_Stores" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            store_id uuid NOT NULL,
            admin_id uuid NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_QualityAdmins_Stores" PRIMARY KEY (id),
            CONSTRAINT "FK_QualityAdmins_Stores_Admin" FOREIGN KEY (admin_id) REFERENCES ${schema}."Admins"(id)
        );
        
        
        CREATE TABLE IF NOT EXISTS ${schema}."Teams_Stores" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            store_id uuid NOT NULL,
            team_id uuid NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_0ea7c8eee175cec66860f79e1d9" PRIMARY KEY (id),
            CONSTRAINT "FK_b8f0b3b5925a8f457e923d76db2" FOREIGN KEY (team_id) REFERENCES ${schema}."Teams"(id)
        );
        
        
        CREATE TABLE IF NOT EXISTS ${schema}."Drafts" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            thread_id uuid NOT NULL,
            "content" varchar(2000) NULL,
            from_admin_id uuid NOT NULL,
            "status" int2 NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_Drafts" PRIMARY KEY (id),
            CONSTRAINT "REL_Drafts_Thread" UNIQUE (thread_id)
        );
        
        CREATE TABLE IF NOT EXISTS ${schema}."InquiryThreads" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            store_id uuid NOT NULL,
            store_name varchar NOT NULL,
            thread_type int2 NOT NULL,
            draft_id uuid NULL,
            lastest_message_id uuid NULL,
            status int2 NOT NULL DEFAULT 1,
            is_read bool NOT NULL DEFAULT false,
            updated_by varchar NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_InquiryThreads" PRIMARY KEY (id),
            CONSTRAINT "REL_InquiryThreads_Draft" UNIQUE (draft_id)
        );
    
        
        CREATE TABLE IF NOT EXISTS ${schema}."Messages" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            thread_id uuid NOT NULL,
            "content" varchar(2000) NOT NULL,
            from_admin_id uuid NULL,
            "type" int2 NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            deleted_by uuid NULL,
            deleted_at timestamp NULL,
            CONSTRAINT "PK_Messages" PRIMARY KEY (id)
        );
        
        
        CREATE TABLE IF NOT EXISTS ${schema}."Tasks" (
            id uuid NOT NULL DEFAULT uuid_generate_v4(),
            thread_id uuid NOT NULL,
            work_type_id uuid NOT NULL,
            memo varchar(1000) NULL,
            assignment_admin_id uuid NULL,
            assignment_team_id uuid NULL,
            next_confirmation_date date NULL,
            status int2 NOT NULL,
            task_no int2 NOT NULL,
            created_at timestamp NOT NULL DEFAULT now(),
            updated_at timestamp NOT NULL DEFAULT now(),
            created_by varchar NULL,
            updated_by varchar NULL,
            CONSTRAINT "PK_Tasks" PRIMARY KEY (id)
        );
        
        
        -- ${schema}."Drafts" foreign keys
        
        ALTER TABLE ${schema}."Drafts" ADD CONSTRAINT "FK_Drafts_Thread" FOREIGN KEY (thread_id) REFERENCES ${schema}."InquiryThreads"(id);
        
        
        -- ${schema}."InquiryThreads" foreign keys
        
        ALTER TABLE ${schema}."InquiryThreads" ADD CONSTRAINT "FK_InquiryThreads_Draft" FOREIGN KEY (draft_id) REFERENCES ${schema}."Drafts"(id);
    
        -- ${schema}."Messages" foreign keys
        
        ALTER TABLE ${schema}."Messages" ADD CONSTRAINT "FK_Messages_Thread" FOREIGN KEY (thread_id) REFERENCES ${schema}."InquiryThreads"(id);
        ALTER TABLE ${schema}."Messages" ADD CONSTRAINT "FK_Messages_Admin" FOREIGN KEY (from_admin_id) REFERENCES ${schema}."Admins"(id);
        
        
        -- ${schema}."Tasks" foreign keys
        
        ALTER TABLE ${schema}."Tasks" ADD CONSTRAINT "FK_Tasks_Team" FOREIGN KEY (assignment_team_id) REFERENCES ${schema}."Teams"(id);
        ALTER TABLE ${schema}."Tasks" ADD CONSTRAINT "FK_Tasks_WorkType" FOREIGN KEY (work_type_id) REFERENCES ${schema}."WorkTypes"(id);
        ALTER TABLE ${schema}."Tasks" ADD CONSTRAINT "FK_Tasks_Admin" FOREIGN KEY (assignment_admin_id) REFERENCES ${schema}."Admins"(id);
        ALTER TABLE ${schema}."Tasks" ADD CONSTRAINT "FK_Tasks_Thread" FOREIGN KEY (thread_id) REFERENCES ${schema}."InquiryThreads"(id);
        
        CREATE UNIQUE INDEX IF NOT EXISTS inquirythreads_store_id_thread_type_idx ON ${schema}."InquiryThreads" (store_id, thread_type);
        CREATE UNIQUE INDEX IF NOT EXISTS drafts_thread_id_idx ON ${schema}."Drafts" (thread_id);
        CREATE INDEX IF NOT EXISTS "Teams_bf691be4" ON ${schema}."Teams" USING btree (department_id);
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const tables = await queryRunner.query(`select tablename from pg_tables where schemaname = ${schema};`);
    tables.map((e) => `drop table if exists ${e.tablename} cascade;`);
    await Promise.all(tables.map((e) => queryRunner.query(`drop table if exists ${e.tablename} cascade;`)));
  }
}
