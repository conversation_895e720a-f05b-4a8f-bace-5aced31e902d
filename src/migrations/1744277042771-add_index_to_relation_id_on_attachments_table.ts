import { MigrationInterface, QueryRunner } from 'typeorm';

const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;
const tableName = 'Attachments';
const indexName = `IDX_${tableName}_Relation_Id`;

export class AddIndexToRelationIdOnAttachmentsTable1744277042771 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const indexExists = await this.indexExists(queryRunner, indexName);
    if (!indexExists) {
      await queryRunner.query(`CREATE INDEX ${indexName} ON ${schema}."${tableName}" (relation_id);`);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const indexExists = await this.indexExists(queryRunner, indexName);
    if (indexExists) {
      await queryRunner.dropIndex(tableName, indexName);
    }
  }

  private async indexExists(queryRunner: QueryRunner, indexName: string): Promise<boolean> {
    const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;
    const qualifiedIndex = `${schema}."${indexName}"`;
    const result = await queryRunner.query(`SELECT to_regclass($1) as name`, [qualifiedIndex]);
    return result[0]?.name !== null;
  }
}
