import { MigrationInterface, QueryRunner } from 'typeorm';
import { parse } from 'csv-parse';
import * as fs from 'fs';

const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `public`;
const table = `${schema}."Admins"`;
export class ImportAdminFromCsv1700539536184 implements MigrationInterface {
  name = 'SyncAdminsBetweenTicketAndConsultingSchema1700539536184';
  public async up(queryRunner: QueryRunner): Promise<void> {
    const path = __dirname + '/csv/admins.csv';
    return new Promise((resolve, reject) => {
      const records = [];
      fs.createReadStream(path)
        .pipe(parse({ delimiter: ',', from_line: 2 }))
        .on('data', function (row) {
          const id = row[0];
          const name = row[1];
          const email = row[2];
          const status = row[3];
          const createdAt = row[4];
          records.push(`('${id}', '${name}', '${email}', ${status}, '${createdAt}')`);
        })
        .on('error', (e) => reject(e))
        .on('end', async function () {
          const values = records.join(',');
          const query = `INSERT INTO ${table} (id, name, email, status, created_at) VALUES ${values};`;
          await queryRunner.query(query);
          resolve();
        });
    });
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`TRUNCATE TABLE ${table};`);
  }
}
