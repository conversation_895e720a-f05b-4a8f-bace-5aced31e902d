import { Module } from '@nestjs/common';
import { ConfigProvider } from '@providers/config.provider';
import { DatabaseProvider } from '@providers/database.provider';
import { QueueProvider } from '@providers/queue.provider';
import { LoggingProvider } from '@providers/logging.provider';
import { WorkerModule } from '@src/modules/shared/worker/worker.module';
import { CuramaWorkerModule } from '@vietnam/curama-worker';

@Module({
  imports: [
    ConfigProvider.register(),
    DatabaseProvider.register(),
    QueueProvider.register(),
    LoggingProvider.register(),
    WorkerModule,
    CuramaWorkerModule,
  ],
})
export class BackgroundModule {}
