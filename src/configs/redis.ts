import { registerAs } from '@nestjs/config';

export interface RedisConfig {
  host: string;
  port: number;
  db: number;
  password: string;
  keyPrefix?: string;
}

export default registerAs('redis', (): RedisConfig => {
  return {
    host: process.env.REDIS_HOST || 'microapp.redis.curama',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    db: parseInt(process.env.REDIS_DB || '0'),
    password: process.env.REDIS_PASSWORD || '',
    keyPrefix: process.env.REDIS_PRIFIX || '',
  };
});
