import { DatabaseConfig } from '../common/interface/database-config';
import { registerAs } from '@nestjs/config';
import { DatabaseType } from '../common/enums/database-type';

export default registerAs('database', (): DatabaseConfig => {
  const poolSize = process.env.DATABASE_POOL_SIZE ? parseInt(process.env.DATABASE_POOL_SIZE) : 10;
  const connectionTimeoutMillis = process.env.DATABASE_CONNECTION_TIMEOUT
    ? parseInt(process.env.DATABASE_CONNECTION_TIMEOUT)
    : 3000;

  return {
    type: DatabaseType[process.env.DATABASE_DRIVER] || DatabaseType.POSTGRES,
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    schema: process.env.DATABASE_SCHEMA || 'example',
    username: process.env.DATABASE_USERNAME || 'username',
    password: process.env.DATABASE_PASSWORD || '',
    database: process.env.DATABASE_NAME || 'postgres',
    extra: {
      max: poolSize,
      min: poolSize,
      connectionTimeoutMillis: connectionTimeoutMillis,
      idleTimeoutMillis: 0,
    },
  };
});
