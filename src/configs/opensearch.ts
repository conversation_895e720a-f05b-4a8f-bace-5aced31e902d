import { registerAs } from '@nestjs/config';
import { OpenSearchAuthType, OpenSearchConfig } from '@vietnam/curama-opensearch';
import * as process from 'process';

export default registerAs('opensearch', (): OpenSearchConfig => {
  const config: OpenSearchConfig = {
    authType: OpenSearchAuthType.NONE,
  };

  const nodes = process.env.OPENSEARCH_NODE_URL ? process.env.OPENSEARCH_NODE_URL.split(',') : [];
  if (nodes.length > 1) {
    config.nodes = nodes;
  } else {
    config.node = nodes.pop();
  }

  if (process.env.OPENSEARCH_AUTH_TYPE === 'aws') {
    config.authType = OpenSearchAuthType.AWS;
    config.aws = {
      region: process.env.OPENSEARCH_AWS_REGION || 'us-east-1',
      service: process.env.OPENSEARCH_AWS_SERVICE === 'aoss' ? 'aoss' : 'es',
    };
  } else if (process.env.OPENSEARCH_AUTH_TYPE === 'basic') {
    config.authType = OpenSearchAuthType.BASIC;
    config.auth = {
      username: process.env.OPENSEARCH_USERNAME || 'admin',
      password: process.env.OPENSEARCH_PASSWORD || 'admin',
    };
  } else {
    config.authType = OpenSearchAuthType.NONE;
  }
  return config;
});
