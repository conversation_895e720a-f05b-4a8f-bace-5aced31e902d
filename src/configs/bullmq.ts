import { registerAs } from '@nestjs/config';
import { BullMQConfig } from '@src/common/interface/bullmq-config';

export default registerAs(
  'bullmq',
  (): BullMQConfig => ({
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
      password: process.env.REDIS_PASSWORD || undefined,
      db: parseInt(process.env.REDIS_DB || '0', 10),
    },
    defaultJobOptions: {
      removeOnComplete: parseInt(process.env.BULLMQ_REMOVE_ON_COMPLETE || '200', 10),
      removeOnFail: parseInt(process.env.BULLMQ_REMOVE_ON_FAIL || '50', 10),
      attempts: parseInt(process.env.BULLMQ_ATTEMPTS || '3', 10),
      backoff: {
        type: 'exponential',
        delay: parseInt(process.env.BULLMQ_BACKOFF_DELAY || '2000', 10),
      },
    },
  }),
);
