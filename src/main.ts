import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { WinstonLoggerService } from '@vietnam/cnga-middleware';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as path from 'path';
import * as cookieParser from 'cookie-parser';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import { i18nValidationErrorFactory } from 'nestjs-i18n';
import { CustomExceptionHandler } from '@src/exceptions/custom-exception.handler';
import { CsrfInterceptor } from '@common/interceptors/csrf.interceptor';
import { configSession } from '@providers/session.provider';
import { overrideInfrastructureEnvironment } from './common/utils/deploy';
import { NunjucksEnvironment } from '@vietnam/cnga-frontend';
import { ValidCsrfInterceptor } from './common/interceptors/valid-csrf.interceptor';
import '@common/utils/string-format';
import { Server } from 'http';
import { PostMigrationService } from './modules/shared/post-migration.service';

let FileTypeModule;
async function bootstrap() {
  FileTypeModule = await (eval('import("file-type")') as Promise<typeof import('file-type')>);
  // when in .env file has SHOULD_REPLACE_INFRA_SETTING=true
  // then override infrastructure environment
  if (process.env.SHOULD_REPLACE_INFRA_SETTING === 'true') {
    overrideInfrastructureEnvironment();
  }

  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      exceptionFactory: i18nValidationErrorFactory,
    }),
  );

  app.useLogger(app.get(WinstonLoggerService));
  app.enableCors();
  app.useGlobalFilters(new CustomExceptionHandler());
  app.useGlobalInterceptors(new CsrfInterceptor(), new ValidCsrfInterceptor());

  app.use(cookieParser());
  configSession(app);

  const env: any = app.get(NunjucksEnvironment);
  env.addGlobal('assets', {
    url: (path: any) => {
      const appStaticBaseUrl = process.env.APP_STATIC_BASE_URL || '';
      const appStaticPrefix = process.env.APP_STATIC_PREFIX || '';
      if (appStaticBaseUrl) {
        const url = `${appStaticBaseUrl}/${appStaticPrefix}/${path}`;
        // replace multiple slashes with single slash
        return url.replace(/([^:]\/)\/+/g, '$1');
      }

      return `/${path.replace(/^\//, '')}`;
    },
  });
  env.addFilter('escapeFields', function (object, ...fields) {
    if (!object) return JSON.stringify({});
    const newFields = fields.reduce((result, field) => {
      return {
        ...result,
        [field]: object[field]?.escape().nl2br().urlize(),
      };
    }, {});
    return JSON.stringify({
      ...object,
      ...newFields,
    });
  });

  app.useStaticAssets(path.join(__dirname, '..', 'public'), {
    prefix: process.env.APP_STATIC_PREFIX ? `/${process.env.APP_STATIC_PREFIX}/` : '',
  });

  const config = new DocumentBuilder()
    .setTitle('Consulting Message Service')
    .setDescription('Consulting Message Service')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  const server: Server = await app.listen(process.env.APP_PORT || 3000);
  server.keepAliveTimeout = process.env.SERVER_KEEP_ALIVE_TIMEOUT
    ? Number(process.env.SERVER_KEEP_ALIVE_TIMEOUT)
    : 65000;
  server.headersTimeout = process.env.SERVER_HEADERS_TIMEOUT ? Number(process.env.SERVER_HEADERS_TIMEOUT) : 70000;

  console.info(`App is running on port ${process.env.APP_PORT || 3000}`);

  return app;
}
bootstrap().then((app) => {
  app.get(PostMigrationService).runMigrations();
});

export { FileTypeModule };
