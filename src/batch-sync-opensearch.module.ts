import { <PERSON>du<PERSON> } from '@nestjs/common';
import { ConfigProvider } from '@providers/config.provider';
import { DatabaseProvider } from '@providers/database.provider';
import { LoggingProvider } from '@providers/logging.provider';
import { OpensearchProvider } from './providers/opensearch.provider';
import { DocumentService } from './modules/shared/search/services/document.service';
import { SyncAllData } from './modules/shared/search/batch/sync-all-data';

@Module({
  imports: [
    ConfigProvider.register(),
    DatabaseProvider.register(),
    LoggingProvider.register(),
    OpensearchProvider.register(),
  ],
  providers: [DocumentService, SyncAllData],
})
export class BatchSyncOpenSearchModule {}
