import { MessageErrorHandler } from '@golevelup/nestjs-rabbitmq';
import { Channel, ConsumeMessage } from 'amqplib';
import { Logger } from '@nestjs/common';

export const RabbitMQExceptionHandler: MessageErrorHandler = (channel: Channel, msg: ConsumeMessage, error: any) => {
  const logger = new Logger(RabbitMQExceptionHandler.name);
  logger.error(`RabbitMQ Message has error: ${error.message}`);
  if (error instanceof SyntaxError) {
    channel.ack(msg);
  }
};
