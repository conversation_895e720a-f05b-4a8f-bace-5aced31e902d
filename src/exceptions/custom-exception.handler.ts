import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  ForbiddenException,
  HttpException,
  HttpStatus,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { InvalidCsrfTokenException } from './invalid-csrf-token.exception';
import { getI18nContextFromArgumentsHost, I18nValidationException } from 'nestjs-i18n';
import { formatI18nErrors } from 'nestjs-i18n/dist/utils/util';
import { HttpResponseFormatter } from '@common/response/response';
import { routes } from '@src/common/routes';
import { debug } from '../common/utils/debugger';
import { checkIsInAppMobileRequest } from '@src/common/utils/helper';

@Catch()
export class CustomExceptionHandler implements ExceptionFilter {
  private logger = new Logger('CustomExceptionHandler');
  catch(exception: any, host: ArgumentsHost) {
    debug(exception.constructor.name, exception);
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const requestId = request.headers['x-request-id'];
    let view = 'error/';
    let message = '';
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    if (exception instanceof HttpException) {
      message = exception.message;
      status = exception.getStatus();
      switch (true) {
        case exception instanceof NotFoundException:
          view += String(HttpStatus.NOT_FOUND);
          break;
        case exception instanceof InvalidCsrfTokenException:
          view += 'csrf';
          break;
        case exception instanceof ForbiddenException:
          view += String(HttpStatus.FORBIDDEN);
          break;
        case exception instanceof UnauthorizedException:
          if (request.path.startsWith('/admin/')) {
            // save intended url to after login will be redirected intended url
            if (request.accepts('html')) {
              request.terrySession.intendedUrl = request.originalUrl;
              response.cookie('intendedUrl', request.originalUrl, { httpOnly: true });
              return response.redirect(routes.terry.auth.login);
            }
            if (request.accepts('json')) {
              return response.status(HttpStatus.UNAUTHORIZED).json({ isTerryPage: true });
            }
          }

          const isInAppMobileRequest = checkIsInAppMobileRequest(request);
          if (!isInAppMobileRequest) {
            if (request.accepts('html')) {
              return response.redirect(routes.store.auth.login);
            }

            if (request.accepts('json')) {
              return response
                .status(HttpStatus.UNAUTHORIZED)
                .json(HttpResponseFormatter.formatErrorResponse({ message, status }));
            }
          }

          view += String(HttpStatus.UNAUTHORIZED);
          break;
        case exception instanceof InternalServerErrorException:
          view += String(HttpStatus.INTERNAL_SERVER_ERROR);
          break;
        case exception instanceof I18nValidationException:
          const i18nService = getI18nContextFromArgumentsHost(host);
          const errors = formatI18nErrors((exception as I18nValidationException).errors, i18nService.service);
          const errorMessage = [];
          for (const error of errors) {
            const constraints = Object.values(error.constraints);
            if (constraints.length > 0) {
              errorMessage.push(constraints[0]);
            }
          }
          message = errorMessage.join(' \n ');
          status = HttpStatus.BAD_REQUEST;
          view += String(HttpStatus.BAD_REQUEST);
          break;
        default:
          view += String(HttpStatus.BAD_REQUEST);
      }
    } else {
      view += String(HttpStatus.INTERNAL_SERVER_ERROR);
    }
    if (status === HttpStatus.INTERNAL_SERVER_ERROR) {
      this.logger.error(`CustomExceptionHandler: ${exception}`, {
        requestId,
      });
    } else {
      this.logger.warn(`CustomExceptionHandler: ${exception}`, {
        requestId,
      });
    }

    if (request.accepts('html')) {
      return response.status(status).render(view, {
        requestId,
        originUrl: request.originalUrl,
        errorMessage: JSON.stringify({ code: status, message }),
      });
    }
    if (request.accepts('json')) {
      return response.status(status).json(HttpResponseFormatter.formatErrorResponse({ message, status }));
    }
  }
}
