import { AddSessionDataToAdminsTable1692674290696 } from '@curama-auth/migrations/1692674290696-add_session_data_to_admins_table';
import { AddOrgunitIntoTeamsTable1692674348463 } from '@curama-auth/migrations/1692674348463-add_orgunit_into_teams_table';
import { CreateGoogleChannels1694491324838 } from '@curama-auth/migrations/1694491324838-create_google_channels';
import { DynamicModule, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule, TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { DatabaseConfig } from '@src/common/interface/database-config';
import { InitDatabase1692252900585 } from '@src/migrations/1692252900585-init-database';
import { AddDataWorkType1692608558670 } from '@src/migrations/1692608558670-add-data-work-type';
import { AddDataMessageTemplateGroup1692608574828 } from '@src/migrations/1692608574828-add-data-message-template-group';
import { ImportAdminsTeamsFromCsv1700539668095 } from '@src/migrations/1700539668095-import_admins_teams_from_csv';
import { ServiceProvider } from './provider';
import { ImportTeamsFromCsv1700539668075 } from '@src/migrations/1700539668075-import_teams_from_csv';
import { RemoveSubDepartmentInTableAdmins1709029365117 } from '@curama-auth/migrations/1709029365117-remove_sub_department_in_table_admins';
import { UpdateConstraintAdminTeam1692252900586 } from '@src/migrations/1692252900586-update-constraint-admin-team';
import { ImportAdminsStoresFromCsv1711005679101 } from '@src/migrations/1711005679101-import_admins_stores_from_csv';
import { ImportTeamsStoresFromCsv1711005847872 } from '@src/migrations/1711005847872-import_teams_stores_from_csv';
import { ImportAdminFromCsv1700539536184 } from '@src/migrations/1700539536184-import_admins_from_csv';
import { ImportQualityAdminsStoresFromCsv1711099912813 } from '@src/migrations/1711099912813-import_quality_admins_stores_from_csv';
import { AlterTableTeams1718760880216 } from '@src/migrations/1718760880216-alter_table_teams';
import { ImportDataTeamsNew1718789060806 } from '@src/migrations/1718789060806-import_data_teams_new';
import { UpdateDataTeams1718855677336 } from '@src/migrations/1718855677336-update_data_teams';
import { UpdateTeamOrgUnitPath1718941827555 } from '@src/migrations/1718941827555-update-team-org-unit-path';
import { AddQualityTeamNew1719543111983 } from '@src/migrations/1719543111983-add-quality-team-new';
import { AddTableBroadcastMessage1724810842262 } from '../migrations/1724810842262-add-table-broadcast-message';
import { AddUniqueConstraintToInquiryThreads1727338315022 } from '@migrations/1727338315022-add_unique_constraint_to_inquiry_threads';
import { AddColumnLabelIntoMessagesTable1728365361482 } from '@migrations/1728365361482-add_column_label_into_messages_table';
import { AddMessageTemplatePatrol1722854400108 } from '../migrations/1722854400108-add_message_template_patrol';
import { AddActivityIdToTaskTable1735889581867 } from '@src/migrations/1735889581867-add_activity_id_to_task_table';
import { ImportStoresFromCsv1739159669958 } from '@src/migrations/1739159669958-import_stores_from_csv';

@Injectable()
export class TypeOrmConfigService implements TypeOrmOptionsFactory {
  constructor(private readonly configService: ConfigService) {}

  createTypeOrmOptions(connectionName?: string): Promise<TypeOrmModuleOptions> | TypeOrmModuleOptions {
    const databaseConfig: DatabaseConfig = this.configService.get<DatabaseConfig>('database');
    return {
      ...databaseConfig,
      autoLoadEntities: true,
      migrationsRun: true,
      migrations: [
        InitDatabase1692252900585,
        AddSessionDataToAdminsTable1692674290696,
        AddOrgunitIntoTeamsTable1692674348463,
        AddDataWorkType1692608558670,
        AddDataMessageTemplateGroup1692608574828,
        CreateGoogleChannels1694491324838,
        ImportAdminFromCsv1700539536184,
        ImportAdminsTeamsFromCsv1700539668095,
        ImportTeamsFromCsv1700539668075,
        RemoveSubDepartmentInTableAdmins1709029365117,
        UpdateConstraintAdminTeam1692252900586,
        ImportAdminsStoresFromCsv1711005679101,
        ImportTeamsStoresFromCsv1711005847872,
        ImportQualityAdminsStoresFromCsv1711099912813,
        AlterTableTeams1718760880216,
        ImportDataTeamsNew1718789060806,
        UpdateDataTeams1718855677336,
        UpdateTeamOrgUnitPath1718941827555,
        AddQualityTeamNew1719543111983,
        AddTableBroadcastMessage1724810842262,
        AddUniqueConstraintToInquiryThreads1727338315022,
        AddColumnLabelIntoMessagesTable1728365361482,
        AddMessageTemplatePatrol1722854400108,
        AddActivityIdToTaskTable1735889581867,
        ImportStoresFromCsv1739159669958,
      ],
      entities: ['dist/**/*.entity{.ts,.js}'],
    };
  }
}
@Injectable()
export class ReplicaTypeOrmConfigService implements TypeOrmOptionsFactory {
  constructor(private readonly configService: ConfigService) {}

  createTypeOrmOptions(connectionName?: string): Promise<TypeOrmModuleOptions> | TypeOrmModuleOptions {
    const databaseConfig: DatabaseConfig = this.configService.get<DatabaseConfig>('replicaDatabase');
    return {
      ...databaseConfig,
    };
  }
}

export class DatabaseProvider extends ServiceProvider {
  public static register(): DynamicModule {
    return {
      module: DatabaseProvider,
      imports: [
        TypeOrmModule.forRootAsync({
          useClass: TypeOrmConfigService,
        }),
        TypeOrmModule.forRootAsync({
          name: 'replicaConnection',
          useClass: ReplicaTypeOrmConfigService,
        }),
      ],
    };
  }
}
