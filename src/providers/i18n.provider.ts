import { ServiceProvider } from './provider';
import { DynamicModule } from '@nestjs/common';
import { HeaderResolver, I18nModule } from 'nestjs-i18n';
import * as path from 'path';

export class I18nProvider extends ServiceProvider {
  public static register(): DynamicModule {
    return I18nModule.forRoot({
      fallbackLanguage: 'jp',
      loaderOptions: {
        path: path.join(__dirname, '../lang/'),
        watch: true,
      },
      resolvers: [new HeaderResolver(['content-language'])],
    });
  }
}
