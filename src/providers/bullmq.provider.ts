import { ServiceProvider } from './provider';
import { DynamicModule } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigService } from '@nestjs/config';
import { BullMQConfig } from '@src/common/interface/bullmq-config';

export class BullM<PERSON><PERSON><PERSON>ider extends ServiceProvider {
  public static register(): DynamicModule {
    return BullModule.forRootAsync({
      useFactory: (configService: ConfigService) => {
        const bullmqConfig = configService.get<BullMQConfig>('bullmq');
        return {
          connection: bullmqConfig.redis,
          defaultJobOptions: bullmqConfig.defaultJobOptions,
        };
      },
      inject: [ConfigService],
    });
  }

  public static forFeature(queueName: string): DynamicModule {
    return BullModule.registerQueue({
      name: queueName,
    });
  }
}
