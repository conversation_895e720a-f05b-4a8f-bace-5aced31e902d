import { ServiceProvider } from './provider';
import { DynamicModule } from '@nestjs/common';
import { CuramaOpensearchModule } from '@vietnam/curama-opensearch';
import { ConfigService } from '@nestjs/config';
import { OpenSearchConfig } from '@vietnam/curama-opensearch';

export class OpensearchProvider extends ServiceProvider {
  public static register(): DynamicModule {
    return CuramaOpensearchModule.forRootAsync({
      useFactory: async (configService: ConfigService) => {
        const opensearchConfig = configService.get<OpenSearchConfig>('opensearch');
        if (!opensearchConfig) throw new Error('Opensearch config not found');
        return opensearchConfig;
      },
      inject: [ConfigService],
    });
  }
}
