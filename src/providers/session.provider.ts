import * as session from 'express-session';
import RedisStore from 'connect-redis';
import { AUTH_STORE, BearerStrategy, SessionSerializer } from '@vietnam/cnga-middleware';
import * as passport from 'passport';
import { INestApplication } from '@nestjs/common';
import { Redis } from 'ioredis';
import flash = require('connect-flash');
import { Strategy } from 'passport-http-bearer';

export function configSession(app: INestApplication) {
  const authStore = app.get<Redis>(AUTH_STORE);
  app.use(
    session({
      store: new RedisStore({ client: authStore }),
      secret: process.env.SESSION_KEY,
      resave: false,
      saveUninitialized: false,
      cookie: {
        // maxAge: 1 day in milliseconds
        maxAge: 24 * 60 * 60 * 1000,
      },
    }),
  );

  app.use(flash());

  const sessionSerializer = app.get(SessionSerializer);
  passport.deserializeUser(sessionSerializer.deserializeUser);

  const bearerStrategy = app.get<BearerStrategy>(BearerStrategy);
  passport.use(new Strategy(bearerStrategy.verifyFunction));

  app.use(passport.initialize());
  app.use(passport.session());
}
