import { ServiceProvider } from './provider';
import { DynamicModule } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RabbitMQConfig } from '@common/interface/rabbitmq-config';
import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { CuramaRabbitMQModule } from '@vietnam/curama-worker';

export class QueueProvider extends ServiceProvider {
  public static register(): DynamicModule {
    return RabbitMQModule.forRootAsync(CuramaRabbitMQModule, {
      useFactory: (configService: ConfigService) => {
        const rabbitmqConfig = configService.get<RabbitMQConfig>('rabbitmq');
        const rabbitmqScheme = process.env.RABBITMQ_SCHEME ? process.env.RABBITMQ_SCHEME : 'amqps';

        const url = new URL(`${rabbitmqScheme}://${rabbitmqConfig.host}:${rabbitmqConfig.port}`);
        url.pathname = rabbitmqConfig.path;
        url.username = rabbitmqConfig.username;
        url.password = encodeURIComponent(rabbitmqConfig.password);

        return {
          uri: url.toString(),
          channels: {
            default: {
              prefetchCount: 5,
            },
          },
          exchanges: [
            {
              name: 'taskManager',
              type: 'direct',
            },
            {
              name: 'eventManager',
              type: 'fanout',
            },
          ],
        };
      },
      inject: [ConfigService],
    });
  }
}
