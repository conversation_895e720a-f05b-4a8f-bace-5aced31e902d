import { DynamicModule } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { RedisConfig } from '@configs/redis';

export class RedisProvider {
  public static register(): DynamicModule {
    return RedisModule.forRootAsync({
      useFactory: (configService: ConfigService) => {
        const config = configService.get<RedisConfig>('redis');
        return {
          config: {
            ...config,
            maxRetriesPerRequest: null,
          },
        };
      },
      inject: [ConfigService],
    });
  }
}
