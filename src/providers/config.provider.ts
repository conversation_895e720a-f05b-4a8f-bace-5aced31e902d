import { ServiceProvider } from './provider';
import { DynamicModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import database from '../configs/database';
import rabbitmq from '../configs/rabbitmq';
import logging from '../configs/logging';
import redis from '../configs/redis';
import py3 from '../configs/py3';
import http from '../configs/http';
import cannaryReleaseStore from '../configs/cannary-release-store';
import replicaDatabase from '../configs/replicaDatabase';
import opensearch from '@src/configs/opensearch';
import notification from '@src/configs/notification';
import bullmq from '@src/configs/bullmq';

export class ConfigProvider extends ServiceProvider {
  public static register(): DynamicModule {
    return ConfigModule.forRoot({
      isGlobal: true,
      load: [
        database,
        replicaDatabase,
        rabbitmq,
        logging,
        py3,
        redis,
        http,
        cannaryReleaseStore,
        opensearch,
        notification,
        bullmq,
      ],
    });
  }
}
