import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { BatchSyncOpenSearchModule } from './batch-sync-opensearch.module';
import { SyncAllData } from './modules/shared/search/batch/sync-all-data';

async function bootstrap() {
  Logger.log('OpenSearch Sync Service staring....');
  Logger.log('Opensearch Endpoint is: ' + process.env.OPENSEARCH_NODE_URL);
  const app = await NestFactory.createApplicationContext(BatchSyncOpenSearchModule);
  const syncService = app.get(SyncAllData);
  await syncService.run();
  Logger.log('OpenSearch Sync Service finished....');
  return app;
}

bootstrap();
