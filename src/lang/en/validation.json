{"isNotEmpty": "{property} is required", "isInt": "{property} must be an integer", "isIn": "{property} must be one of the following values: {constraints}", "isString": "{property} must be a string", "isArray": "{property} must be an array", "isDate": "{property} must be a date", "formatDate": "{property} must be formatted as YYYY-MM-DD", "formatTime": "{property} must be formatted as HH:mm", "maxLengthMessage": "Please write a message within {constraints} characters.", "minLengthMessage": "Write a message of {constraints} characters or more.", "formatUUID": "{property} must be set UUID version 4", "invalidUUID": "Please enter your UUID."}