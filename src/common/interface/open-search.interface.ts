export interface InquiryThreadOpenSearchQuery {
  thread_id: string;
  store_id: string;
  team_id: string;
  admin_id: string;
  thread_status_id: number;
  message_id: string;
  message: string;
  thread_updated_at: string;
}

export interface OpenSearchHit<T> {
  _index: string;
  _type: string;
  _id: string;
  _score: number;
  _source: T;
}

interface OpenSearchHits<T> {
  total: {
    value: number;
    relation: string;
  };
  max_score: number;
  hits: OpenSearchHit<T>[];
}

interface OpenSearchAggregations {
  total_groups: {
    value: number;
  };
}

export interface OpenSearchResponse<T> {
  took: number;
  timed_out: boolean;
  _shards: {
    total: number;
    successful: number;
    skipped: number;
    failed: number;
  };
  hits: OpenSearchHits<T>;
  aggregations: OpenSearchAggregations;
}
