import { sign } from 'jsonwebtoken';

function getSignature(imgPath) {
  const secret = process.env.PRIVATE_FILE_JWT_SECRET;
  const aud = process.env.PRIVATE_FILE_JWT_AUD;
  const exp = process.env.PRIVATE_FILE_JWT_EXPIRES_IN;

  return sign({ path: imgPath }, secret, { algorithm: 'HS256', audience: aud, expiresIn: exp || '24h' });
}

export function getPrivateFile(path, size = '0x0') {
  const secret = getSignature(path);
  return `${process.env.PRIVATE_FILE_HANDLER_URL}/${path}?size=${size}&s=${secret}`;
}
