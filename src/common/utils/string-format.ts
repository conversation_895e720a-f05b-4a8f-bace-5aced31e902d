declare global {
  interface String {
    escape(): string;
    urlize(): string;
    nl2br(): string;
  }
}

const escapeMap = {
  '&': '&amp;',
  '"': '&quot;',
  "'": '&#39;',
  '<': '&lt;',
  '>': '&gt;',
  '\\': '&#92;',
};

const escapeRegex = /[&"'<>\\]/g;
const puncRe = /^(?:\(|<|&lt;)?(.*?)(?:\.|,|\)|\n|&gt;)?$/;

export function lookupEscape(ch) {
  return escapeMap[ch];
}
function isValidUrl(string) {
  try {
    const url = new URL(string);
    if (['http:', 'https:'].includes(url.protocol)) return true;
    return false;
  } catch (_) {
    return false;
  }
}
String.prototype.escape = function () {
  if (this === null || this === undefined) {
    return '';
  }
  return this.replace(escapeRegex, lookupEscape);
};
String.prototype.urlize = function () {
  const words = this.split(/(\s+)/)
    .filter((word) => {
      // If the word has no length, bail. This can happen for str with
      // trailing whitespace.
      return word && word.length;
    })
    .map((word) => {
      const matches = word.match(puncRe);
      const possibleUrl = matches ? matches[1] : word;

      // url that starts with http or https
      if (isValidUrl(possibleUrl)) {
        return `<a href="${possibleUrl}" target="_blank">${possibleUrl}</a>`;
      }

      return word;
    });

  return words.join('');
};

String.prototype.nl2br = function () {
  if (this === null || this === undefined) {
    return '';
  }
  return this.replace(/\r\n|\n/g, '<br/>\n');
};
