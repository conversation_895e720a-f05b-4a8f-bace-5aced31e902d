import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
dayjs.extend(utc);
dayjs.extend(timezone);

export const JP_WEEKDAYS = ['日', '月', '火', '水', '木', '金', '土'];
export const JP_YYYYMD = 'YYYY年M月D日';
export const JP_YYYYMD_HHMM = 'YYYY年M月D日 HH:mm';
export const JP_YYYYMD_HHMM_TZ = 'YYYY/MM/DD (here) HH:mm';
export const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
export const TZ_JP = 'Asia/Tokyo';
export const JP_MMDD = 'MM月DD日';
export const JP_YYYYMMDD = 'YYYY年MM月DD日';
export const API_DATE_FORMAT_WITH_TZ = 'YYYY-MM-DDTHH:mm:ssZ';

export function formatToDateTime(date: Date | string, format = DATE_TIME_FORMAT): string {
  return dayjs(date).format(format);
}

export function formatToDateTimeWithTimezone(date: Date | string, format = DATE_TIME_FORMAT, tz = TZ_JP): string {
  return dayjs.utc(date).tz(tz).format(format);
}

export function formatDateWithJapaneseWeekday(
  date: Date | string,
  format = JP_YYYYMD_HHMM_TZ,
  tz = 'Asia/Tokyo',
): string {
  const dateInTz = dayjs.utc(date).tz(tz);
  const dayOfWeekIndex = dateInTz.day();
  const formatWithDay = format.replace('(here)', `(${JP_WEEKDAYS[dayOfWeekIndex]})`);
  return dateInTz.format(formatWithDay);
}

export function getStartOfDay(date: Date | string, format = DATE_TIME_FORMAT): Date {
  return dayjs.utc(date).tz(TZ_JP).startOf('day').toDate();
}

export function getEndOfDay(date: Date | string, format = DATE_TIME_FORMAT): Date {
  return dayjs.utc(date).tz(TZ_JP).endOf('day').toDate();
}

export function formatDateWithComparisonYear(date: Date | string): string {
  if (!date) return '';
  const now = dayjs();
  const input = dayjs(date);

  return now.isSame(input, 'year') ? input.format(JP_MMDD) : input.format(JP_YYYYMMDD);
}

export function getStartDateNowString(): string {
  return dayjs().startOf('day').format(DATE_TIME_FORMAT);
}
