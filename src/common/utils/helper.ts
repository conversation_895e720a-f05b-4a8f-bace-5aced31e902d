import { NotFoundException } from '@nestjs/common';
import { ThreadType, ThreadTypeKey } from '../enums/thread-type';
import { version as uuidVersion } from 'uuid';
import { validate as uuidValidate } from 'uuid';
import { ACCOUNT_TYPES } from '@vietnam/cnga-middleware';

/**
 * get ThreadType from ThreadTypeKey
 * @param threadTypeKey ThreadTypeKey enum (consultants, quality-management)
 * @returns ThreadType enum (1, 2)
 */
export const getThreadTypeIdByKey = (threadTypeKey: ThreadTypeKey): ThreadType => {
  switch (threadTypeKey) {
    case ThreadTypeKey.CuramaThread:
      return ThreadType.CuramaThread;
    case ThreadTypeKey.QualityThread:
      return ThreadType.QualityThread;
    default:
      throw new NotFoundException(`Thread type id is not found by key ${threadTypeKey}.`);
  }
};

/**
 * get ThreadTypeKey from ThreadType
 * @param threadTypeId ThreadType enum (1, 2)
 * @returns ThreadTypeKey
 */
export const getThreadTypeKeyById = (threadTypeId: ThreadType): ThreadTypeKey => {
  switch (threadTypeId) {
    case ThreadType.CuramaThread:
      return ThreadTypeKey.CuramaThread;
    case ThreadType.QualityThread:
      return ThreadTypeKey.QualityThread;
    default:
      throw new NotFoundException(`Thread type key is not found by id ${threadTypeId}.`);
  }
};

export const validationExceptionArrayMessages = (errors: any) => {
  const messages = errors.map((error: any) => {
    return error.constraints ? Object.values(error.constraints).join(', ') : 'Validation failed';
  });

  return messages;
};

export function checkIsInAppMobileRequest(req) {
  const path = req.originalUrl;
  return path.startsWith('/shopapp');
}

export function getParentTeamNameByPath(orgUnitPath: string) {
  if (!orgUnitPath) return '';
  const splitPath = orgUnitPath.split('/');
  return splitPath[splitPath.length - 2] ?? '';
}

export function uuidValidateV4(uuid) {
  if (!uuid?.trim()) return false;
  return uuidValidate(uuid) && uuidVersion(uuid) === 4;
}
/**
 * Use a regular expression to match {{ param }} and replace it with the corresponding value
 * @param text: string; params: Object params
 * @returns string
 */
export const replaceParams = (text: string, params: any) => {
  return text.replace(/{{\s*(\w+)\s*}}/g, (match, paramName) => {
    return params.hasOwnProperty(paramName) ? params[paramName] : '';
  });
};

/**
 * Get common request headers for Python3 services
 * @param request
 */
export const getPy3CommonRequestHeaders = (request: any): any => {
  const isAdmin = request.terrySession?.adminId;
  const xClientId = isAdmin ? request.terrySession?.adminId : request.user?.clientId || request.user?.ownerId;
  const xClientType = isAdmin ? ACCOUNT_TYPES.Admin : request.user?.accountTypeId || request.user?.accountType;

  return {
    'x-request-id': request.headers?.['x-request-id'] as string,
    'x-client-id': xClientId,
    'x-client-type': xClientType,
    'x-api-fw-principal-key': process.env.API_FW_PRINCIPAL_KEY || '',
  };
};

export const getLatestMessageCacheKey = (storeId: string) => {
  return `consulting-message:latest_message:${storeId}`;
};
