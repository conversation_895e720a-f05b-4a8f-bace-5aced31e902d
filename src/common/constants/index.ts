export const ThreadStatus = {
  1: '未読',
  2: '確認中',
  3: '既読',
};

export const TaskStatusMapping = {
  1: '対応待ち',
  2: '掲載停止中',
  3: '掲載停止解除待ち',
  4: '退店決定済み',
  5: '終了',
};

export const BroadcastMessageStatus = {
  Draft: 1,
  Unapproved: 2,
  Approved: 3,
  SendingScheduled: 4,
  Sent: 5,
  Sending: 6,
};

export const BroadcastMessageStatusLabel = {
  [BroadcastMessageStatus.Draft]: '下書き',
  [BroadcastMessageStatus.Unapproved]: '未承認',
  [BroadcastMessageStatus.Approved]: '承認済み',
  [BroadcastMessageStatus.SendingScheduled]: '送信設定完了',
  [BroadcastMessageStatus.Sent]: '送信済み',
  [BroadcastMessageStatus.Sending]: '送信中',
};

export const QUALITY_TEAM_NAME = '品質管理';

export const StoreStatus = {
  NewlyApplied: 1,
  InReview: 2,
  Published: 3,
  OnHold: 4,
  Suspended: 5,
  Closed: 6,
  ForcedToClose: 7,
  ReviewFailed: 8,
  ReviewExceededTimeLimit: 9,
  RegistrationCancelled: 10,
};

export const StoreStatusLabel = {
  [StoreStatus.NewlyApplied]: '新規申し込み',
  [StoreStatus.InReview]: '審査中',
  [StoreStatus.Published]: '出店中',
  [StoreStatus.OnHold]: '保留中',
  [StoreStatus.Suspended]: '停止中',
  [StoreStatus.Closed]: '退店中',
  [StoreStatus.ForcedToClose]: '強制退店',
  [StoreStatus.ReviewFailed]: '審査落ち',
  [StoreStatus.ReviewExceededTimeLimit]: '審査期限切れ',
  [StoreStatus.RegistrationCancelled]: '登録キャンセル',
};

export const StoppedStoreStatuses = [
  StoreStatus.Closed,
  StoreStatus.ForcedToClose,
  StoreStatus.ReviewFailed,
  StoreStatus.ReviewExceededTimeLimit,
  StoreStatus.RegistrationCancelled,
];

export const ActivityType = {
  Patrol: 'AIメッセージパトロール',
};

export const ValidFileTypes = ['image/png', 'image/jpeg', 'application/pdf'];
export const LIMIT_5_MB = 5 * 1024 * 1024;

export const SHOP_INBOX_TAB = 'messages';

export const SYNC_OPENSEARCH_MESSAGE_MQ_TASK = 'sync-opensearch-message-task';

export const CONCURRENT_MESSAGE_SIZE_BATCH = parseInt(process.env.CONCURRENT_MESSAGE_SIZE_BATCH || '500', 10);
export const CONCURRENT_MESSAGE_MINUTE_DELAY = parseInt(process.env.CONCURRENT_MESSAGE_MINUTE_DELAY || '3600000', 10);
export const CONCURRENT_MESSAGE_FILENAME_SENT_SUCCESS = 'sent_success';
export const CONCURRENT_MESSAGE_FILENAME_SENT_FAILED = 'sent_failed';
export const CONCURRENT_MESSAGE_FOLDER_NAME = 'bulk-messages';
export const CONCURRENT_MESSAGE_MQ_NAME = 'consulting-message-task';

export const REDIS_CACHE_LATEST_MESSAGE_EXPIRED_TIME = 3600; // 1 hour

// BullMQ Queue Names
export const BULLMQ_MESSAGE_BROADCAST_QUEUE = 'message-broadcast';

// BullMQ Job Types
export const BULLMQ_JOB_SEND_MESSAGE_BATCH = 'send-message-batch';
