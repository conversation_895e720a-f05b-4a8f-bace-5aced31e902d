import { UseInterceptors, applyDecorators } from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { ValidFileMultiInputInterceptor } from '@common/interceptors/valid-file-multi-input.interceptor';

interface FileField {
  name: string;
  fileType: string[];
  maxCount?: number;
  fileSize?: number;
}

export function MultiInputFiles(fields: FileField[], localOptions: MulterOptions = {}, fileInterceptors = []) {
  const validFileTypesAndSizes = fields.reduce((acc, field) => {
    acc[field.name] = { types: field.fileType, size: field.fileSize };
    return acc;
  }, {});
  return applyDecorators(
    UseInterceptors(
      FileFieldsInterceptor(
        fields.map((field) => ({
          name: field.name,
          maxCount: field.maxCount ?? 1,
        })),
        localOptions,
      ),
      new ValidFileMultiInputInterceptor(validFileTypesAndSizes),
      ...fileInterceptors,
    ),
  );
}
