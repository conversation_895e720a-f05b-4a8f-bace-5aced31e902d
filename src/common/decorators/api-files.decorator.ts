import { UseInterceptors, applyDecorators } from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { ValidFileTypeInterceptor } from '../interceptors/valid-file-type.interceptor';
import { LIMIT_5_MB } from '../constants';
export function ApiFiles(
  fieldName = 'files',
  maxCount: number = null,
  localOptions: MulterOptions = {},
  interceptors = [],
) {
  return applyDecorators(
    UseInterceptors(
      FilesInterceptor(fieldName, maxCount, {
        limits: {
          fileSize: LIMIT_5_MB,
        },
        ...localOptions,
      }),
      ValidFileTypeInterceptor,
      ...interceptors,
    ),
  );
}
