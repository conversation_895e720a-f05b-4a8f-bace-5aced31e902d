import { ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments } from 'class-validator';

@ValidatorConstraint({ name: 'minLengthCustom', async: false })
export class MinLengthValidator implements ValidatorConstraintInterface {
  /**
   * @private
   */
  private errors: { [key: string]: { [key: string]: string } } = {};

  /**
   * @param value
   * @param args
   */
  async validate(value: string, args: ValidationArguments) {
    if (!args.constraints || !args.constraints[0]) {
      return false;
    }

    const errors = {};

    if (value.trim().length < args.constraints[0]) {
      errors[0] = `${args.constraints[0]} 文字以上で入力してください。`;
      this.errors[args.property] = errors;
      return false;
    }

    return true;
  }

  /**
   * @param args
   */
  defaultMessage(args: ValidationArguments) {
    return JSON.stringify(this.errors[args.property]);
  }
}
