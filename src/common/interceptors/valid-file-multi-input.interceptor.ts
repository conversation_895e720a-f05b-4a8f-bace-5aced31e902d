import {
  <PERSON><PERSON><PERSON><PERSON>,
  Execution<PERSON>ontext,
  Injectable,
  NestInterceptor,
  UnsupportedMediaTypeException,
  BadRequestException,
} from '@nestjs/common';
import { FileTypeModule } from '@src/main';
import { extname } from 'path';

interface ValidFileTypeAndSize {
  [key: string]: {
    types: string[];
    size?: number;
  };
}

@Injectable()
export class ValidFileMultiInputInterceptor implements NestInterceptor {
  constructor(private readonly validFileTypesAndSizes: ValidFileTypeAndSize) {}

  async intercept(context: ExecutionContext, next: CallHandler<any>) {
    const request = context.switchToHttp().getRequest();
    for (const fieldName in request.files) {
      const files = request.files[fieldName];
      if (files) {
        for (const file of files) {
          // Validate type file
          const fileType = await FileTypeModule.fileTypeFromBuffer(file.buffer);
          const mimeType = fileType ? fileType.mime : `text/${extname(file.originalname).slice(1)}`;
          if (!this.validFileTypesAndSizes[fieldName].types.includes(mimeType)) {
            throw new UnsupportedMediaTypeException(`The file type for field ${fieldName} is not supported`);
          }

          // Validate size file
          const fileSizeLimit = this.validFileTypesAndSizes[fieldName].size;
          if (fileSizeLimit && file.size > fileSizeLimit) {
            throw new BadRequestException(`The file size for field ${fieldName} exceeds the allowed limit`);
          }
        }
      }
    }
    return next.handle();
  }
}
