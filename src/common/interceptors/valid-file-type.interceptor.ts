import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
  UnsupportedMediaTypeException,
} from '@nestjs/common';
import { ValidFileTypes } from '../constants';
import { FileTypeModule } from '@src/main';

@Injectable()
export class ValidFileTypeInterceptor implements NestInterceptor {
  async intercept(context: ExecutionContext, next: CallHandler<any>) {
    const request = context.switchToHttp().getRequest();
    for (const file of request.files) {
      const fileType = await FileTypeModule.fileTypeFromBuffer(file.buffer);
      if (!ValidFileTypes.includes(fileType?.mime)) {
        throw new UnsupportedMediaTypeException(`File type is not support`);
      }
    }
    return next.handle();
  }
}
