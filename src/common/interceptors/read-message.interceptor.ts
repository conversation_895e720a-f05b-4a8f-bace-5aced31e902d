import { Call<PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor, UnauthorizedException } from '@nestjs/common';
import { Observable } from 'rxjs';
import { debug } from '../utils/debugger';
import { InquiryThreadService } from '@src/modules/shared/inquiry-thread/inquiry-thread.service';
import { getThreadTypeIdByKey } from '../utils/helper';
import { ACCOUNT_TYPES } from '@vietnam/cnga-middleware';

@Injectable()
export class ReadMessageInterceptor implements NestInterceptor {
  constructor(private readonly inquiryThreadService: InquiryThreadService) {}

  async intercept(context: ExecutionContext, next: CallHandler<any>): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    if (!request?.user?.ownerId && !request?.user?.clientId) {
      throw new UnauthorizedException();
    }

    const user = request.user;
    debug('User Info', user);

    const storeId = user.ownerId || user.clientId;
    const threadTypeId = getThreadTypeIdByKey(request?.params?.type);
    const thread = await this.inquiryThreadService.getThreadByType(storeId, threadTypeId);
    if (thread && user.accountType !== ACCOUNT_TYPES.Admin) {
      this.inquiryThreadService.updateThreadIsRead(thread);
    }

    return next.handle();
  }
}
