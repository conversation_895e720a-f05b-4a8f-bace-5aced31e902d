import { BaseEntity, CreateDate<PERSON>olumn, PrimaryGeneratedColumn, UpdateDateColumn, VirtualColumn } from 'typeorm';
import { formatToDateTimeWithTimezone, JP_YYYYMD_HHMM } from '@src/common/utils/date-util';
import { ApiProperty } from '@nestjs/swagger';

export class AbstractEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty()
  id: string;

  @CreateDateColumn({
    name: 'created_at',
  })
  @ApiProperty()
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updated_at',
  })
  @ApiProperty()
  updatedAt: Date;

  @VirtualColumn({
    query: (alias) => `${alias}.updated_at`,
    transformer: {
      to: (value) => value,
      from: (value) => {
        return formatToDateTimeWithTimezone(value, JP_YYYYMD_HHMM);
      },
    },
  })
  displayUpdatedAt: string;
}
