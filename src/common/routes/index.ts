const TERRY_ROUTE_PREFIX = 'admin';
const STORE_ROUTE_PREFIX = 'shop';
const GENERATE_TERRY_ROUTE = (path: string) => `/${TERRY_ROUTE_PREFIX}/${path}`.replace(/\/\//g, '/');
export const GENERATE_STORE_ROUTE = (path: string) => `/${STORE_ROUTE_PREFIX}/${path}`.replace(/\/\//g, '/');
export const routes = {
  terry: {
    thread: {
      viewList: GENERATE_TERRY_ROUTE('consulting-message/messages'),
      viewByStore: GENERATE_TERRY_ROUTE('consulting-message/stores/:storeId/messages'),
      viewDetail: GENERATE_TERRY_ROUTE('consulting-message/stores/:storeId/messages/:type'),
    },
    broadcastMessage: {
      viewList: GENERATE_TERRY_ROUTE('consulting-message/broadcast-message'),
      createTemplate: GENERATE_TERRY_ROUTE('consulting-message/broadcast-message/create'),
      editTemplate: GENERATE_TERRY_ROUTE('consulting-message/broadcast-message/:id'),
      previewTemplate: GENERATE_TERRY_ROUTE('consulting-message/broadcast-message/:id/preview'),
      trackingTemplate: GENERATE_TERRY_ROUTE('consulting-message/api/broadcast-message/tracking/:id'),
      detailHistory: GENERATE_TERRY_ROUTE('consulting-message/broadcast-message/:broadcastMessageId/detail-history'),
      getStoreName: GENERATE_TERRY_ROUTE('consulting-message/api/broadcast-message/stores'),
      store: GENERATE_TERRY_ROUTE('consulting-message/api/broadcast-message/'),
      update: GENERATE_TERRY_ROUTE('consulting-message/api/broadcast-message/:id'),
      countCSV: GENERATE_TERRY_ROUTE('consulting-message/api/broadcast-message/count-csv'),
      getById: GENERATE_TERRY_ROUTE('consulting-message/api/broadcast-message/:id'),
      setScheduleTime: GENERATE_TERRY_ROUTE('consulting-message/api/broadcast-message/handle-time/:id'),
    },
    admin: {
      list: GENERATE_TERRY_ROUTE('consulting-message/api/admins'),
    },
    auth: {
      login: GENERATE_TERRY_ROUTE('consulting-message/login'),
      logout: GENERATE_TERRY_ROUTE('consulting-message/logout'),
      callback: GENERATE_TERRY_ROUTE('consulting-message/authenticate'),
      authorization: GENERATE_TERRY_ROUTE('consulting-message/authorization'),
      getPolicy: GENERATE_TERRY_ROUTE('consulting-message/api/authorization/:adminId/policies'),
      postPolicy: GENERATE_TERRY_ROUTE('consulting-message/api/authorization/:adminId/policies'),
    },
    draft: {
      delete: GENERATE_TERRY_ROUTE('consulting-message/api/drafts/:draftId'),
      upsert: GENERATE_TERRY_ROUTE('consulting-message/api/drafts'),
      convert: GENERATE_TERRY_ROUTE('consulting-message/api/drafts/:draftId/send'),
    },
    messageTemplate: {
      viewList: GENERATE_TERRY_ROUTE('consulting-message/templates'),
      subCategory: GENERATE_TERRY_ROUTE('consulting-message/api/templates/sub-categories'),
      getTemplate: GENERATE_TERRY_ROUTE('consulting-message/api/templates'),
      save: GENERATE_TERRY_ROUTE('consulting-message/api/templates'),
      getDetail: GENERATE_TERRY_ROUTE('consulting-message/api/templates/:id'),
      delete: GENERATE_TERRY_ROUTE('consulting-message/api/templates/:id'),
    },
    message: {
      delete: GENERATE_TERRY_ROUTE('consulting-message/api/messages/:id'),
      getPagingMessage: GENERATE_TERRY_ROUTE('consulting-message/api/messages'),
    },
    ngWord: {
      viewList: GENERATE_TERRY_ROUTE('consulting-message/ng-words'),
      save: GENERATE_TERRY_ROUTE('consulting-message/api/ng-words'),
      delete: GENERATE_TERRY_ROUTE('consulting-message/api/ng-words/:id'),
    },
    task: {
      viewList: GENERATE_TERRY_ROUTE('consulting-message/tasks'),
      getPagingTask: GENERATE_TERRY_ROUTE('consulting-message/api/tasks'),
      upsert: GENERATE_TERRY_ROUTE('consulting-message/api/tasks'),
      getForCreate: GENERATE_TERRY_ROUTE('consulting-message/api/tasks/static-data'),
      getForEdit: GENERATE_TERRY_ROUTE('consulting-message/api/tasks/:id/static-data'),
    },
  },
  store: {
    auth: {
      login: GENERATE_STORE_ROUTE('login'),
    },
    message: {
      webDetail: GENERATE_STORE_ROUTE('inbox/detail/curama/:type/'),
      mobileDetail: '/shopapp/consulting-message/v1/threads/:type/',
      webSave: GENERATE_STORE_ROUTE('inbox/detail/curama/:type/'),
      mobileSave: '/shopapp/consulting-message/v1/threads/:type/messages/',
      webGetList: GENERATE_STORE_ROUTE('inbox/detail/curama/:type/messages/'),
      mobileGetList: '/shopapp/consulting-message/v1/threads/:type/messages/',
    },
  },
  internalApi: {
    assignments: '/internal-api/v1/stores/:storeId/admin-assignments/',
    latestMessage: '/internal-api/v1/stores/:storeId/consulting-message/summary/',
    getMessageTemplateGroupByCategory: '/internal-api/v1/templates/',
    selectPatrolTemplate: '/internal-api/v1/templates/:templateId/save-message/',
    admins: '/internal-api/v1/admins/',
    teams: '/internal-api/v1/teams/',
  },
};
