import { Request } from 'express';
import { PAGINATOR } from '../constants/paginator';

export interface Pagination {
  perPage?: number;
  pageNumber?: number;
  total?: number;
  request?: Pick<Request, 'query' | 'path'>;
}

export class TerryPagination implements Pagination {
  perPage: number;
  pageNumber: number;
  total: number;
  request: Pick<Request, 'query' | 'path'>;

  constructor(data: Pagination) {
    this.perPage = data?.perPage || PAGINATOR.TERRY;
    this.pageNumber = data?.pageNumber || 1;
    this.total = data?.total || 0;
    this.request = data?.request || { query: {}, path: '' };
  }
}
