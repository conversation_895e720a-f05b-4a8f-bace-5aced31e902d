{"Curama": {"entityTypes": {"User": {"memberOfTypes": [], "shape": {"type": "Record", "attributes": {"custom.id": {"required": false, "type": "String"}, "email": {"type": "String", "required": false}}}}, "Policy": {"shape": {"type": "Record", "attributes": {}}, "memberOfTypes": []}, "ConsultingMessage": {"memberOfTypes": [], "shape": {"attributes": {"type": {"required": false, "type": "String"}, "id": {"type": "String", "required": false}}, "type": "Record"}}}, "actions": {"editPolicy": {"memberOf": [], "appliesTo": {"principalTypes": ["User"], "resourceTypes": ["Policy"], "context": {"attributes": {}, "type": "Record"}}}, "deleteConsultingMessage": {"memberOf": [], "appliesTo": {"context": {"type": "Record", "attributes": {}}, "principalTypes": ["User"], "resourceTypes": ["ConsultingMessage"]}}}}}