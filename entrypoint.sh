#!/bin/sh

DEPLOY_APP=$DEPLOY_APP
RUN_INITIAL_COMMAND=$RUN_INITIAL_COMMAND

run_http() {
  if [ "$RUN_INITIAL_COMMAND" = "true" ]; then
    echo "init verifify permission policy template, sync data from google once";
    node dist/src/command.js curama:init
  fi

  exec node dist/src/main.js
}

case "$DEPLOY_APP" in
  app)
    run_http
    ;;
  worker)
    exec node dist/src/worker.js
    ;;
  syncos)
    exec node dist/src/batch-sync-opensearch.js
    ;;
  batch)
    exec node dist/src/cron-script.js
    ;;
  * )
    echo "Select app want to deploy"
    exit 2
esac
