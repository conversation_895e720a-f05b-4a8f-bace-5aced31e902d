image: node:18-alpine

merge-request-approval-check:
  stage: review
  extends: .basic-approval-check

monetary-workflow-check:
  stage: review
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always
  extends: .check-monetary-workflow

sre-monetary-ignore-check:
  stage: review
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always
  extends: .monetary-ignore-require-sre-review

stages:
  - test
  - checklist
  - review
  - build-docker
  - check-pkg-version
 
npm-prepare:
  stage: test
  before_script:
    - echo "configuring npm registry... and install dependencies"
    - npm config set @vietnam:registry=https://gitlab.wkwk.io/api/v4/packages/npm/
    - npm config set -- //gitlab.wkwk.io/api/v4/packages/npm/:_authToken=${CI_JOB_TOKEN}
    - npm config set strict-ssl=false
    - npm ci --quite
  script:
    - echo "test stage environment is ready."
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - node_modules/
    policy: pull-push

npm-lint:
  stage: test
  needs: [npm-prepare]
  extends: npm-prepare
  script:
    - npm run lint
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

npm-tsc:
  stage: test
  needs: [npm-prepare]
  extends: npm-prepare
  script:
    - npx tsc
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

npm-test:
  stage: test
  needs: [npm-prepare]
  extends: npm-prepare
  script:
    - npm run test:cov
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

npm-audit-critical:
  stage: test
  needs: [npm-prepare]
  extends: npm-prepare
  script:
    - npm audit --audit-level critical
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

feature-build-curama-consulting-message:
  stage: build-docker
  image: plugins/ecr:20.10.9
  variables:
    ECR_REPO: $ECR_BASE_URL_JP/curama/consulting-message
    BUILD_DOCKERFILE: ./Dockerfile
    SERVICE_NAME: consulting-message
    BUILD_ARGS: --build-arg CI_JOB_TOKEN=${CI_JOB_TOKEN}
  before_script:
    - export TAG1="$(echo $CI_COMMIT_REF_NAME | sed 's/\//-/g')"
  after_script:
    - export TAG1="$(echo $CI_COMMIT_REF_NAME | sed 's/\//-/g')"
    - docker network rm ${SERVICE_NAME}-${CI_COMMIT_SHA}-${CI_PIPELINE_SOURCE}
    - docker rmi ${SERVICE_NAME}-${CI_COMMIT_SHA}-${CI_PIPELINE_SOURCE}
    - docker rmi $ECR_REPO:$TAG1
    - docker rmi $ECR_REPO:$TAG2
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^(feature-build|feature|bugfix|refactor|test|design)\/.*/'
      when: always
  extends: .docker-build-template

build-curama-consulting-message-jp-release:
  stage: build-docker
  image: plugins/ecr:20.10.9
  variables:
    ECR_REPO: $ECR_BASE_URL_JP/curama/consulting-message
    BUILD_DOCKERFILE: ./Dockerfile
    SERVICE_NAME: consulting-message
    BUILD_ARGS: --build-arg CI_JOB_TOKEN=${CI_JOB_TOKEN}
  rules:
    - if: '$CI_COMMIT_TAG != null'
      when: always
  extends: .docker-release-template

build-curama-consulting-message-jp-master-kaizen-latest:
  stage: build-docker
  image: plugins/ecr:20.10.9
  variables:
    ECR_REPO: $ECR_BASE_URL_JP/curama/consulting-message
    BUILD_DOCKERFILE: ./Dockerfile
    SERVICE_NAME: consulting-message
    BUILD_ARGS: --build-arg CI_JOB_TOKEN=${CI_JOB_TOKEN}
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "kaizen"'
      when: always
  extends: .docker-build-template

checklist:
  stage: checklist
  variables:
    VERIFIABLE_CHECKLISTS: "VulnerabilityChecklist"
  extends: .checklist

include:
  - '.gitlab-templates/.check-package-version.yml'
  - project: 'minma/ci-templates'
    ref: 'master'
    file: 'docker-template.yml'
  - project: 'minma/ci-templates'
    ref: "master"
    file: 'basic-approval-check.yml'
  - project: 'minma/ci-templates'
    ref: 'master'
    file: 'check-monetary-workflow.yml'
  - project: "minma/ci-templates"
    ref: 'master'
    file: "checklist.yml"
