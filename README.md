## DESCRIPTION
Consulting Message Microservice

## Local development

### Install dependency
```bash
$ npm install
```
or (MacOS)
```bash
$ npm install --arch=x64 --platform=darwin
```
### Build and run the app
```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```
## Test

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## HOW TO DEPLOY CONSULTING MESSAGE SERVICE

### Configure environment variables

Please refer to the `.env.example` file to create your `.env` file. When deploying to production, you must set all the required environment variables listed in the `.env.example` file.

Please configure the `DEPLOY_APP` environment variable to select the application to deploy. Accepted values: `app` or `worker`.

When deploying for the first time, you need to add the build environment `RUN_COMMAND_INIT=true` to the run command `curama:init` when executing in the production environment.
Ensure that you have configured all the necessary environment variables as listed in the .env.example file and that the Amazon verified permission policy store and policy schema have been created. After completing these steps, you can omit this argument in subsequent deployments.
The goal is to create Amazon verified permission policy templates, synchronize users from Google, establish mappings between Google and the database, and register a webhook to listen for changes from Google.

#### Configure Cognito environment variables

```bash
# (Required) AWS configuration
# AWS Cognito App Client Domain. Please includes https://
COGNITO_OAUTH2_DOMAIN=
# AWS Cognito App Client ID
COGNITO_OAUTH2_CLIENT_ID=
# AWS Cognito App Client Secret
COGNITO_OAUTH2_CLIENT_SECRET=
# AWS Cognito App Client Scope
COGNITO_OAUTH2_SCOPES=aws.cognito.signin.user.admin,email,openid,phone,profile
```

#### Configure verified permission environment variables

```bash
# Verified permission policy store id that get from aws verified permission service
VERIFIED_PERMISSION_POLICY_STORE_ID=
# Verified permission user pool id that related to verified permission policy store
VERIFIED_PERMISSION_USER_POOL_ID=
```

#### Configure Google environment variables

```bash
# (Require) Google app account service to synchronize data from google
# Must be admin email of google workspace
GOOGLE_APP_ACCOUNT_SERVICE_EMAIL=
# Must be domain of google workspace
GOOGLE_APP_ACCOUNT_SERVICE_DOMAIN=
# Must be customer id of google workspace
GOOGLE_APP_ACCOUNT_CUSTOMER_ID=
# Google app account service key
GOOGLE_APP_ACCOUNT_SERVICE_KEY=
```

#### Configure infrastructure environment variables

```bash
# By default, the data will be retrieved from the infrastructureSettings.json file.
# If you want to replace some infrastructure information, set SHOULD_REPLACE_INFRA_SETTING=true. (Default is false)
# Ensure that you also set values for them; these values will replace the data in the infrastructureSettings.json file every time the application starts.
SHOULD_REPLACE_INFRA_SETTING=false
INFRA_FRONTEND_STATIC_DOMAIN=
INFRA_FRONTEND_EXTERNAL_STATIC_CDN=
INFRA_FRONTEND_IMAGE_DOMAIN=
INFRA_GOOGLE_ANALYTIC_TRACKING_ID=
INFRA_GOOGLE_TAGMANAGER_CONTAINER_ID=
```

#### Configure deployment environment variables

```bash
# If set RUN_INITIAL_COMMAND is true, application will run command curama:init when start.
# The goal of this command is to sync data from google and create verified permission policy template.
# (RUN_INITIAL_COMMAND = true is required for first time deploy) Accepted values: true
RUN_INITIAL_COMMAND=
```
### Create cognito user pool
Please check the guide to create cognito user pool. [Cognito User Pool Guide](https://gitlab.wkwk.io/vietnam/curama-consulting-message/-/wikis/Guide-to-creating-Google-Workspace-SAML-integrate-AWS-Cognito-%7C-Create-cognito-user-pool).

### Create verified permission policy store

Please check the guide to create verified permission policy store. [Verified Permission Guide](https://gitlab.wkwk.io/vietnam/curama-consulting-message/-/wikis/Guide-to-creating-Google-Workspace-SAML-integrate-AWS-Cognito-%7C-Create-amazon-verified-permission).

Follow that, nagivate to schema page and create verified permission policy store with this schema. Please refer to file `consulting-message.aws-verified-permission.json` in the root directory of this project.

Would you like create a static policy for specific email such as you want to create a policy for `Policy` resource.

Click on `Create policy` button and click on `Create static policy` button.
In the page `Create policy`, click `Next` button to navigate to Step 2 (`Details`).

In the policy input, you can add policy like this:
```
permit(
  principal,
  action in [Curama::Action::"editPolicy"],
  resource == Curama::Policy::"*"
) when {
  principal.email == "<your-email>"
};
```

In the description input, you can add description for this policy. Example `[Policy][Terry][Permit]-[Principal:<EMAIL>] invoke [Action:editPolicy] on [Resource:Policy]`

### Setting Google App Account Service

- Go to google app account service and create a new service account.
- Download the service account key and save it to your local machine or store it in AWS Parameter Store.
- Go to google workspace and access to `Domain-wide Delegation` to link to service account that you created above.
- Ensure that when create new app client id in `Domain-wide Delegation` you must set scopes below:
  ```
  https://www.googleapis.com/auth/admin.reports.audit.readonly
  https://www.googleapis.com/auth/admin.directory.user.readonly
  https://www.googleapis.com/auth/admin.directory.group.readonly
  https://www.googleapis.com/auth/admin.directory.orgunit.readonly
  ```

You can refer to this guide to create google app account service. [Google App Account Service Guide](https://gitlab.wkwk.io/vietnam/curama-consulting-message/-/wikis/Setting-Up-Google-Cloud-Service-for-Project).

