import { Command, CommandRunner } from 'nest-commander';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { AWSVerifiedPermissionProvider } from '@curama-auth/authorization';

@Command({
  name: 'curama:policy-template-init',
})
export class PolicyTemplateInit extends CommandRunner {
  private readonly logger = new Logger(PolicyTemplateInit.name);
  constructor(private readonly configService: ConfigService) {
    super();
  }

  async run(inputs: string[], options: Record<string, any>): Promise<void> {
    this.logger.log('The policy template init command is running...');
    const verifiedPermissionProvider = new AWSVerifiedPermissionProvider(this.configService);
    const isInitPolicyTemplates = await verifiedPermissionProvider.checkWasInitPolicyTemplates();
    if (!isInitPolicyTemplates) {
      this.logger.log('The policy template init command is initializing...');
      await verifiedPermissionProvider.initPolicyTemplates();
    }

    this.logger.log('The policy template init command is done!');
  }
}
