import { Command, CommandRunner } from 'nest-commander';
import { GoogleService } from '../services/google.service';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { TeamEntity } from '../entities/team.entity';
import { AdminEntity } from '../entities/admin.entity';
import { Logger } from '@nestjs/common';

@Command({
  name: 'curama:sync-admin-team',
})
export class SyncAdminTeam extends CommandRunner {
  private readonly logger = new Logger(SyncAdminTeam.name);
  constructor(private readonly dataSource: DataSource, private readonly configService: ConfigService) {
    super();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async run(inputs: string[], options: Record<string, any>): Promise<void> {
    this.logger.log('sync admin and team command is running...');
    const googleService = new GoogleService(this.configService);
    const googleUsers = await googleService.getUsers();
    const insertAdminsTeams = [];
    const teamRepository = this.dataSource.getRepository(TeamEntity);
    const adminRepository = this.dataSource.getRepository(AdminEntity);

    for (const user of googleUsers) {
      const admin = await adminRepository.findOne({
        where: {
          email: user.primaryEmail,
        },
        relations: ['teams'],
      });

      if (!admin) {
        continue;
      }

      const team = await teamRepository.findOneBy({ orgUnitPath: user.orgUnitPath });
      if (!team) {
        continue;
      }

      const existedRelation = admin.teams.find((item) => item.id === team.id);
      if (!existedRelation) {
        insertAdminsTeams.push({
          adminName: admin.name,
          adminId: admin.id,
          teamId: team.id,
          teamName: team.name,
        });
      }
    }

    if (insertAdminsTeams.length > 0) {
      this.logger.log(`insert relation of ${insertAdminsTeams.length} admin and team`);
      insertAdminsTeams.forEach((item) => this.logger.log(`add admin ${item.adminName} to team ${item.teamName}`));
      const queryValueBuilder = insertAdminsTeams
        .map((item) => `('${item.adminId}', '${item.teamId}', CURRENT_TIMESTAMP)`)
        .join(',');
      const schema = this.dataSource.driver.schema ?? 'public';
      const query = `INSERT INTO "${schema}"."Admins_Teams" (admin_id, team_id, created_at) VALUES ${queryValueBuilder}`;
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.query(query);
      this.logger.log('link admin and team success');
    } else {
      this.logger.log('No admin and team to sync');
    }
  }
}
