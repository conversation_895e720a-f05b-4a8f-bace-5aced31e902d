import { Command, CommandRunner } from 'nest-commander';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { SyncAdmin } from './sync-admin.command';
import { SyncTeam } from './sync-team.command';
import { SyncAdminTeam } from './sync-admin-team.command';
import { PolicyTemplateInit } from './policy-template-init.command';
import { RegisterGoogleChannelCommand } from './register-google-channel.command';
import { GoogleChannelService } from '@curama-auth/services/google-channel.service';

@Command({
  name: 'curama:init',
})
export class CuramaConsultingInitCommand extends CommandRunner {
  private readonly logger = new Logger(CuramaConsultingInitCommand.name);
  constructor(
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,
    private googleChannelService: GoogleChannelService,
  ) {
    super();
  }

  async run(inputs: string[], options: Record<string, any>): Promise<void> {
    this.logger.log('The curama consulting init command is running...');
    await this.dataSource.transaction(async (manager) => {
      const syncAdminCommander = new SyncAdmin(this.dataSource, this.configService);
      await syncAdminCommander.run([], {});
      const syncTeamCommander = new SyncTeam(this.dataSource, this.configService);
      await syncTeamCommander.run([], {});
      const syncAdminTeamCommander = new SyncAdminTeam(this.dataSource, this.configService);
      await syncAdminTeamCommander.run([], {});
    });

    const initPolicyTemplateCommander = new PolicyTemplateInit(this.configService);
    try {
      await initPolicyTemplateCommander.run([], {});
    } catch (error) {
      this.logger.error(error);
    }

    try {
      const registerGoogleChannelCommander = new RegisterGoogleChannelCommand(this.googleChannelService);
      await registerGoogleChannelCommander.run([], {});
    } catch (error) {
      this.logger.error(error);
    }

    this.logger.log('The curama consulting init command is done.');
  }
}
