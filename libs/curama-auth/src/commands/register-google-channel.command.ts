import { Command, CommandRunner } from 'nest-commander';
import { GoogleChannelService } from '../services/google-channel.service';

@Command({
  name: 'curama:register-google-channel',
})
export class RegisterGoogleChannelCommand extends CommandRunner {
  constructor(private googleChannelService: GoogleChannelService) {
    super();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async run(inputs: string[], options: Record<string, any>): Promise<void> {
    await this.googleChannelService.registerNeccessaryGoogleChannel();
  }
}
