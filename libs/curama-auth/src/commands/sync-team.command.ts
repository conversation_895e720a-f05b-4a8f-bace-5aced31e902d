import { Command, CommandRunner } from 'nest-commander';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { Logger } from '@nestjs/common';
import { GoogleService } from '@curama-auth/services/google.service';
import { TeamEntity } from '@curama-auth/entities/team.entity';

@Command({
  name: 'curama:sync-team',
})
export class SyncTeam extends CommandRunner {
  private readonly logger = new Logger(SyncTeam.name);
  constructor(private readonly dataSource: DataSource, private readonly configService: ConfigService) {
    super();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async run(inputs: string[], options: Record<string, any>): Promise<void> {
    this.logger.log('Sync team command is running...');
    const teamRepository = this.dataSource.getRepository(TeamEntity);
    const googleService = new GoogleService(this.configService);
    this.logger.log('get organization units from google');
    const organizationUnits = await googleService.getOrgUnits();
    if (!organizationUnits) {
      this.logger.log('No organization units found.');
      return;
    }
    const upsertDepartMent = async (unit) => {
      const department = await teamRepository.findOneBy({ orgUnitPath: unit.orgUnitPath });
      if (department) {
        if (department.isLast === true) await teamRepository.update(department.id, { isLast: false });
        return;
      }
      const parent = await teamRepository.findOneBy({ orgUnitPath: unit.parentOrgUnitPath });
      const newDepartment = teamRepository.create({
        name: unit.name,
        orgUnitPath: unit.orgUnitPath,
        departmentId: parent?.id,
        isLast: false,
      });
      await teamRepository.insert(newDepartment);
    };
    const upsertTeam = async (unit) => {
      const team = await teamRepository.findOneBy({ orgUnitPath: unit.orgUnitPath });
      if (team) return;
      const parent = await teamRepository.findOneBy({ orgUnitPath: unit.parentOrgUnitPath });
      const newDepartment = teamRepository.create({
        name: unit.name,
        orgUnitPath: unit.orgUnitPath,
        departmentId: parent?.id,
        isLast: true,
      });
      await teamRepository.insert(newDepartment);
    };
    const getAllUnit = async (rootUnits) => {
      for (const organization of rootUnits) {
        const unit = await googleService.getOrgUnits({
          orgUnitPath: organization.orgUnitPath as string,
        });
        if (unit) {
          await upsertDepartMent(organization);
          await getAllUnit(unit);
        } else {
          await upsertTeam(organization);
        }
      }
    };
    this.logger.log('update teams');
    await getAllUnit(organizationUnits);
  }
}
