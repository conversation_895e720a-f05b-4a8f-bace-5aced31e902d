import { Command, CommandRunner } from 'nest-commander';
import { ConfigService } from '@nestjs/config';
import { DataSource, In, Repository } from 'typeorm';
import { Logger } from '@nestjs/common';
import { GoogleService } from '@curama-auth/services/google.service';
import { AdminStatus } from '@curama-auth/constants';
import { AdminEntity } from '@curama-auth/entities/admin.entity';

@Command({
  name: 'curama:sync-admin',
})
export class SyncAdmin extends CommandRunner {
  private googleService: GoogleService;
  private adminRepository: Repository<AdminEntity>;
  private readonly logger = new Logger(SyncAdmin.name);
  constructor(private readonly dataSource: DataSource, private readonly configService: ConfigService) {
    super();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async run(inputs: string[], options: Record<string, any>): Promise<void> {
    this.logger.log('sync admins command is running...');
    this.adminRepository = this.dataSource.getRepository(AdminEntity);
    this.googleService = new GoogleService(this.configService);
    this.logger.log('get users from google');
    const users = await this.googleService.getUsers();
    this.logger.log('create admin entities');
    const adminEntites: Array<AdminEntity> = [];
    for (const user of users) {
      adminEntites.push(this.createAdminEntity(user));
    }

    this.logger.log('get existing admins');
    const existingAdmins = await this.getExistAdmins(adminEntites);
    this.logger.log('update existing admins');
    await this.updateAdmins(existingAdmins, adminEntites);
    this.logger.log('insert new admins');
    await this.insertAdmins(existingAdmins, adminEntites);
    this.logger.log('sync admins success');
  }

  private async getExistAdmins(adminEntites: AdminEntity[]): Promise<AdminEntity[]> {
    const existingAdmins = await this.adminRepository.findBy({
      email: In(adminEntites.map((admin) => admin.email)),
    });

    existingAdmins.map((admin) => this.logger.log(`- exists admin: ${admin.email}`));
    return existingAdmins;
  }

  private createAdminEntity(user: any): AdminEntity {
    const adminEntity = new AdminEntity();
    adminEntity.name = user.name?.fullName;
    adminEntity.status = user.suspended ? AdminStatus.Inactive : AdminStatus.Active;
    adminEntity.email = user.primaryEmail;
    adminEntity.createdAt = new Date();
    adminEntity.updatedAt = new Date();
    return adminEntity;
  }

  private async updateAdmins(existingAdmins: AdminEntity[], adminEntites: AdminEntity[]): Promise<void> {
    const promiseUpdatingAdmins = [];
    existingAdmins.forEach(async (existingAdmin) => {
      const adminEntity = adminEntites.find((admin) => admin.email === existingAdmin.email);
      if (adminEntity) {
        const updateData = {
          name: adminEntity.name,
          status: adminEntity.status,
          updatedAt: new Date(),
        };

        this.logger.log(`- update admin: ${existingAdmin.email}`);
        promiseUpdatingAdmins.push(this.adminRepository.update(existingAdmin.id, updateData).catch(() => null));
      }
    });

    promiseUpdatingAdmins.length && (await Promise.all(promiseUpdatingAdmins));
  }

  private async insertAdmins(existingAdmins: AdminEntity[], adminEntites: AdminEntity[]): Promise<void> {
    const newAdmins = adminEntites.filter(
      (admin) => !existingAdmins.find((existingAdmin) => existingAdmin.email === admin.email),
    );

    newAdmins.map((admin) => this.logger.log(`- new admin: ${admin.email}`));
    await this.adminRepository.save(newAdmins).catch(() => {
      this.logger.error('- insert new admins failed');
    });
  }
}
