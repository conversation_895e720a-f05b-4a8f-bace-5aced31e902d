import { <PERSON>, Controller, Logger, Post } from '@nestjs/common';
import routes from '../common/routes';
import { AdminService } from '@curama-auth/services/admin.service';

@Controller()
export class AdminWebhookController {
  private readonly logger = new Logger(AdminWebhookController.name);
  constructor(private readonly adminService: AdminService) {}
  @Post(routes.webhook.admin.create)
  async createAdmin(@Body('primaryEmail') email: string): Promise<void> {
    this.logger.log(`AdminWebhookController.createAdmin: ${email}`);
    if (!email) {
      return Promise.resolve();
    }

    await this.adminService.createAdmin(email);
    return Promise.resolve();
  }

  @Post(routes.webhook.admin.update)
  async updateAdmin(@Body('primaryEmail') email: string): Promise<void> {
    this.logger.log(`AdminWebhookController.updateAdmin: ${email}`);
    if (!email) {
      return Promise.resolve();
    }

    await this.adminService.updateAdmin(email);
    return Promise.resolve();
  }

  @Post(routes.webhook.admin.delete)
  async deleteAdmin(@Body('primaryEmail') email: string): Promise<void> {
    this.logger.log(`AdminWebhookController.deleteAdmin: ${email}`);
    if (!email) {
      return Promise.resolve();
    }

    await this.adminService.deleteAdmin(email);
    return Promise.resolve();
  }
}
