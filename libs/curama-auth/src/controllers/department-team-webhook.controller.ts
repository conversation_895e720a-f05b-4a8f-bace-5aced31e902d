import routes from '@curama-auth/common/routes';
import {
  GoogleAuditEventName,
  GoogleOrganizationUnitEventType,
  SLEEP_BEFORE_GOOGLE_API_CALL,
} from '@curama-auth/constants';
import { OrgUnitEvents } from '@curama-auth/decorators/orgunit-events.decorator';
import { sleep } from '@curama-auth/helper';
import { GoogleService } from '@curama-auth/services/google.service';
import { TeamService } from '@curama-auth/services/team.service';
import { Controller, InternalServerErrorException, Logger, Post } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Controller()
export class DepartmentTeamWebhookController {
  private readonly logger = new Logger(DepartmentTeamWebhookController.name);
  constructor(private readonly teamService: TeamService, private readonly configService: ConfigService) {}
  @Post(routes.webhook.orgUnit.create)
  async createTeamOrDepartment(
    @OrgUnitEvents({
      eventType: GoogleAuditEventName.OrganizationUnit,
      eventName: GoogleOrganizationUnitEventType.Create,
    })
    payload: any,
  ): Promise<void> {
    if (!payload) {
      this.logger.log('[createDepartmentTeam] No payload found.');
      return Promise.resolve();
    }

    this.logger.log(`[createDepartmentTeam]: payload ${JSON.stringify(payload)}`);
    const newOrgUnitPath = payload.find((parameter: any) => parameter.name == 'ORG_UNIT_NAME')?.value ?? null;
    if (!newOrgUnitPath) {
      this.logger.log('[createDepartmentTeam] new organization unit path is invalid.');
      return Promise.resolve();
    }
    this.logger.log(`[createTeam] Create new team: ${newOrgUnitPath}`);
    await this.teamService.createTeam(newOrgUnitPath);

    return Promise.resolve();
  }

  @Post(routes.webhook.orgUnit.update)
  async updateTeamOrDepartment(
    @OrgUnitEvents({
      eventType: GoogleAuditEventName.OrganizationUnit,
      eventName: GoogleOrganizationUnitEventType.ChangeName,
    })
    payload: any,
  ): Promise<void> {
    if (!payload) {
      this.logger.log('[updateDepartmentTeam] No payload found.');
      return Promise.resolve();
    }

    this.logger.log(`[updateDepartmentTeam]: payload ${JSON.stringify(payload)}`);
    const currentPath = payload.find((parameter: any) => parameter.name == 'ORG_UNIT_NAME')?.value ?? null;
    const newPath = payload.find((parameter: any) => parameter.name == 'NEW_VALUE')?.value ?? null;
    if (!currentPath || !newPath) {
      this.logger.log('[updateDepartmentTeam] Current path or new path is invalid.');
      return Promise.resolve();
    }

    this.logger.log(`[updateDepartmentTeam] Update: ${currentPath}`);
    await this.teamService.updateNameDepartmentAndTeam(currentPath, newPath);

    return Promise.resolve();
  }

  @Post(routes.webhook.orgUnit.delete)
  async deleteTeamOrDepartment(
    @OrgUnitEvents({
      eventType: GoogleAuditEventName.OrganizationUnit,
      eventName: GoogleOrganizationUnitEventType.Delete,
    })
    payload: any,
  ): Promise<void> {
    if (!payload) {
      this.logger.log('[deleteDepartmentTeam] No payload found.');
      return Promise.resolve();
    }

    this.logger.log(`[deleteDepartmentTeam]: payload ${JSON.stringify(payload)}`);
    const orgUnitPath = payload.find((parameter: any) => parameter.name == 'ORG_UNIT_NAME')?.value ?? null;
    if (!orgUnitPath) {
      this.logger.log(`[deleteDepartmentTeam] org unit path is invalid ${orgUnitPath}`);
      return Promise.resolve();
    }

    // check if department or team was deleted
    const googleService = new GoogleService(this.configService);
    const minusLeadSlashOrgUnitPath = orgUnitPath.replace(/^\//, '');
    await sleep(SLEEP_BEFORE_GOOGLE_API_CALL);
    const orgUnit = await googleService.getOrgUnit(minusLeadSlashOrgUnitPath);
    if (orgUnit) {
      throw new InternalServerErrorException(
        `[deleteDepartmentTeam] org unit is not deleted yet. Please check data in google workspace: ${orgUnitPath}`,
      );
    }

    this.logger.log(`[deleteTeam] Delete team: ${orgUnitPath}`);
    await this.teamService.deleteTeam(orgUnitPath);

    return Promise.resolve();
  }

  @Post(routes.webhook.orgUnit.moveTeam)
  async moveTeam(
    @OrgUnitEvents({
      eventType: GoogleAuditEventName.OrganizationUnit,
      eventName: GoogleOrganizationUnitEventType.Move,
    })
    payload: any,
  ): Promise<void> {
    if (!payload) {
      this.logger.log('[moveTeam] No payload found.');
      return Promise.resolve();
    }

    this.logger.log(`[moveTeam]: payload ${JSON.stringify(payload)}`);

    const currentPath = payload.find((parameter: any) => parameter.name == 'ORG_UNIT_NAME')?.value ?? null;
    const newPath = payload.find((parameter: any) => parameter.name == 'NEW_VALUE')?.value ?? null;
    this.logger.log(`[deleteTeam] currentPath: ${currentPath}, newPath: ${newPath}`);
    if (!currentPath || !newPath) {
      this.logger.log('[moveTeam] current path or new path is invalid.');
      return Promise.resolve();
    }
    await this.teamService.moveTeam(currentPath, newPath);
    return Promise.resolve();
  }
}
