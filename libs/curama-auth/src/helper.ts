import * as jwt from 'jsonwebtoken';

export const getDepartmentFromOrgUnitPath = (orgUnitPath: string): string => {
  const splitted = orgUnitPath.split('/');
  return splitted && splitted.length > 1 ? splitted[1] : '';
};

export const getTeamFromGoogleOrgUnitPath = (orgUnitPath: string): string => {
  const splitted = orgUnitPath.split('/');
  return splitted && splitted.length > 2 ? splitted[2] : '';
};
export const getNameFromGoogleOrgUnitPath = (orgUnitPath: string): string => {
  const splitted = orgUnitPath.split('/');
  return splitted && splitted.length ? splitted[splitted.length - 1] : '';
};
export const isDepartment = (orgUnitPath: string): boolean => {
  const pattern = new RegExp('^/[^/]*$');
  return pattern.test(orgUnitPath);
};

export const generateJWTToken = (payload: any, secret: string, audience: string, expiresIn: string): string => {
  return jwt.sign(payload, secret, { expiresIn: expiresIn, algorithm: 'HS256', audience: audience });
};

export const verifyJWT = (token: string, secret: string, audience: string): any => {
  try {
    return jwt.verify(token, secret, { algorithms: ['HS256'], audience: audience, maxAge: '2d' });
  } catch {
    return null;
  }
};

export const sleep = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};
