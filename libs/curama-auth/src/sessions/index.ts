import * as express from 'express';
import { SessionAdmin } from './session-admin';
import { Redis } from 'ioredis';
import { v4 as uuidv4 } from 'uuid';
import { Inject } from '@nestjs/common';
import { AUTH_STORE } from '@vietnam/cnga-middleware';

export const TERRY_SESSION_COOKIE_KEY = 'connect.tsid';
export const TERRY_SESSION_PREFIX_KEY = 'terrysess';

export interface TerrySessionData {
  admin?: SessionAdmin;
  adminId?: string;
  intendedUrl?: string;
  cookie: Partial<express.CookieOptions>;
}

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      terrySession: TerrySessionData;
      terrySessionID: string;
      terryLogIn: (admin: SessionAdmin) => void;
      terryLogout: () => void;
    }
  }
}

export class TerrySessionManager {
  private redisClient: Redis;
  private cookie: express.CookieOptions;
  private ttl: number;
  constructor(@Inject(AUTH_STORE) private readonly redis: Redis) {
    this.redisClient = redis;
    this.ttl = process.env.SESSION_TTL ? parseInt(process.env.SESSION_TTL) : 24 * 60 * 60;
    this.cookie = {
      // maxAge: 1 day in milliseconds
      maxAge: this.ttl * 1000,
      expires: new Date(Date.now() + this.ttl * 1000),
      httpOnly: true,
    };
  }

  private async genid() {
    const sessionId = uuidv4();
    return sessionId;
  }

  private getSessionKey(id: string) {
    return TERRY_SESSION_PREFIX_KEY + ':' + id;
  }

  getCookie(): Partial<express.CookieOptions> {
    return this.cookie;
  }

  async init() {
    const sessionId = await this.genid();
    await this.redisClient.set(this.getSessionKey(sessionId), JSON.stringify({}), 'EX', this.ttl);
    return sessionId;
  }

  async save(sessionId: string, data: TerrySessionData, ttl?: number) {
    data.cookie = { ...this.cookie, ...data?.cookie };
    await this.redisClient.set(this.getSessionKey(sessionId), JSON.stringify(data), 'EX', ttl ?? this.ttl);
  }

  async resave(sessionId: string, data: any) {
    data.cookie = { ...this.cookie, ...data?.cookie };
    await this.redisClient.set(this.getSessionKey(sessionId), JSON.stringify(data), 'KEEPTTL');
  }

  async touch(sessionId: string) {
    await this.redisClient.touch(this.getSessionKey(sessionId));
  }

  async load(sessionId: string) {
    const sessionData = await this.redisClient.get(this.getSessionKey(sessionId));
    return sessionData ? JSON.parse(sessionData) : null;
  }

  async destroy(sessionId: string) {
    await this.redisClient.del(this.getSessionKey(sessionId));
  }

  async getTimeToLive(sessionId: string) {
    return await this.redisClient.ttl(this.getSessionKey(sessionId));
  }
}
