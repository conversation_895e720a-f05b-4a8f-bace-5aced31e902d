import { CuramaAction, CuramaResource } from '@curama-auth/authorization/aws/schema';
import { AdminEntity } from '../entities/admin.entity';
import { ConfigService } from '@nestjs/config';

export interface SessionAdmin extends Pick<AdminEntity, 'id' | 'email' | 'status' | 'name' | 'teams'> {
  accessToken: string;
  refreshToken: string;
  idToken: string;
  given_name: string;
  family_name: string;
  expiresIn: number;
  policies?: { [key: string]: string[] };
  can(config: ConfigService, resource: CuramaResource, actions: CuramaAction[]): Promise<boolean>;
}
