import { BaseEntity, Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('GoogleChannels')
export class GoogleChannelEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'application_name' })
  applicationName: string;

  @Column({ name: 'event_name' })
  eventName: string;

  @Column({ name: 'channel_id' })
  channelId: string;

  @Column({ name: 'resource_id' })
  resourceId: string;

  @Column({ name: 'resource_uri' })
  resourceUri: string;

  @Column({ name: 'type', default: 'web_hook' })
  type: string;

  @Column({ name: 'address' })
  address: string;

  @Column({ name: 'kind' })
  kind: string;

  @Column({ name: 'scope' })
  scope: string;

  @Column({ name: 'expiration', nullable: true })
  expiration?: Date;

  @Column({ name: 'created_at', nullable: true, default: new Date() })
  createdAt?: Date;
}
