import {
  AttributeType,
  CognitoIdentityProvider,
  GetU<PERSON><PERSON>ommand,
  RevokeTokenCommand,
  UpdateUserAttributesCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { BadRequestException, HttpStatus, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as queryString from 'querystring';
import { CognitoAuthenticationConfig } from '../configs/aws';
import { Request } from 'express';
import { SessionAdmin } from '../sessions/session-admin';
import { ApiClientService } from '@vietnam/cnga-http-request';
import { IdentityServiceProvider } from './identity.provider';
import { AdminEntity } from '../entities/admin.entity';
import { Repository } from 'typeorm';
import { AWSVerifiedPermissionProvider } from '@curama-auth/authorization';
import { CURAMA_AUTHORIZATION_SERVICE } from '@curama-auth/constants';
import { CuramaPrincipal } from '@curama-auth/authorization/aws/schema';
import { debug } from '@src/common/utils/debugger';

export class CognitoServiceProvider implements IdentityServiceProvider {
  constructor(
    private readonly configService: ConfigService,
    private readonly apiClient: ApiClientService,
    private readonly adminRepository: Repository<AdminEntity>,
    @Inject(CURAMA_AUTHORIZATION_SERVICE) private readonly authorizationService: AWSVerifiedPermissionProvider,
  ) {}

  /**
   * get oauth2 url to login using idp provider
   * @returns string
   */
  getOauth2Url(): string {
    const cognitoConfig = this.configService.get<CognitoAuthenticationConfig>('curama-auth.aws.cognito');
    if (!cognitoConfig) {
      return '';
    }

    const payload = { ...cognitoConfig.payload };
    return cognitoConfig.oauth2Url + '?' + queryString.stringify(payload);
  }

  /**
   * updateUserAttributes
   * @param cognitoClient CognitoIdentityProvider
   * @param accessToken string
   * @param data AttributeType[]
   * @returns Promise<any>
   */
  async updateUserAttributes(
    cognitoClient: CognitoIdentityProvider,
    accessToken: string,
    data: AttributeType[],
  ): Promise<any> {
    const updateUserAttributeCommand = new UpdateUserAttributesCommand({
      AccessToken: accessToken,
      UserAttributes: data,
    });

    return await cognitoClient.send(updateUserAttributeCommand).catch(() => null);
  }

  /**
   * exchange token from code for access token
   * @param code string
   * @returns Promise<any>
   */
  private async exchangeToken(code: string): Promise<any> {
    const cognitoConfig = this.configService.get<CognitoAuthenticationConfig>('curama-auth.aws.cognito');
    const payload = {
      grant_type: 'authorization_code',
      client_id: cognitoConfig.payload.client_id,
      code,
      redirect_uri: cognitoConfig.payload.redirect_uri,
    };

    const response = await this.apiClient.request({
      url: cognitoConfig.tokenExchangeURL,
      baseURL: cognitoConfig.domain,
      method: 'POST',
      data: payload,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization:
          'Basic ' + Buffer.from(cognitoConfig.clientId + ':' + cognitoConfig.clientSecret).toString('base64'),
      },
    });

    if (response.status !== HttpStatus.OK) {
      throw new BadRequestException('Failed to exchange token');
    }

    return {
      accessToken: response.data.access_token,
      refreshToken: response.data.refresh_token,
      idToken: response.data.id_token,
      expiresIn: response.data.expires_in,
    };
  }

  /**
   * get logged in user information
   * @param cognitoClient CognitoIdentityProvider
   * @param accessToken string
   * @returns Promise<any>
   */
  async getLoggedInUser(cognitoClient: CognitoIdentityProvider, accessToken: string): Promise<any> {
    const getUserInfoCommand = new GetUserCommand({
      AccessToken: accessToken,
    });

    return await cognitoClient.send(getUserInfoCommand).catch(() => null);
  }

  /**
   * filter logged in user data from cognito
   * @param data any
   * @param accessToken string
   * @param refreshToken string
   * @param idToken string
   * @returns any
   */
  private async exchangeForSessionAdmin(
    data: any,
    accessToken: string,
    refreshToken: string,
    idToken: string,
    expiresIn: number,
  ): Promise<SessionAdmin> {
    const authConfig = this.configService.get('curama-auth.app.auth');
    const user = {} as SessionAdmin;

    data.UserAttributes.forEach((attr: any) => {
      if (authConfig.validAttributes.includes(attr.Name)) {
        const userKey = attr.Name.replace('custom:', '');
        user[userKey] = attr.Value;
      }
    });

    if (!user) {
      throw new BadRequestException('[AuthProvider] Failed to map user data');
    }

    let adminEntityInfo = await this.adminRepository.findOne({
      where: {
        email: user.email,
      },
      relations: {
        teams: true,
      },
    });

    if (!adminEntityInfo) {
      adminEntityInfo = await this.createAdminUser(user);
    }

    const userPolicies = await this.authorizationService.getUserPoliciesResourcesWithActions({
      ...adminEntityInfo,
    } as unknown as CuramaPrincipal);

    const sessionAdmin = {
      ...user,
      ...adminEntityInfo,
      accessToken,
      refreshToken,
      idToken,
      expiresIn,
      policies: userPolicies,
    } as SessionAdmin;

    return sessionAdmin;
  }

  /**
   * @returns CognitoIdentityProvider
   */
  private getCognitoIdentityProvider(): CognitoIdentityProvider {
    const awsConfig = this.configService.get('curama-auth.aws.cognito');
    return new CognitoIdentityProvider({
      region: awsConfig.region,
    });
  }

  /**
   * authenticate user from cognito callback
   * @param req Request
   * @returns void
   */
  async authenticate(req: Request): Promise<SessionAdmin> {
    const code: string = req.query.code as string;

    const { accessToken, refreshToken, idToken, expiresIn } = await this.exchangeToken(code);
    const cognitoIdentityServiceProvider = this.getCognitoIdentityProvider();
    const loggedInUser = await this.getLoggedInUser(cognitoIdentityServiceProvider, accessToken);
    const sessionAdmin = await this.exchangeForSessionAdmin(
      loggedInUser,
      accessToken,
      refreshToken,
      idToken,
      expiresIn,
    );
    req.terryLogIn(sessionAdmin);
    debug('[cognitoProvider] sessionAdmin', {
      id: sessionAdmin?.id,
      email: sessionAdmin?.email,
      name: sessionAdmin?.name,
      policies: sessionAdmin?.policies,
    });
    return req.terrySession.admin;
  }

  /**
   * get session admin info
   * @param req Request
   * @returns Promise<SessionAdmin>
   */
  async getSessionAdmin(req: Request): Promise<SessionAdmin> | undefined {
    if (!req.terrySession?.adminId) {
      return undefined;
    }

    const cognitoIdentityServiceProvider = this.getCognitoIdentityProvider();

    const admin = req.terrySession.admin;
    const adminInfo = await this.getLoggedInUser(cognitoIdentityServiceProvider, admin.accessToken);
    if (!adminInfo) {
      return undefined;
    }

    const sessionAdmin = this.exchangeForSessionAdmin(
      adminInfo,
      admin.accessToken,
      admin.refreshToken,
      admin.idToken,
      admin.expiresIn,
    );
    return sessionAdmin;
  }

  /**
   * Destroy session and revoke cognito token
   * @param req Request
   * @returns void
   */
  async signOut(req: Request): Promise<void> {
    if (!req.terrySession?.admin) {
      return;
    }

    const loggedInAdmin = { ...req.terrySession.admin } as SessionAdmin;

    req.terryLogout();

    if (!loggedInAdmin) {
      return;
    }

    const cognitoClient = this.getCognitoIdentityProvider();
    const awsConfig = this.configService.get<CognitoAuthenticationConfig>('curama-auth.aws.cognito');
    const revokeTokenCommand = new RevokeTokenCommand({
      ClientId: awsConfig.clientId,
      ClientSecret: awsConfig.clientSecret,
      Token: loggedInAdmin.refreshToken,
    });

    await cognitoClient.send(revokeTokenCommand).catch(() => null);
  }

  /**
   * create admin user
   * @param user SessionAdmin
   * @returns Promise<AdminEntity>
   */
  async createAdminUser(user: SessionAdmin): Promise<AdminEntity> {
    const adminEntityInfo = new AdminEntity();
    adminEntityInfo.email = user.email;
    adminEntityInfo.name = user.given_name + ' ' + user.family_name;
    this.adminRepository.save(adminEntityInfo);
    return adminEntityInfo;
  }
}
