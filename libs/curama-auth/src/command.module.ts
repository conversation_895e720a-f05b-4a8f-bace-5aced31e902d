import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import configs from './configs';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClientModule } from '@vietnam/cnga-http-request';
import {
  CuramaConsultingInitCommand,
  PolicyTemplateInit,
  RegisterGoogleChannelCommand,
  SyncAdmin,
  SyncAdminTeam,
  SyncTeam,
} from './commands';
import { GoogleChannelService } from './services/google-channel.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [...configs],
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_HOST || 'localhost',
      port: parseInt(process.env.DATABASE_PORT || '5432'),
      schema: process.env.DATABASE_SCHEMA || 'example',
      username: process.env.DATABASE_USERNAME || 'username',
      password: process.env.DATABASE_PASSWORD || '',
      database: process.env.DATABASE_NAME || 'postgres',
      synchronize: false,
      autoLoadEntities: true,
      entities: ['dist/**/*.entity.{ts,js}'],
      migrationsRun: true,
      migrations: ['dist/**/migrations/*.{ts,js}'],
      logging: process.env.APP_DEBUG === 'true',
      ...(process.env.DATABASE_POOL_SIZE && { poolSize: parseInt(process.env.DATABASE_POOL_SIZE) }),
    }),
    ApiClientModule.forRoot({
      baseURL: null,
    }),
  ],
  providers: [
    ConfigService,
    GoogleChannelService,
    SyncAdmin,
    SyncTeam,
    SyncAdminTeam,
    PolicyTemplateInit,
    RegisterGoogleChannelCommand,
    CuramaConsultingInitCommand,
  ],
  exports: [],
})
export class CommandModule {}
