// session.middleware.ts
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction, CookieOptions } from 'express';
import { TERRY_SESSION_COOKIE_KEY, TerrySessionManager } from '../sessions/index';
import { SessionAdmin } from '../sessions/session-admin';
import * as dayjs from 'dayjs';

@Injectable()
export class TerrySessionMiddleware implements NestMiddleware {
  constructor(private sessionManager: TerrySessionManager) {}

  async use(req: Request, res: Response, next: NextFunction) {
    let sessionId = req.cookies[TERRY_SESSION_COOKIE_KEY];
    const isSecureProtocol = req.headers['x-forwarded-proto'] === 'https';
    const cookieOptions = { ...(this.sessionManager.getCookie() as CookieOptions), secure: isSecureProtocol };
    if (!sessionId) {
      sessionId = await this.sessionManager.init();
      res.cookie(TERRY_SESSION_COOKIE_KEY, sessionId, cookieOptions);
    }

    let sessionData = await this.sessionManager.load(sessionId);
    if (!sessionData) {
      await this.sessionManager.destroy(sessionId);
      sessionId = await this.sessionManager.init();
      res.cookie(TERRY_SESSION_COOKIE_KEY, sessionId, cookieOptions);
      sessionData = {};
    }

    req.terrySession = sessionData;
    req.terrySessionID = sessionId;

    req.terryLogIn = async (admin: SessionAdmin) => {
      req.terrySession.admin = admin;
      req.terrySession.adminId = admin.id;
      const cookieOptions = { ...(this.sessionManager.getCookie() as CookieOptions), secure: isSecureProtocol };
      cookieOptions.expires = dayjs.utc().add(admin.expiresIn, 'seconds').toDate();
      cookieOptions.maxAge = admin.expiresIn * 1000;
      res.cookie(TERRY_SESSION_COOKIE_KEY, sessionId, cookieOptions);
      await this.sessionManager.save(sessionId, req.terrySession, admin.expiresIn);
    };

    req.terryLogout = async () => {
      res.clearCookie(TERRY_SESSION_COOKIE_KEY);
      await this.sessionManager.destroy(sessionId);
    };

    res.on('finish', async () => {
      const ttl = await this.sessionManager.getTimeToLive(sessionId);
      if (ttl > 0) {
        await this.sessionManager.resave(sessionId, req.terrySession);
      }
    });

    next();
  }
}
