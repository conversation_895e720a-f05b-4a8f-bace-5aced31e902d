import { GoogleConfigOptions } from '@curama-auth/configs/google';
import { GoogleChannelEntity } from '@curama-auth/entities/google-channel.entity';
import { verifyJWT } from '@curama-auth/helper';
import { HttpStatus, Injectable, NestMiddleware } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { DataSource } from 'typeorm';

@Injectable()
export class GoogleChannelVerifyMiddleware implements NestMiddleware {
  constructor(private readonly dataSource: DataSource, private readonly configService: ConfigService) {}

  async use(req: Request, res: any, next: () => void) {
    const channelId = req.header('X-Goog-Channel-Id');
    const token = req.header('X-Goog-Channel-Token');

    const googleConfig = this.configService.get<GoogleConfigOptions>('curama-auth.google');

    if (!channelId || !token) {
      return res.status(HttpStatus.NOT_ACCEPTABLE).send({
        status: 'error',
        message: 'Invalid request',
      });
    }

    const payload = verifyJWT(token, googleConfig.tokenSecret, googleConfig.jwtAudience);
    if (payload?.id !== channelId) {
      return res.status(HttpStatus.NOT_ACCEPTABLE).send({
        status: 'error',
        message: 'Invalid token',
      });
    }

    const googleChannelRepository = this.dataSource.getRepository(GoogleChannelEntity);
    const channel = await googleChannelRepository.findOneBy({ channelId: channelId });
    if (!channel) {
      return res.status(HttpStatus.NOT_ACCEPTABLE).send({
        status: 'error',
        message: 'Invalid channel id',
      });
    }

    return next();
  }
}
