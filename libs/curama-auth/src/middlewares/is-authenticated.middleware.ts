import { IdentityServiceProvider } from '@curama-auth/identities/identity.provider';
import { Inject, Injectable, NestMiddleware } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CURAMA_AUTH_SERVICE } from '../constants';

/**
 * redirect to speicific url when access login page while user session existing.
 */
@Injectable()
export class IsAuthenticatedMiddleware implements NestMiddleware {
  constructor(
    @Inject(CURAMA_AUTH_SERVICE) private readonly authService: IdentityServiceProvider,
    private readonly configService: ConfigService,
  ) {}
  async use(req: any, res: any, next: () => void) {
    if (req.terrySession?.admin) {
      const sessionAdmin = await this.authService.getSessionAdmin(req);
      if (sessionAdmin) {
        const intendedUrl = req?.cookies ? req.cookies['intendedUrl'] : req?.terrySession?.intendedUrl;
        if (intendedUrl) {
          return res.redirect(intendedUrl);
        } else {
          const homeUrl = this.configService.get<string>('curama-auth.app.auth.homeUrl') ?? '/';
          return res.redirect(homeUrl);
        }
      }
    }

    next();
  }
}
