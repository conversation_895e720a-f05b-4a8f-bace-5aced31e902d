import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const OrgUnitEvents = createParamDecorator(
  (data: { eventType: string; eventName: string }, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const body = request.body;
    const events = body.events && body.events.length > 0 ? body.events : null;
    if (!events) {
      return null;
    }

    const payload = events.find((event: any) => event.type == data.eventType && event.name == data.eventName);
    if (!payload) {
      return null;
    }

    return payload?.parameters ?? null;
  },
);
