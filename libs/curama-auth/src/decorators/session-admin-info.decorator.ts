import { AWSVerifiedPermissionProvider } from '@curama-auth/authorization';
import { CuramaAction, CuramaResource } from '@curama-auth/authorization/aws/schema';
import { SessionAdmin } from '@curama-auth/sessions/session-admin';
import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export const SessionAdminInfo = createParamDecorator((data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  const sessionAdmin = request.terrySession?.admin as SessionAdmin;
  if (sessionAdmin) {
    sessionAdmin.can = async function (config: ConfigService, resource: CuramaResource, actions: CuramaAction[]) {
      let shouldInvoke = false;
      const verifiedPermissionService = new AWSVerifiedPermissionProvider(config);
      for (const action of actions) {
        const isAllowed = await verifiedPermissionService.authorize(request, resource, action);
        if (isAllowed) {
          shouldInvoke = true;
          break;
        }
      }
      return shouldInvoke;
    };
  }
  return sessionAdmin;
});
