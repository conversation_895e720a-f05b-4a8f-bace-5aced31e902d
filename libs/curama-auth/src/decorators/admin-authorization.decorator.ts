import { SetMetadata, UseGuards, applyDecorators } from '@nestjs/common';
import { AdminAuthorizeGuard } from '../guards/admin-authorize.guard';
import { CuramaResource, CuramaAction } from '../authorization/aws/schema';
import { AuthorizationInput } from '@curama-auth/authorization/aws/types';

export const AdminAuthorizationMetaData = Symbol('AdminAuthorizationMetaData');
export const AdminAuthorization = (resource: CuramaResource, action: CuramaAction) => {
  return applyDecorators(
    SetMetadata(AdminAuthorizationMetaData, {
      resource,
      action,
    } as AuthorizationInput),
    UseGuards(AdminAuthorizeGuard),
  );
};
