import { CuramaActionOutputSchema } from './schema/action';
import {
  CreatePolicyCommand,
  CreatePolicyCommandOutput,
  CreatePolicyTemplateCommand,
  CreatePolicyTemplateCommandInput,
  CreatePolicyTemplateCommandOutput,
  Decision,
  DeletePolicyCommand,
  DeletePolicyCommandOutput,
  DeletePolicyTemplateCommand,
  DeletePolicyTemplateCommandOutput,
  EntityReference,
  GetPolicyCommand,
  GetPolicyCommandOutput,
  GetPolicyTemplateCommand,
  GetPolicyTemplateCommandOutput,
  GetSchemaCommand,
  GetSchemaCommandOutput,
  IsAuthorizedCommand,
  IsAuthorizedInput,
  ListPoliciesCommand,
  ListPolicyTemplatesCommand,
  PolicyDefinition,
  PolicyTemplateItem,
  VerifiedPermissionsClient,
  VerifiedPermissionsClientConfig,
} from '@aws-sdk/client-verifiedpermissions';
import { ConfigService } from '@nestjs/config';
import { AWSConfiguration } from '../../configs/aws';
import { PolicyStatic } from './types';
import { CuramaResource, CuramaAction, CuramaPrincipal } from './schema';
import { CuramaPolicyTemplates } from '../init-data';
import { Request } from 'express';
import { CuramaPolicyItem } from './policy-item';
import { CuramaPolicyTemplateItem } from './policy-template-item';
import { debug } from '@src/common/utils/debugger';
import { Logger } from '@nestjs/common';

export class AWSVerifiedPermissionProvider {
  private readonly logger = new Logger(AWSVerifiedPermissionProvider.name);
  private configuration: AWSConfiguration;
  private verifiedClient: VerifiedPermissionsClient;

  private ACTION_SCHEMA = 'Action';
  private PRINCIPAL_ENTITY_NAME = 'User';
  private MAX_RESULTS = 20;

  constructor(private readonly configService: ConfigService) {
    this.configuration = configService.get<AWSConfiguration>('curama-auth.aws');
    this.verifiedClient = this.getVerifiedPermissionClient();
  }

  /**
   * @returns VerifiedPermissionsClient
   */
  private getVerifiedPermissionClient(): VerifiedPermissionsClient {
    const configuration: VerifiedPermissionsClientConfig = {
      region: this.configuration.region,
    };

    if (this.configuration.accessKey && this.configuration.secretKey) {
      configuration.credentials = {
        accessKeyId: this.configuration.accessKey,
        secretAccessKey: this.configuration.secretKey,
      };
    }

    return new VerifiedPermissionsClient(configuration);
  }

  /**
   * get schema resource entity name
   * @param curamaResource CuramaResource
   * @returns string
   */
  private getSchemaResourceEntityName(curamaResource: CuramaResource): string {
    if (this.configuration.verifiedPermission.namespace) {
      return this.configuration.verifiedPermission.namespace + '::' + curamaResource;
    }

    return curamaResource;
  }

  /**
   * get schema principal entity name
   * @returns string
   */
  private getSchemaPrincipalEntityName(): string {
    if (this.configuration.verifiedPermission.namespace) {
      return this.configuration.verifiedPermission.namespace + '::' + this.PRINCIPAL_ENTITY_NAME;
    }

    return this.PRINCIPAL_ENTITY_NAME;
  }

  /**
   * get action schema
   * @returns string
   */
  private getActionSchemaName(): string {
    if (this.configuration.verifiedPermission.namespace) {
      return this.configuration.verifiedPermission.namespace + '::' + this.ACTION_SCHEMA;
    }

    return this.ACTION_SCHEMA;
  }

  /**
   * get action ID
   * @param curamaAction CuramaAction
   * @param curamaResource CuramaResource
   * @returns string
   */
  private getActionId(curamaAction: CuramaAction, curamaResource: CuramaResource): string {
    return curamaAction + curamaResource;
  }

  /**
   * get action value
   * @param curamaAction CuramaAction
   * @param curamaResource CuramaResource
   * @returns string
   */
  private getActionValue(curamaAction: CuramaAction, curamaResource: CuramaResource): string {
    return this.getActionSchemaName() + '::"' + this.getActionId(curamaAction, curamaResource) + '"';
  }

  /**
   * generate permit statement for template policy
   * @param curamaResource CuramaResource
   * @param curamaActions CuramaAction[]
   * @param when string
   * @returns string
   */
  private generatePermitStatementPolicyTemplate(
    curamaResource: CuramaResource,
    curamaActions: CuramaAction[],
    when?: string,
  ): string {
    const actions = curamaActions.map((action) => this.getActionValue(action, curamaResource)).join(',');
    const condition = when ? when : 'true';
    return `permit(principal == ?principal, action in [${actions}], resource == Curama::${curamaResource}::"*") when { ${condition} };`;
  }

  /**
   * create user policy linked with policy template
   * @param curamaPrincipal CuramaPrincipal
   * @param policyTemplate PolicyTemplateItem
   * @returns Promise<CreatePolicyCommandOutput>
   */
  async createUserPolicyLinkedWithTemplate(
    curamaPrincipal: CuramaPrincipal,
    policyTemplate: PolicyTemplateItem,
  ): Promise<CreatePolicyCommandOutput> | undefined {
    const client = this.verifiedClient;
    const inputPolicyCommand: PolicyStatic = {
      policyStoreId: this.configuration.verifiedPermission.policyStoreId,
      definition: {
        templateLinked: {
          policyTemplateId: policyTemplate.policyTemplateId,
          principal: {
            entityType: this.getSchemaPrincipalEntityName(),
            entityId: curamaPrincipal.email,
          },
        },
      } as PolicyDefinition,
    };

    const createPolicyCommand = new CreatePolicyCommand(inputPolicyCommand);
    const response = await client.send(createPolicyCommand).catch((ex) => {
      this.logger.error(ex);
      return undefined;
    });

    return response;
  }

  /**
   * generate description for policy template
   * @param action string
   * @param resource string
   * @returns string
   */
  private generateDescriptionPolicyTemplate(action: string, resource: string): string {
    // When change this description, please update regex to get action
    return `[PolicyTemplate][Terry][Permit]-[Principal:Admin] invoke [Action:${action}] on [Resource:${resource}]`;
  }

  /**
   * create policy template
   * @param curamaResource CuramaResource
   * @param curamaActions CuramaAction
   * @param when string|null
   * @returns CreatePolicyTemplateCommandOutput | undefined
   */
  async createPolicyTemplate(
    curamaResource: CuramaResource,
    curamaActions: CuramaAction[],
    when?: string,
  ): Promise<CreatePolicyTemplateCommandOutput> | undefined {
    const client = this.verifiedClient;
    const statement = this.generatePermitStatementPolicyTemplate(curamaResource, curamaActions, when);
    const curamaActionStringify = curamaActions.map((action) => this.getActionId(action, curamaResource)).join(',');
    const inputPolicyTemplateCommand: CreatePolicyTemplateCommandInput = {
      policyStoreId: this.configuration.verifiedPermission.policyStoreId,
      description: this.generateDescriptionPolicyTemplate(curamaActionStringify, curamaResource),
      statement,
    };

    const createPolicyTemplateCommand = new CreatePolicyTemplateCommand(inputPolicyTemplateCommand);
    const response = await client.send(createPolicyTemplateCommand).catch((ex) => {
      this.logger.error(ex);
      return undefined;
    });

    return response;
  }

  /**
   * init policy templates
   */
  async initPolicyTemplates(): Promise<void> {
    const promises = [];
    for (const [resource, actions] of Object.entries(CuramaPolicyTemplates)) {
      for (const action of actions) {
        promises.push(this.createPolicyTemplate(resource as CuramaResource, [action]));
      }
    }

    await Promise.all(promises);
  }

  /**
   * authorize user permission
   * @param resource CuramaResource
   * @param action CuramaAction
   * @returns Promise<boolean>
   */
  async authorize(req: Request, curamaResource: CuramaResource, curamaAction: CuramaAction): Promise<boolean> {
    const curamaPrincipal = req.terrySession.admin as Partial<CuramaPrincipal>;
    const action = this.getActionId(curamaAction, curamaResource);

    debug('[verifyPermission] curamaPrincipal', {
      email: curamaPrincipal?.email,
      name: curamaPrincipal?.name,
      policies: req?.terrySession?.admin?.policies,
      can: {
        curamaResource,
        curamaAction,
      },
    });

    // define  entity
    const resourceEntity = {
      identifier: {
        entityType: this.getSchemaResourceEntityName(curamaResource),
        entityId: '*',
      },
      attributes: {},
      parents: [],
    };

    // combine data into input for authorizing
    const inputForAuthorizing: IsAuthorizedInput = {
      policyStoreId: this.configuration.verifiedPermission.policyStoreId,
      principal: {
        entityType: this.getSchemaPrincipalEntityName(),
        entityId: curamaPrincipal.email,
      },
      action: {
        actionType: this.getActionSchemaName(),
        actionId: action,
      },
      resource: {
        entityType: this.getSchemaResourceEntityName(curamaResource),
        entityId: '*',
      },
      entities: {
        entityList: [resourceEntity],
      },
    };

    const isAuthorizedCommandInput = new IsAuthorizedCommand(inputForAuthorizing);

    try {
      const response = await this.verifiedClient.send(isAuthorizedCommandInput);
      debug('[verifyPermission] response: ', response);
      return response.decision == Decision.ALLOW;
    } catch (ex) {
      this.logger.error(ex);
      return false;
    }
  }

  /**
   * get all policies
   * @returns Promise<CuramaPolicyItem[] | undefined>
   */
  async getAllPolicies(): Promise<CuramaPolicyItem[] | undefined> {
    let isFetching = true;
    let nextToken = null;
    const allPolicies: CuramaPolicyItem[] = [];
    while (isFetching) {
      const listPolicyCommand = new ListPoliciesCommand({
        policyStoreId: this.configuration.verifiedPermission.policyStoreId,
        maxResults: this.MAX_RESULTS,
        nextToken,
      });
      const result = await this.verifiedClient.send(listPolicyCommand);

      nextToken = result.nextToken;
      allPolicies.push(...result.policies);
      if (!nextToken) {
        isFetching = false;
      }
    }

    return allPolicies;
  }

  /**
   * get all template policies
   * @returns Promise<CuramaPolicyTemplateItem[] | undefined>
   */
  async getAllPolicyTemplates(): Promise<CuramaPolicyTemplateItem[] | undefined> {
    let isFetching = true;
    let nextToken = null;
    const allTemplatePolicies: CuramaPolicyTemplateItem[] = [];
    while (isFetching) {
      const listPolicyCommand = new ListPolicyTemplatesCommand({
        policyStoreId: this.configuration.verifiedPermission.policyStoreId,
        maxResults: this.MAX_RESULTS,
        nextToken,
      });
      const result = await this.verifiedClient.send(listPolicyCommand);
      const transformer = [];
      for (const policy of result.policyTemplates) {
        const parsedData = this.getActionAndResourceFromDescription(policy.description);
        transformer.push({ ...policy, action: parsedData?.action ?? '', resource: parsedData?.resource ?? '' });
      }

      nextToken = result.nextToken;
      allTemplatePolicies.push(...transformer);
      if (!nextToken) {
        isFetching = false;
      }
    }

    return allTemplatePolicies;
  }

  /**
   * check that policy templates is exists
   * @returns boolean
   */
  async checkWasInitPolicyTemplates(): Promise<boolean> {
    const listPolicyCommand = new ListPolicyTemplatesCommand({
      policyStoreId: this.configuration.verifiedPermission.policyStoreId,
      maxResults: 2,
    });
    const result = await this.verifiedClient.send(listPolicyCommand);
    return result.policyTemplates.length > 0;
  }

  /**
   * delete policy by policy id
   * @param policyId string
   * @returns Promise<DeletePolicyCommandOutput>
   */
  async deletePolicy(policyId: string): Promise<DeletePolicyCommandOutput> {
    const deletePolicyCommand = new DeletePolicyCommand({
      policyStoreId: this.configuration.verifiedPermission.policyStoreId,
      policyId,
    });

    return await this.verifiedClient.send(deletePolicyCommand);
  }

  /**
   * delete policy template by policy template id
   * @param policyTemplateId string
   * @returns Promise<DeletePolicyTemplateCommandOutput>
   */
  async deletePolicyTemplate(policyTemplateId: string): Promise<DeletePolicyTemplateCommandOutput> {
    const deletePolicyCommand = new DeletePolicyTemplateCommand({
      policyStoreId: this.configuration.verifiedPermission.policyStoreId,
      policyTemplateId,
    });

    return await this.verifiedClient.send(deletePolicyCommand);
  }

  /**
   * Get all schema entities
   * @returns Promise<GetSchemaCommandOutput>
   */
  async getAllSchemaEntities(): Promise<GetSchemaCommandOutput> {
    const listSchemaEntitiesCommand = new GetSchemaCommand({
      policyStoreId: this.configuration.verifiedPermission.policyStoreId,
    });

    return await this.verifiedClient.send(listSchemaEntitiesCommand);
  }

  /**
   * get list policies by principal
   * @param principal principal
   * @returns CuramaPolicyItem[]
   */
  async getPoliciesByPrincipal(principal: CuramaPrincipal): Promise<CuramaPolicyItem[]> {
    let isFetching = true;
    let nextToken = null;
    const allPolicies: CuramaPolicyItem[] = [];
    while (isFetching) {
      const listPolicyCommand = new ListPoliciesCommand({
        filter: {
          principal: {
            identifier: {
              unspecified: false,
              entityType: this.getSchemaPrincipalEntityName(),
              entityId: principal.email,
            } as unknown as EntityReference.IdentifierMember,
          } as EntityReference,
        },
        policyStoreId: this.configuration.verifiedPermission.policyStoreId,
        maxResults: this.MAX_RESULTS,
        nextToken,
      });
      const result = await this.verifiedClient.send(listPolicyCommand);

      nextToken = result.nextToken;
      allPolicies.push(...result.policies);
      if (!nextToken) {
        isFetching = false;
      }
    }

    return allPolicies;
  }

  /**
   * get resource with actions
   * @returns Promise<Record<string, string[]>>
   */
  async getAllPoliciesResourcesWithActions(): Promise<Record<string, string[]>> {
    // define resources with actions object result
    const resourcesWithActions: Record<string, string[]> = {};
    // get curama namespace
    const curamaNamepsace = this.configuration.verifiedPermission.namespace;
    // get schema definition
    const schemaDefinition = await this.getAllSchemaEntities();
    // parse schema definition
    const schema = JSON.parse(schemaDefinition.schema);
    if (!schema) {
      // if schema is not defined, throw error
      throw new Error('Cannot get schema definition!');
    }

    // get authorization resources and actions defined in aws verified permission.
    const curamaAuthorizationDefinition = schema[curamaNamepsace];
    if (!curamaAuthorizationDefinition) {
      // if not found curama namespace in schema, throw error
      throw new Error(`The ${curamaNamepsace} namespace is not defined authorization rules in schema!`);
    }

    // get all resources except principal entity
    const resources = Object.keys(curamaAuthorizationDefinition.entityTypes).filter(
      (x) => x !== this.PRINCIPAL_ENTITY_NAME,
    );

    // init resource with actions
    for (const resource of resources) {
      resourcesWithActions[resource] = [];
    }

    // get all actions of each resource
    for (const [actionName, actionValue] of Object.entries(curamaAuthorizationDefinition.actions)) {
      const action = actionValue as CuramaActionOutputSchema;
      const applyResource = action.appliesTo.resourceTypes[0];
      resourcesWithActions[applyResource].push(actionName);
    }

    const sortedKeys = Object.keys(resourcesWithActions).sort();

    const sortedObject: { [key: string]: any } = {};

    sortedKeys.forEach((key) => {
      sortedObject[key] = resourcesWithActions[key];
    });

    return sortedObject;
  }

  /**
   * get resources and actions of all user's policies
   * @param user CuramaPrincipal
   * @returns Promise<any>
   */
  async getUserPoliciesResourcesWithActions<T extends { [key: string]: string[] }>(user: CuramaPrincipal): Promise<T> {
    const listUserPolicies = await this.getPoliciesByPrincipal(user);
    const policyTemplates = await this.getAllPolicyTemplates();
    listUserPolicies.map((policy) => {
      const linkedTemplate = policyTemplates.find(
        (template) => template.policyTemplateId == policy.definition.templateLinked?.policyTemplateId,
      );

      if (linkedTemplate) {
        policy.action = linkedTemplate.action;
      }

      return policy;
    });

    const result = {} as any;
    listUserPolicies.forEach((policy) => {
      const resource = policy?.resource.entityType.replace(this.configuration.verifiedPermission.namespace + '::', '');
      if (!result.hasOwnProperty(resource)) {
        result[resource] = [];
      }

      result[resource].push(policy.action);
    });

    return result;
  }

  /**
   * find policy
   * @param policyId string
   * @returns Promise<GetPolicyCommandOutput>
   */
  async findPolicy(policyId: string): Promise<GetPolicyCommandOutput> {
    const getPolicyCommand = new GetPolicyCommand({
      policyStoreId: this.configuration.verifiedPermission.policyStoreId,
      policyId: policyId,
    });

    return await this.verifiedClient.send(getPolicyCommand);
  }

  /**
   * find policy template by id
   * @param policyTemplateId string
   * @returns Promise<GetPolicyTemplateCommandOutput>
   */
  async findPolicyTemplate(policyTemplateId: string): Promise<GetPolicyTemplateCommandOutput> {
    const getPolicyTemplateCommand = new GetPolicyTemplateCommand({
      policyStoreId: this.configuration.verifiedPermission.policyStoreId,
      policyTemplateId: policyTemplateId,
    });

    return await this.verifiedClient.send(getPolicyTemplateCommand);
  }

  /**
   * parse action and resource from statement
   * @param statement string
   * @returns T | null
   */
  getActionAndResourceFromStatement<T extends { action: string; resource: string }>(statement: string): T | null {
    // Define the regex pattern
    const actionSchema = this.getActionSchemaName();
    const verifyPermissionNamespace = this.configuration.verifiedPermission.namespace;
    const regexPattern = `action in \\[${actionSchema}::"(\\w+)"\\], resource == ${verifyPermissionNamespace}::(\\w+)::`;
    // Use the match() function to find matches
    const matches = statement.match(regexPattern);
    if (matches) {
      const action = matches[1];
      const resource = matches[2];
      // Format the output data as an object
      return { action, resource } as T;
    }

    return null;
  }

  /**
   * parse action and resource from description
   * @param description string
   * @returns T
   */
  getActionAndResourceFromDescription<T extends { action: string; resource: string }>(description: string): T | null {
    // Define the regex pattern
    const regexPattern = /invoke \[Action:(\w+)\] on \[Resource:(\w+)\]/;
    // Use the match() function to find matches
    const matches = description.match(regexPattern);
    if (matches) {
      const action = matches[1];
      const resource = matches[2];
      // Format the output data as an object
      return { action, resource } as T;
    }

    return null;
  }

  /**
   * updatePolicies
   * @param user CuramaPrincipal
   * @param policyUpdates string[]
   * @returns void
   */
  async updateUserPolicies(user: CuramaPrincipal, policyUpdates: string[]): Promise<void> {
    const userPolicies = await this.getPoliciesByPrincipal(user);
    const policyTemplates = await this.getAllPolicyTemplates();
    // delete all user policies
    const deletePromises = [];
    userPolicies.map((policy) => {
      deletePromises.push(this.deletePolicy(policy.policyId));
    });
    await Promise.all(deletePromises);
    // create new user policies
    const createPromises = [];
    for (const policyUpdate of policyUpdates) {
      const resourceAndActionParsed = policyUpdate.split('_');
      const resource = resourceAndActionParsed[0];
      const action = resourceAndActionParsed[1];
      const policy = policyTemplates.find((policy) => policy.action == action && policy?.resource == resource);
      if (policy) {
        createPromises.push(this.createUserPolicyLinkedWithTemplate(user, policy));
      }
    }
    await Promise.all(createPromises);
  }
}
