export const CuramaActions = {
  View: 'view',
  Edit: 'edit',
  Review: 'review',
  Invoke: 'invoke',
  Delete: 'delete',
  ForceDelete: 'forceDelete',
  DeleteMessageThread: 'deleteMessage',
  Approve: 'approve',
} as const;

export type CuramaAction = (typeof CuramaActions)[keyof typeof CuramaActions];

export interface CuramaActionOutputSchema {
  appliesTo: {
    principalTypes: string[];
    resourceTypes: string[];
    context: {
      type: string;
      attributes: string[];
    };
  };
}
