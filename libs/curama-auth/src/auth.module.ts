import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CognitoServiceProvider, IdentityServiceProvider } from './identities';
import { CURAMA_AUTH_SERVICE, CURAMA_AUTHORIZATION_SERVICE } from './constants';
/** active SessionData for express-session */
import './sessions';
import { IsAuthenticatedMiddleware } from './middlewares';
import configs from './configs';
import { AWSVerifiedPermissionProvider } from './authorization';
import { ApiClientModule, ApiClientService } from '@vietnam/cnga-http-request';
import { AdminEntity, TeamEntity } from './entities';
import { TypeOrmModule, getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminWebhookController } from './controllers/admin-webhook.controller';
import { DepartmentTeamWebhookController } from './controllers/department-team-webhook.controller';
import { AdminService } from './services/admin.service';
import { TeamService } from './services/team.service';
import { TerrySessionMiddleware, GoogleChannelVerifyMiddleware } from './middlewares';
import { TerrySessionManager } from './sessions';
import { QueueProvider } from '@providers/queue.provider';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [...configs],
    }),
    ApiClientModule.forRoot(),
    TypeOrmModule.forFeature([AdminEntity, TeamEntity]),
    QueueProvider.register(),
  ],
  controllers: [AdminWebhookController, DepartmentTeamWebhookController],
  providers: [
    {
      provide: CURAMA_AUTH_SERVICE,
      useFactory: (
        configService: ConfigService,
        apiClient: ApiClientService,
        adminRepository: Repository<AdminEntity>,
        authorizationService: AWSVerifiedPermissionProvider,
      ): IdentityServiceProvider => {
        return new CognitoServiceProvider(configService, apiClient, adminRepository, authorizationService);
      },
      inject: [ConfigService, ApiClientService, getRepositoryToken(AdminEntity), CURAMA_AUTHORIZATION_SERVICE],
    },
    {
      provide: CURAMA_AUTHORIZATION_SERVICE,
      useFactory: (configService: ConfigService) => {
        return new AWSVerifiedPermissionProvider(configService);
      },
      inject: [ConfigService],
    },
    ConfigService,
    IsAuthenticatedMiddleware,
    TerrySessionMiddleware,
    AdminService,
    TeamService,
    TerrySessionManager,
  ],
  exports: [CURAMA_AUTH_SERVICE, CURAMA_AUTHORIZATION_SERVICE, IsAuthenticatedMiddleware, TerrySessionMiddleware],
})
export class AuthModule {
  configure(consumer: MiddlewareConsumer) {
    // application will provide logIn function to express request
    consumer.apply(TerrySessionMiddleware).forRoutes({ path: '*', method: RequestMethod.ALL });
    // application will auto redirect to specified url if user already logged in
    consumer
      .apply(IsAuthenticatedMiddleware)
      .forRoutes({ path: 'admin/consulting-message/login', method: RequestMethod.ALL });
    // check google channel valid
    consumer.apply(GoogleChannelVerifyMiddleware).forRoutes(AdminWebhookController, DepartmentTeamWebhookController);
  }
}
