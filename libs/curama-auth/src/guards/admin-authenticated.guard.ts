import { IdentityServiceProvider } from '@curama-auth/identities/identity.provider';
import { CanActivate, ExecutionContext, Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { CURAMA_AUTH_SERVICE } from '../constants';

@Injectable()
export class AdminAuthenticatedGuard implements CanActivate {
  constructor(@Inject(CURAMA_AUTH_SERVICE) private readonly authService: IdentityServiceProvider) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();
    const response = await this.authService.getSessionAdmin(req);
    if (!response) {
      throw new UnauthorizedException('Unauthorized');
    }
    req.terrySession.admin = response;
    return true;
  }
}
