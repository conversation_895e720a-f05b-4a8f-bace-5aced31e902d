import { CanActivate, ExecutionContext, Inject, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CURAMA_AUTHORIZATION_SERVICE } from '../constants';
import { AdminAuthorizationMetaData } from '../decorators/admin-authorization.decorator';
import { AuthorizationInput } from '../authorization/aws/types';

@Injectable()
export class AdminAuthorizeGuard implements CanActivate {
  constructor(
    @Inject(Reflector.name) private readonly reflector: Reflector,
    @Inject(CURAMA_AUTHORIZATION_SERVICE) private readonly authorizationService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();
    const adminAuthorizationMetaData = this.reflector.get<AuthorizationInput>(
      AdminAuthorizationMetaData,
      context.getHandler(),
    );

    const response = await this.authorizationService.authorize(
      req,
      adminAuthorizationMetaData?.resource,
      adminAuthorizationMetaData?.action,
    );
    return response;
  }
}
