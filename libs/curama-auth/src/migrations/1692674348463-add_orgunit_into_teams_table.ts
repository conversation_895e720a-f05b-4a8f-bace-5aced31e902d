import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddOrgunitIntoTeamsTable1692674348463 implements MigrationInterface {
  private readonly tableName = 'Teams';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns(this.tableName, [
      new TableColumn({
        name: 'orgunit_path',
        type: 'varchar',
        length: '255',
        isNullable: true,
        comment: 'orgunit_path get from google while login or sync from google',
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns(this.tableName, ['orgunit_path']);
  }
}
