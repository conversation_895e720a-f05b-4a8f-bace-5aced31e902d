import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddSessionDataToAdminsTable1692674290696 implements MigrationInterface {
  private readonly tableName = 'Admins';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns(this.tableName, [
      new TableColumn({
        name: 'sub',
        type: 'varchar',
        length: '255',
        isNullable: true,
        comment: 'unique uuid to identity user in aws cognito',
      }),
      new TableColumn({
        name: 'department',
        type: 'varchar',
        length: '255',
        isNullable: true,
        comment: 'admin department get from google while login or sync from google',
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns(this.tableName, ['sub', 'department']);
  }
}
