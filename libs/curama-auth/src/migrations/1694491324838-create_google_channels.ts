import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateGoogleChannels1694491324838 implements MigrationInterface {
  private readonly tableName = 'GoogleChannels';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'application_name',
            type: 'varchar',
          },
          {
            name: 'event_name',
            type: 'varchar',
          },
          {
            name: 'channel_id',
            type: 'uuid',
          },
          {
            name: 'resource_id',
            type: 'varchar',
          },
          {
            name: 'resource_uri',
            type: 'varchar',
          },
          {
            name: 'type',
            type: 'varchar',
          },
          {
            name: 'address',
            type: 'varchar',
          },
          {
            name: 'kind',
            type: 'varchar',
          },
          {
            name: 'scope',
            type: 'varchar',
          },
          {
            name: 'expiration',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            comment: 'The time the channel was created',
            default: 'now()',
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
