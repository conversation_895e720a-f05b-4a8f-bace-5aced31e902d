import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleChannelScope, GoogleOrganizationUnitEventType, GoogleUserEventType } from '../constants';
import { GoogleChannelEntity } from '../entities/google-channel.entity';
import { DataSource, Repository } from 'typeorm';
import { GoogleService } from './google.service';
import * as dayjs from 'dayjs';
import { GoogleConfigOptions } from '@curama-auth/configs/google';

@Injectable()
export class GoogleChannelService {
  private googleService;
  private googleChannelRepository: Repository<GoogleChannelEntity>;
  private readonly dateFormat = 'YYYY-MM-DD HH:mm:ss';
  private readonly logger = new Logger(GoogleChannelService.name);
  constructor(private readonly dataSource: DataSource, private readonly configService: ConfigService) {
    this.googleService = new GoogleService(configService);
    this.googleChannelRepository = this.dataSource.getRepository(GoogleChannelEntity);
  }

  async registerNeccessaryGoogleChannel() {
    const expirationTime = dayjs(new Date()).add(1, 'h').format(this.dateFormat);
    const promiseCheckingChannels = [];
    const googleConfig = this.configService.get<GoogleConfigOptions>('curama-auth.google');
    promiseCheckingChannels.push(
      this.registerGoogleChannel(
        expirationTime,
        GoogleUserEventType.Add,
        googleConfig.channels.adminCreated,
        GoogleChannelScope.User,
      ),
      this.registerGoogleChannel(
        expirationTime,
        GoogleUserEventType.Update,
        googleConfig.channels.adminUpdated,
        GoogleChannelScope.User,
      ),
      this.registerGoogleChannel(
        expirationTime,
        GoogleUserEventType.Delete,
        googleConfig.channels.adminDeleted,
        GoogleChannelScope.User,
      ),
      this.registerGoogleChannel(
        expirationTime,
        GoogleOrganizationUnitEventType.Create,
        googleConfig.channels.orgUnitCreated,
        GoogleChannelScope.OrgUnit,
      ),
      this.registerGoogleChannel(
        expirationTime,
        GoogleOrganizationUnitEventType.ChangeName,
        googleConfig.channels.orgUnitUpdated,
        GoogleChannelScope.OrgUnit,
      ),
      this.registerGoogleChannel(
        expirationTime,
        GoogleOrganizationUnitEventType.Delete,
        googleConfig.channels.orgUnitDeleted,
        GoogleChannelScope.OrgUnit,
      ),
      this.registerGoogleChannel(
        expirationTime,
        GoogleOrganizationUnitEventType.Move,
        googleConfig.channels.orgUnitMoveTeam,
        GoogleChannelScope.OrgUnit,
      ),
    );

    await Promise.all(promiseCheckingChannels);
  }

  private async registerGoogleChannel(
    expirationTime: string,
    eventName: string,
    address: string,
    scope: string,
  ): Promise<void> {
    this.logger.log(`[Checking] Google channel for ${eventName} in scope ${scope} compare with ${expirationTime}`);

    const needRenewChannels = await this.googleChannelRepository
      .createQueryBuilder()
      .where('event_name = :eventName and scope = :scope and expiration <= :expiration', {
        eventName: eventName,
        expiration: expirationTime,
        scope: scope,
      })
      .getMany();

    await this.dataSource.transaction(async () => {
      const needToStopChannels: GoogleChannelEntity[] = [];
      if (needRenewChannels && needRenewChannels.length > 0) {
        const needToStopChannelIds = [];
        for (const channel of needRenewChannels) {
          this.logger.log(`[Result] Google channel for ${scope} - ${eventName} is expired. Add to stop channels list.`);
          needToStopChannels.push(channel);
          needToStopChannelIds.push(channel.id);
        }

        await this.googleChannelRepository.delete(needToStopChannelIds);
      }

      const channel = await this.googleChannelRepository.findOne({
        where: { eventName: eventName, scope: scope },
      });

      if (!channel) {
        this.logger.log(`[Result] Register google channel for ${scope} - ${eventName}`);
        switch (scope) {
          case GoogleChannelScope.User:
            await this.registerGoogleUserChannel(eventName, address);
            break;
          case GoogleChannelScope.OrgUnit:
            await this.registerGoogleOrgUnitChannel(eventName, address);
            break;
        }
      } else {
        this.logger.log(
          `[Result] Google channel ${channel.id} for ${scope} - ${eventName} is valid until (${channel.expiration}). Skip register channel.`,
        );
      }

      if (needToStopChannels.length > 0) {
        this.logger.log(`[Stop expiration channels] Stop ${needToStopChannels.length} google channels`);
        const stopPromiseJobs = [];
        for (const channel of needToStopChannels) {
          this.logger.log(`[Stop] Google channel ${channel.id} - ${channel.eventName}: ${channel.address}`);
          stopPromiseJobs.push(this.googleService.stopChannel(channel.channelId, channel.resourceId));
        }

        await Promise.all(stopPromiseJobs);
      }
    });
  }

  private async registerGoogleUserChannel(eventType: string, address: string): Promise<void> {
    const registerResponse = await this.googleService.registerListenGoogleUserChangedHook(eventType, address);
    if (registerResponse) {
      await this.saveGoogleChannel(registerResponse, eventType, address, GoogleChannelScope.User);
      this.logger.log('Register google user channel successfully');
      this.logger.debug(`Register google user channel response: ${JSON.stringify(registerResponse)}`);
    } else {
      this.logger.error('Register google user channel failed');
    }
  }

  private async registerGoogleOrgUnitChannel(eventType: string, address: string): Promise<void> {
    const registerResponse = await this.googleService.registerListenerGoogleOrgUnitChannel(eventType, address);
    if (registerResponse) {
      await this.saveGoogleChannel(registerResponse, eventType, address, GoogleChannelScope.OrgUnit);
      this.logger.log('Register google organization unit channel successfully');
      this.logger.debug(`Register google organization unit channel response: ${JSON.stringify(registerResponse)}`);
    } else {
      this.logger.error('Register google organization unit channel failed');
    }
  }

  private async saveGoogleChannel(
    registerResponse: any,
    eventType: string,
    address: string,
    scope: string,
  ): Promise<void> {
    const googleChannelRepository = this.dataSource.getRepository(GoogleChannelEntity);
    const googleChannel = new GoogleChannelEntity();
    googleChannel.channelId = registerResponse.id;
    googleChannel.applicationName = 'admin';
    googleChannel.eventName = eventType;
    googleChannel.resourceId = registerResponse.resourceId;
    googleChannel.resourceUri = registerResponse.resourceUri;
    googleChannel.type = registerResponse.type ?? 'web_hook';
    googleChannel.address = registerResponse.address ?? address;
    googleChannel.kind = registerResponse.kind;
    googleChannel.scope = scope;
    googleChannel.expiration = new Date(Number(registerResponse.expiration));
    await googleChannelRepository.save(googleChannel);
  }
}
