import { sleep } from '@curama-auth/helper';
import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { AdminStatus, SLEEP_BEFORE_GOOGLE_API_CALL } from '../constants';
import { AdminEntity, AdminsStoresEntity, TaskEntity, TeamEntity, TeamsStoresEntity } from '@src/entities';
import { GoogleService } from './google.service';
import { DataSource, In, Repository, EntityManager } from 'typeorm';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { SYNC_OPENSEARCH_MESSAGE_MQ_TASK } from '@common/constants';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  constructor(
    @InjectRepository(AdminEntity)
    private readonly adminRepository: Repository<AdminEntity>,
    @InjectRepository(TeamEntity)
    private readonly teamRepository: Repository<TeamEntity>,
    private readonly configService: ConfigService,
    private dataSource: DataSource,
    private readonly amqpConnection: AmqpConnection,
  ) {}

  async createAdmin(email: string): Promise<void> {
    const admin = await this.adminRepository.findOneBy({
      email: email,
    });

    if (!admin) {
      this.logger.log('[createAdmin] create admin: ' + email);
      const googleService = new GoogleService(this.configService);
      await sleep(SLEEP_BEFORE_GOOGLE_API_CALL);
      const data = await googleService.getUser(email);
      this.logger.log('[createAdmin] get user data: ' + JSON.stringify(data));
      const adminEntity = new AdminEntity();
      adminEntity.email = email;
      adminEntity.name = data.name?.fullName ?? 'unknown';
      adminEntity.status = AdminStatus.Active;
      const orgUnitPath = data.orgUnitPath;
      const team = await this.teamRepository.findOneBy({ orgUnitPath });
      adminEntity.teams = team ? [team] : [];
      await this.adminRepository.save(adminEntity);
      this.logger.log('[createAdmin] Admin created: ' + email);
    } else {
      this.logger.log('[createAdmin] Admin existed - updating: ' + email);
      this.updateAdmin(email);
    }
  }

  async updateAdmin(email: string): Promise<void> {
    const googleService = new GoogleService(this.configService);
    await sleep(SLEEP_BEFORE_GOOGLE_API_CALL);
    const data = await googleService.getUser(email);
    this.logger.log('[updateAdmin] get user data from google: ' + JSON.stringify(data));

    if (!data) {
      this.logger.log('[updateAdmin] Cannot get user data from google: ' + email);
      throw new InternalServerErrorException(`Cannot get user data from google: ${email}`);
    }

    const fullname = data.name?.fullName;
    const status = data.suspended ? AdminStatus.Inactive : AdminStatus.Active;
    const orgUnitPath = data.orgUnitPath;
    const updatingInfo = {
      name: fullname,
      status: status,
    };

    let isExistAdmin = await this.adminRepository.exist({
      where: {
        email,
      },
    });

    if (!isExistAdmin) {
      this.logger.log(`[updateAdmin] Cannot found admin with email: ${email}. Try to check by alias email`);

      if (data.aliases?.length > 0) {
        const aliasEmail = data.aliases[0];
        this.logger.log(
          `[updateAdmin] Find admin with alias email: ${aliasEmail}. Maybe user just changed email from ${email} to ${aliasEmail}.`,
        );
        isExistAdmin = await this.adminRepository.exist({ where: { email: aliasEmail } });
        if (isExistAdmin) {
          this.logger.log(
            `[updateAdmin] User just changed email from ${email} to ${aliasEmail}. Update email to ${aliasEmail} and continue updating info.`,
          );
          updatingInfo['email'] = email;
          email = aliasEmail;
        }
      }
    }

    const admin = await this.adminRepository.findOne({
      where: {
        email,
      },
      relations: ['teams'],
    });

    if (admin) {
      await this.dataSource.transaction(async (entityManager: EntityManager) => {
        this.logger.log('[updateAdmin] Update admin: ' + email);
        await entityManager.update(AdminEntity, { id: admin.id }, updatingInfo);

        const shouldChangeTeam = admin.teams.filter((team) => team.orgUnitPath === orgUnitPath).length === 0;
        if (shouldChangeTeam) {
          const relatedTeam = admin.teams[0] ?? null;
          if (relatedTeam) {
            this.logger.log(
              '[updateAdmin] Change team of admin from ' + relatedTeam.orgUnitPath + ' to ' + orgUnitPath,
            );
          } else {
            this.logger.log('[updateAdmin] Add admin to team ' + orgUnitPath);
          }

          const team = await entityManager.findOneBy(TeamEntity, { orgUnitPath });
          if (team) {
            admin.teams = [team];
            await entityManager.save(admin);
          }
        }

        if (status === AdminStatus.Inactive) {
          await this.deleteRelationship(entityManager, admin.id);
        }
        this.logger.log(`[updateAdmin] Admin updated: ${JSON.stringify(updatingInfo)}`);
      });

      if (status === AdminStatus.Inactive) {
        await this.amqpConnection.publish('taskManager', SYNC_OPENSEARCH_MESSAGE_MQ_TASK, {
          event: RabbitMQEventType.RemoveAdminFromStore,
          content: {
            admin_id: admin.id,
          },
        });
      }

      return;
    } else {
      this.logger.log(`[updateAdmin] Cannot found admin: ${email}`);
      throw new InternalServerErrorException(`[updateAdmin] Cannot found admin: ${email}`);
    }
  }

  async deleteAdmin(email: string): Promise<void> {
    const admin = await this.adminRepository.findOneBy({ email });
    if (admin) {
      this.logger.log('[deleteAdmin] Delete admin: ' + email);
      admin.teams = [];
      admin.status = AdminStatus.Inactive;

      await this.dataSource.transaction(async (entityManager: EntityManager) => {
        await Promise.all([entityManager.save(admin), this.deleteRelationship(entityManager, admin.id)]);
      });

      await this.amqpConnection.publish('taskManager', SYNC_OPENSEARCH_MESSAGE_MQ_TASK, {
        event: RabbitMQEventType.RemoveAdminFromStore,
        content: {
          admin_id: admin.id,
        },
      });

      return;
    } else {
      this.logger.log(`[deleteAdmin] Cannot found admin: ${email}`);
      throw new InternalServerErrorException(`[deleteAdmin] Cannot found admin: ${email}`);
    }
  }

  async deleteRelationship(entityManager: EntityManager, adminId: string) {
    try {
      this.logger.log(
        '[deleteRelationship] Delete relationship of admin (adminsStores & teamsStores & task): ' + adminId,
      );
      const listAdminStoreByAdmin = await entityManager.findBy(AdminsStoresEntity, { adminId });
      const listStoreId = listAdminStoreByAdmin.map((item) => item.storeId);

      await Promise.all([
        entityManager.delete(AdminsStoresEntity, { adminId }),
        entityManager.delete(TeamsStoresEntity, { storeId: In(listStoreId) }),
        entityManager.update(
          TaskEntity,
          { assignmentAdminId: adminId },
          { assignmentAdminId: null, assignmentTeamId: null },
        ),
      ]);

      this.logger.log(
        '[deleteRelationship] Delete relationship of admin (adminsStores & teamsStores & task) successfully',
      );
    } catch (err) {
      this.logger.error('[deleteRelationship] Error when delete relationship of admin: ' + adminId);
      throw new InternalServerErrorException(
        '[deleteRelationship] Error when delete relationship of admin: ' + adminId,
      );
    }
  }
}
