import { generateJWTToken } from '@curama-auth/helper';
import { GoogleConfigOptions } from '../configs/google';
import { HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { randomUUID } from 'crypto';
import { admin_directory_v1, admin_reports_v1, google } from 'googleapis';

export class GoogleService {
  protected auth;
  protected authClient;
  protected googleConfig: GoogleConfigOptions;

  constructor(private readonly configService: ConfigService) {
    this.googleConfig = this.configService.get<GoogleConfigOptions>('curama-auth.google');
    const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_KEY);
    this.auth = new google.auth.GoogleAuth({
      credentials,
      scopes: this.googleConfig.scopes,
    });
  }

  /**
   * init google authenticate client
   * @returns Promise<void>
   */
  public async initGoogleAuthClient(): Promise<void> {
    if (!this.authClient) {
      this.authClient = await this.auth.getClient();
      this.authClient.subject = this.googleConfig.accountServiceEmail;
    }
  }

  private async directorySDK() {
    await this.initGoogleAuthClient();
    return google.admin({ version: 'directory_v1', auth: this.authClient } as admin_directory_v1.Options);
  }

  private async reportSDK() {
    await this.initGoogleAuthClient();
    return google.admin({ version: 'reports_v1', auth: this.authClient } as admin_reports_v1.Options);
  }

  /**
   * get User
   * @param userKey string
   * @returns admin_directory_v1.Schema$User
   */
  async getUser(userKey: string): Promise<admin_directory_v1.Schema$User> {
    const directory = await this.directorySDK();
    const response = await directory.users.get({
      userKey,
      viewType: 'admin_view',
      projection: 'full',
    });

    if (response.status === HttpStatus.OK) {
      return response.data;
    }

    return null;
  }

  /**
   * get users
   * @returns admin_directory_v1.Schema$User[]
   */
  async getUsers(): Promise<admin_directory_v1.Schema$User[]> {
    const directory = await this.directorySDK();
    const response = await directory.users.list({
      domain: this.googleConfig.domain,
      maxResults: 20,
      orderBy: 'email',
      viewType: 'admin_view',
      projection: 'full',
    });

    if (response.status === HttpStatus.OK) {
      const users = response.data.users;
      return users;
    }

    return [];
  }

  /**
   * get OrgUnits
   * @returns Promise<admin_directory_v1.Schema$OrgUnits[]>
   */
  async getOrgUnits(
    option?: Partial<admin_directory_v1.Params$Resource$Orgunits$List>,
  ): Promise<admin_directory_v1.Schema$OrgUnit[]> {
    const directory = await this.directorySDK();
    const response = await directory.orgunits.list({
      customerId: this.googleConfig.customerId,
      ...option,
    });
    return response.data.organizationUnits;
  }

  /**
   * get org unit
   * @param orgUnitPath string
   * @returns admin_directory_v1.Schema$OrgUnit
   */
  async getOrgUnit(orgUnitPath: string): Promise<admin_directory_v1.Schema$OrgUnit | undefined> {
    const directory = await this.directorySDK();
    const response = await directory.orgunits
      .get({
        customerId: this.googleConfig.customerId,
        orgUnitPath,
      })
      .catch(() => undefined);

    return response?.data;
  }

  /**
   * register listen google workspace user changed hook
   * @returns Promise<admin_directory_v1.Schema$Channel>
   */
  async registerListenGoogleUserChangedHook(
    event: string,
    address: string,
  ): Promise<admin_directory_v1.Schema$Channel> {
    const directory = await this.directorySDK();
    const id = randomUUID();
    const token = generateJWTToken({ id }, this.googleConfig.tokenSecret, this.googleConfig.jwtAudience, '2d');

    const response = await directory.users.watch({
      domain: this.googleConfig.domain,
      event: event,
      projection: 'full',
      viewType: 'admin_view',
      requestBody: {
        id: id,
        type: 'web_hook',
        address: address,
        token: token,
      },
    });

    if (response.status === HttpStatus.OK) {
      return response.data;
    }

    return null;
  }

  /**
   * register listen google workspace org unit changed hook
   * @returns Promise<admin_reports_v1.Schema$Channel>
   */
  async registerListenerGoogleOrgUnitChannel(
    eventName: string,
    address: string,
  ): Promise<admin_reports_v1.Schema$Channel> {
    const reportSDK = await this.reportSDK();

    const id = randomUUID();
    const token = generateJWTToken({ id }, this.googleConfig.tokenSecret, this.googleConfig.jwtAudience, '6h');
    const response = await reportSDK.activities.watch({
      customerId: this.googleConfig.customerId,
      applicationName: 'admin',
      eventName: eventName,
      userKey: 'all',
      requestBody: {
        id: id,
        type: 'web_hook',
        address: address,
        token: token,
      },
    } as admin_reports_v1.Params$Resource$Activities$Watch);

    if (response.status === HttpStatus.OK) {
      return response.data;
    }

    return null;
  }

  /**
   * stop channel
   * @param id string
   * @param resourceId string
   * @returns Promise
   */
  async stopChannel(id: string, resourceId: string): Promise<void> {
    const reportSDK = await this.reportSDK();
    await reportSDK.channels
      .stop({
        requestBody: {
          id,
          resourceId,
        },
      })
      .catch((ex) => {});
  }
}
