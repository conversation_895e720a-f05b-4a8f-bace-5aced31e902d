import { getNameFromGoogleOrgUnitPath } from '@curama-auth/helper';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Like, Not, Repository } from 'typeorm';
import { TeamEntity } from '../entities';

@Injectable()
export class TeamService {
  private readonly logger = new Logger(TeamService.name);

  constructor(
    @InjectRepository(TeamEntity)
    private readonly teamRepository: Repository<TeamEntity>,
  ) {}

  async createTeam(orgUnitPath: string): Promise<void> {
    const name = getNameFromGoogleOrgUnitPath(orgUnitPath);
    if (!name) {
      this.logger.log(`[createTeam] Cannot extract team name from orgUnitPath ${orgUnitPath}`);
      return;
    }
    const departmentOrgUnitPath = orgUnitPath.slice(0, orgUnitPath.lastIndexOf('/'));
    this.logger.log(`[createTeam] orgUnitPath: ${orgUnitPath}, extractDepartmentName: ${departmentOrgUnitPath}. `);
    let department;
    if (departmentOrgUnitPath) {
      department = await this.teamRepository.findOneBy({ orgUnitPath: departmentOrgUnitPath });
      if (department && department.isLast === true) {
        department.isLast = false;
        await this.teamRepository.save(department);
      }
    }
    const existTeam = await this.teamRepository.findOneBy({ orgUnitPath });
    if (existTeam) {
      this.logger.log(`[createTeam] Team ${orgUnitPath} already exists`);
      return;
    }
    const teamEntity = this.teamRepository.create({
      orgUnitPath,
      name,
      departmentId: department?.id || null,
      isLast: true,
    });
    await this.teamRepository.insert(teamEntity);
    this.logger.log(`[createTeam] Created successfully orgUnitPath: ${orgUnitPath}, extractTeamName: ${name}. `);
  }

  async moveTeam(currentPath: string, newPath: string): Promise<void> {
    const teamEntity = await this.teamRepository.findOneBy({ orgUnitPath: currentPath });
    if (teamEntity) {
      this.logger.log(`[moveTeam] Move team currentOrgUnitPath ${currentPath} to newOrgUnitPath ${newPath}`);
      let department;
      if (newPath !== '/') {
        department = await this.teamRepository.findOneBy({ orgUnitPath: newPath });
        if (department && department.isLast === true) {
          department.isLast = false;
          await this.teamRepository.save(department);
        }
      }
      const oldDepartmentId = teamEntity.departmentId;
      teamEntity.orgUnitPath = `${newPath === '/' ? '' : newPath}/${teamEntity.name}`;
      teamEntity.departmentId = department?.id || null;
      await this.teamRepository.save(teamEntity);

      if (teamEntity.isLast === false) {
        const children = await this.teamRepository.find({
          where: { orgUnitPath: Like(`${currentPath}/%`), id: Not(teamEntity.id) },
        });
        for (const child of children) {
          child.orgUnitPath = child.orgUnitPath.replace(currentPath, teamEntity.orgUnitPath);
          await this.teamRepository.save(child);
        }
      }
      if (oldDepartmentId) {
        const countChildren = await this.teamRepository.countBy({ departmentId: oldDepartmentId });
        if (countChildren === 0) {
          await this.teamRepository.update({ id: oldDepartmentId }, { isLast: true });
        }
      }
      this.logger.log(`[moveTeam] Moved successfully currentOrgUnitPath ${currentPath} to newOrgUnitPath ${newPath}`);
    } else {
      this.logger.log(`[moveTeam] Cannot found team ${currentPath}`);
    }
  }
  async deleteTeam(orgUnitPath: string): Promise<void> {
    const teamEntity = await this.teamRepository.findOneBy({ orgUnitPath: orgUnitPath });
    if (teamEntity) {
      await this.teamRepository.delete({ id: teamEntity.id });
      if (teamEntity.departmentId) {
        const countChildren = await this.teamRepository.countBy({ departmentId: teamEntity.departmentId });
        if (countChildren === 0) {
          await this.teamRepository.update({ id: teamEntity.departmentId }, { isLast: true });
        }
      }
      this.logger.log(`[deleteTeam] Deleted ${orgUnitPath} - id: ${teamEntity.id}`);
    } else {
      this.logger.log(`[deleteTeam] Cannot found team ${orgUnitPath}`);
    }
  }

  async updateNameDepartmentAndTeam(currentPath: string, newPath: string): Promise<void> {
    const updatedUnit = await this.teamRepository.findOneBy({ orgUnitPath: currentPath });
    if (updatedUnit) {
      this.logger.log(
        `[updateNameDepartmentAndTeam] Update team currentOrgUnitPath ${currentPath} to newOrgUnitPath ${newPath}`,
      );
      const newName = getNameFromGoogleOrgUnitPath(newPath);
      updatedUnit.name = newName;
      updatedUnit.orgUnitPath = newPath;
      await this.teamRepository.save(updatedUnit);
      if (updatedUnit.isLast === false) {
        const children = await this.teamRepository.find({
          where: { orgUnitPath: Like(`${currentPath}/%`), id: Not(updatedUnit.id) },
        });
        for (const child of children) {
          child.orgUnitPath = child.orgUnitPath.replace(currentPath, newPath);
          await this.teamRepository.save(child);
        }
      }
      this.logger.log(
        `[updateNameDepartmentAndTeam] Updated successfully currentOrgUnitPath ${currentPath} to newOrgUnitPath ${newPath}`,
      );
    } else {
      this.logger.log(`[updateNameDepartmentAndTeam] Cannot found updatedUnit ${currentPath}`);
    }
  }
}
