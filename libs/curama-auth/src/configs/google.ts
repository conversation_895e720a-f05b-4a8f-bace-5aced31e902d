import { registerAs } from '@nestjs/config';
import routes from '../common/routes';

export interface GoogleConfigOptions {
  domain: string;
  customerId: string;
  accountServiceEmail: string;
  scopes: string[];
  tokenSecret: string;
  jwtAudience: string;
  channels?: {
    adminCreated: string;
    adminUpdated: string;
    adminDeleted: string;
    orgUnitCreated: string;
    orgUnitUpdated: string;
    orgUnitDeleted: string;
    orgUnitMoveTeam: string;
  };
}

const buildChannelAddress = (channelPath: string) => {
  const path = channelPath.replace(/^\/+/, '');
  let appUrl = process.env.APP_URL;
  if (!appUrl) {
    throw new Error('APP_URL is not defined');
  }

  appUrl = appUrl.replace(/\/+$/, '');

  return `${appUrl}/${path}`;
};

const googleConfig: GoogleConfigOptions = {
  domain: process.env.GOOGLE_APP_ACCOUNT_SERVICE_DOMAIN,
  customerId: process.env.GOOGLE_APP_ACCOUNT_CUSTOMER_ID,
  accountServiceEmail: process.env.GOOGLE_APP_ACCOUNT_SERVICE_EMAIL,
  scopes: [
    'https://www.googleapis.com/auth/admin.reports.audit.readonly',
    'https://www.googleapis.com/auth/admin.directory.user.readonly',
    'https://www.googleapis.com/auth/admin.directory.group.readonly',
    'https://www.googleapis.com/auth/admin.directory.orgunit.readonly',
  ],
  tokenSecret: process.env.GOOGLE_CHANNEL_TOKEN_SECRET,
  jwtAudience: process.env.GOOGLE_CHANNEL_TOKEN_AUD,
  channels: {
    adminCreated: buildChannelAddress(routes.webhook.admin.create),
    adminUpdated: buildChannelAddress(routes.webhook.admin.update),
    adminDeleted: buildChannelAddress(routes.webhook.admin.delete),
    orgUnitCreated: buildChannelAddress(routes.webhook.orgUnit.create),
    orgUnitUpdated: buildChannelAddress(routes.webhook.orgUnit.update),
    orgUnitDeleted: buildChannelAddress(routes.webhook.orgUnit.delete),
    orgUnitMoveTeam: buildChannelAddress(routes.webhook.orgUnit.moveTeam),
  },
};

export default registerAs('curama-auth.google', (): GoogleConfigOptions => googleConfig);
