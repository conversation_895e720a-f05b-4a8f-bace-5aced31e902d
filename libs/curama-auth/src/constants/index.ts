export const CURAMA_AUTH_SERVICE = Symbol('CURAMA_AUTH_SERVICE');
export const CURAMA_AUTHORIZATION_SERVICE = Symbol('CURAMA_AUTHORIZATION_SERVICE');

export const AdminStatus = {
  Active: 1,
  Inactive: 2,
} as const;
export type AdminStatusOption = (typeof AdminStatus)[keyof typeof AdminStatus];

export const AdminStatusLabel = {
  [AdminStatus.Active]: 'Active',
  [AdminStatus.Inactive]: 'Inactive',
};

export const GoogleChannelScope = {
  User: 'User',
  OrgUnit: 'OrgUnit',
} as const;
export type GoogleChannelScope = (typeof GoogleChannelScope)[keyof typeof GoogleChannelScope];

export const GoogleUserEventType = {
  Add: 'ADD',
  Delete: 'DELETE',
  MakeAdmin: 'MAKE_ADMIN',
  Update: 'UPDATE',
  Undelete: 'UNDELETE',
} as const;
export type GoogleUserEventTypeOption = (typeof GoogleUserEventType)[keyof typeof GoogleUserEventType];

export const GoogleAuditEventName = {
  OrganizationUnit: 'ORG_SETTINGS',
} as const;
export type GoogleAuditEventNameOption = (typeof GoogleAuditEventName)[keyof typeof GoogleAuditEventName];

export const GoogleOrganizationUnitEventType = {
  Create: 'CREATE_ORG_UNIT',
  ChangeName: 'EDIT_ORG_UNIT_NAME',
  Delete: 'REMOVE_ORG_UNIT',
  Move: 'MOVE_ORG_UNIT',
} as const;
export type GoogleOrganizationUnitEventType =
  (typeof GoogleOrganizationUnitEventType)[keyof typeof GoogleOrganizationUnitEventType];

export const PAGINATION_LIMIT = 10;

export const CuramaUserEventType = {
  Create: 'USER_CREATED',
  Update: 'USER_UPDATED',
  Delete: 'USER_DELETED',
} as const;
export type CuramaUserEventType = (typeof CuramaUserEventType)[keyof typeof CuramaUserEventType];

export const CuramaTeamEventType = {
  Create: 'TEAM_CREATED',
  Update: 'TEAM_UPDATED',
  Delete: 'TEAM_DELETED',
} as const;
export type CuramaTeamEventType = (typeof CuramaTeamEventType)[keyof typeof CuramaTeamEventType];

export const SLEEP_BEFORE_GOOGLE_API_CALL = 2000;
