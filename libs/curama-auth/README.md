## Setup .env file

```bash

# (Required) AWS configuration
# AWS Cognito App Client Domain. Please includes https://
COGNITO_OAUTH2_DOMAIN=
# AWS Cognito App Client ID
COGNITO_OAUTH2_CLIENT_ID=
# AWS Cognito App Client Secret
COGNITO_OAUTH2_CLIENT_SECRET=
# AWS Cognito App Client Scope
COGNITO_OAUTH2_SCOPES=aws.cognito.signin.user.admin,email,openid,phone,profile

# AWS ACCESS KEY (Not need to set if you use aws profile)
AWS_ACCESS_KEY=
# AWS SECRET KEY (Not need to set if you use aws profile)
AWS_SECRET_KEY=
# AWS REGION (Not need to set if you use aws profile)
AWS_REGION=ap-northeast-1

# Verified permission policy store id that get from aws verified permission service
VERIFIED_PERMISSION_POLICY_STORE_ID=
# Verified permission user pool id that related to verified permission policy store
VERIFIED_PERMISSION_USER_POOL_ID=

# (Require) Google app account service to synchronize data from google
# Must be admin email of google workspace
GOOGLE_APP_ACCOUNT_SERVICE_EMAIL=
# Must be domain of google workspace
GOOGLE_APP_ACCOUNT_SERVICE_DOMAIN=
# Must be customer id of google workspace
GOOGLE_APP_ACCOUNT_CUSTOMER_ID=

# If set RUN_INITIAL_COMMAND is true, application will run command curama:init when start.
# The goal of this command is to sync data from google and create verified permission policy template.
# (RUN_INITIAL_COMMAND = true is required for first time deploy) Accepted values: true
RUN_INITIAL_COMMAND=

```

## Config Google Service Account Key to synchronize user from google to local database.

- Download google service account key from google app account service.

## How to handle authentication

We need three functions below to handle authentication with aws cognito using google idp.

```typescript
@Get('login')
@Render('admin/login')
showLoginForm() {
  const loginUrl = this.authService.getOauth2Url();
  return {
    loginUrl,
  };
}

@Get('logout')
@Redirect('/admin/consulting-message/login')
logout(@Req() req: Request) {
  // handle destroy session and revoke aws cognito refresh token
  this.authService.signOut(req);
}

@Get('/admin/consulting-message/authenticate') // url callback defined in aws cognito app client
async oauth2Callback(@Req() req, @Res() res: Response): Promise<any> {
  await this.authService.authenticate(req);
  return res.redirect(req.terrySession?.intendedUrl ?? '/admin');
}
```

## How to use guard in your controller

```typescript
@Get('inquiry-thread/:id')
@UserAuthorization(CuramaResources.InquiryThread, CuramaActions.Edit) // this is the decorator execute guard to check admin policy
@UseGuards(AdminAuthenticatedGuard) // this is guard to check admin is authenticated
async inquiryThread(@SessionAdminInfo() admin, @Param('id') id: string) {
  ...
}
```

## Get session user information via param decorator.

```typescript
@Get('profile')
async profile(@SessionAdminInfo() admin: SessionAdmin) {
  return admin;
}
```

## Would you like to check if the sessionAdmin `[@SessionAdminInfo()]` has permission to access a resource or not?

Currently, the logic is the user only needs at least one action of resource.

```typescript
  Get('/')
  @UseGuards(AdminAuthenticatedGuard)
  async index(@SessionAdminInfo() sessionAdmin: SessionAdmin) {
    const adminCanEditInquiryThread = sessionAdmin.can(CuramaResources.InquiryThread, [CuramaActions.Edit]);
    console.log('adminCanEditInquiryThread', adminCanEditInquiryThread);
    ....
  }
```
