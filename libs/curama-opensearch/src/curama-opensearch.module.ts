import { BeforeApplicationShutdown, DynamicModule, Logger, Module } from '@nestjs/common';
import { ASYNC_OPTIONS_TYPE, ConfigurableModuleClass, OPTIONS_TYPE } from './definition/opensearch.definition';
import { MODULE_OPTIONS_TOKEN } from '@nestjs/common/cache/cache.module-definition';
import { Client } from '@opensearch-project/opensearch';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { OpenSearchConfig } from './types/opensearch-config.type';
import { CuramaOpenSearchClient } from './curama-open-search-client';
import { AwsSigv4Signer } from '@opensearch-project/opensearch/aws';
import { OpenSearchConnectionException } from '@vietnam/curama-opensearch/exceptions/opensearch-connection.exception';

@Module({})
export class CuramaOpensearchModule extends ConfigurableModuleClass implements BeforeApplicationShutdown {
  private logger = new Logger(CuramaOpensearchModule.name);

  constructor(private curamaOpenSearchClient: CuramaOpenSearchClient) {
    super();
  }

  static forRoot(config: typeof OPTIONS_TYPE): DynamicModule {
    return {
      module: CuramaOpensearchModule,
      imports: [],
      exports: [],
      providers: [
        {
          provide: MODULE_OPTIONS_TOKEN,
          useValue: config,
        },
        {
          provide: CuramaOpenSearchClient,
          useFactory: (config: OpenSearchConfig) => CuramaOpensearchModule.createClient(config),
        },
      ],
    };
  }

  static forRootAsync(config: typeof ASYNC_OPTIONS_TYPE): DynamicModule {
    return {
      module: CuramaOpensearchModule,
      imports: [],
      exports: [CuramaOpenSearchClient],
      providers: [
        {
          provide: MODULE_OPTIONS_TOKEN,
          useFactory: config.useFactory,
          inject: config.inject,
        },
        {
          provide: CuramaOpenSearchClient,
          useFactory: async (opensearchConfig: OpenSearchConfig) =>
            await CuramaOpensearchModule.createClient(opensearchConfig),
          inject: [MODULE_OPTIONS_TOKEN],
        },
      ],
      global: true,
    };
  }

  static async createClient(config: OpenSearchConfig): Promise<Client> {
    let openSearchConfig = {
      ...config,
    };

    if (process.env.APP_ENV == 'local') {
      openSearchConfig.ssl = {
        rejectUnauthorized: false,
      };
    }

    if (config.aws && config.aws.region) {
      openSearchConfig = {
        ...config,
        ...AwsSigv4Signer({
          region: config.aws.region,
          service: config.aws.service || 'es',
          getCredentials: () => {
            const credentialsProvider = defaultProvider();
            return credentialsProvider();
          },
        }),
      };
    }
    const logger = new Logger(CuramaOpensearchModule.name);
    const client = new Client(openSearchConfig);
    try {
      await client.ping();
      logger.log('OpenSearch client is connected');
      return client;
    } catch (e) {
      throw new OpenSearchConnectionException('OpenSearch client failed to connect', e.meta);
    }
  }

  async beforeApplicationShutdown(signal?: string): Promise<any> {
    if (this.curamaOpenSearchClient) {
      this.logger.log('Closing OpenSearch client');
      await this.curamaOpenSearchClient.close();
    }
  }
}
