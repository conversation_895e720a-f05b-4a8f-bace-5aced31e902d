import { ClientOptions } from '@opensearch-project/opensearch';

export enum OpenSearchAuthType {
  NONE = 'none',
  BASIC = 'basic',
  AWS = 'aws',
}

export type OpenSearchAwsConfig = {
  region: string;
  service: 'es' | 'aoss';
};

export type OpenSearchConfig = {
  authType: OpenSearchAuthType;
  aws?: OpenSearchAwsConfig;
} & Pick<ClientOptions, 'node' | 'nodes' | 'maxRetries' | 'requestTimeout' | 'auth' | 'ssl'>;
