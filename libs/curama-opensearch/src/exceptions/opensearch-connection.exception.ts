export class OpenSearchConnectionException extends Error {
  private response: any;

  constructor(message: string, response?: any) {
    super(message);
    this.name = 'OpensearchConnectionException';
    this.message = message;
    this.response = response;
    this.stack = `OpensearchConnectionException Server response: body: ${JSON.stringify(response)}; statusCode: ${
      response.statusCode
    }`;
  }
}
