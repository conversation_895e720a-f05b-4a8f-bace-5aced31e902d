import { Injectable } from '@nestjs/common';
import { RPCContext } from '../types/RPCContext.type';

@Injectable()
export class WorkerManager {
  private _runningTask: Map<string, RPCContext>;
  constructor() {
    this._runningTask = new Map();
  }

  addTask(taskId: string, context: RPCContext) {
    if (this._runningTask.has(taskId)) {
      return;
    }

    this._runningTask.set(taskId, context);
  }

  get runningTask(): Map<string, RPCContext> {
    return this._runningTask;
  }

  completedTask(taskId: string) {
    if (this._runningTask.has(taskId)) {
      this._runningTask.delete(taskId);
    }
  }
}
