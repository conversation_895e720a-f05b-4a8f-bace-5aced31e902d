import { Modu<PERSON> } from '@nestjs/common';
import { CuramaWorkerService } from './curama-worker.service';
import { WorkerManager } from './WorkerManager/WorkerManager';
import { CuramaWorkerInterceptor } from './interceptors/CuramaWorker.interceptor';
import { WorkerEventManager } from '@vietnam/curama-worker/WorkerEventManager/WorkerEventManager';
import { DiscoveryModule } from '@golevelup/nestjs-discovery';

@Module({
  providers: [WorkerManager, CuramaWorkerInterceptor, CuramaWorkerService, WorkerEventManager],
  exports: [CuramaWorkerService, WorkerManager, CuramaWorkerInterceptor, WorkerEventManager],
  imports: [DiscoveryModule],
})
export class CuramaWorkerModule {}
