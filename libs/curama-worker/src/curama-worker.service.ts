import { BeforeApplicationShutdown, Inject, Injectable, Logger, OnApplicationShutdown, Scope } from '@nestjs/common';
import { WorkerManager } from './WorkerManager/WorkerManager';

@Injectable({ scope: Scope.DEFAULT })
export class CuramaWorkerService implements BeforeApplicationShutdown, OnApplicationShutdown {
  private logger: Logger = new Logger();
  constructor(@Inject(WorkerManager) private workerManager: WorkerManager) {}

  async beforeApplicationShutdown(signal?: string): Promise<any> {
    const runningTask = this.workerManager.runningTask;
    if (runningTask.size) {
      this.logger.log('Worker has some running processes. Please wait!');
    }

    while (runningTask.size) {
      await this.wait(500);
    }
  }

  private wait(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  onApplicationShutdown(signal?: string): any {
    this.logger.log('System shutdown');
  }
}
