import { DiscoveryService } from '@golevelup/nestjs-discovery';
import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { Logger, OnModuleDestroy } from '@nestjs/common';
import { ExternalContextCreator } from '@nestjs/core/helpers/external-context-creator';
import { RabbitRpcParamsFactory } from '@golevelup/nestjs-rabbitmq/lib/rabbitmq.factory';
import { AmqpConnectionManager } from '@golevelup/nestjs-rabbitmq/lib/amqp/connectionManager';

export class CuramaRabbitMQModule extends RabbitMQModule implements OnModuleDestroy {
  private _connectionManager: AmqpConnectionManager;
  private _logger = new Logger(CuramaRabbitMQModule.name);

  constructor(
    discover: DiscoveryService,
    externalContextCreator: ExternalContextCreator,
    rpcParamsFactory: RabbitRpcParamsFactory,
    connectionManager: AmqpConnectionManager,
  ) {
    super(discover, externalContextCreator, rpcParamsFactory, connectionManager);

    this._connectionManager = connectionManager;
  }

  async onModuleDestroy(): Promise<any> {
    this._logger.log('Gracefully shutdown RabbitMQ worker consumer');
    const connections = this._connectionManager.getConnections();
    if (connections) {
      for (let i = 0; i < connections.length; i++) {
        const connection = connections[i];

        if (!connection || !connection.channel) {
          continue;
        }

        const consumers: Map<string, any> = (connection.channel as any).consumers;
        if (!consumers) {
          this._logger.error('Curama worker consumer is broken. Please check!');
          return;
        }

        if (consumers.size > 0) {
          const consumerTags = Object.keys(consumers);
          await Promise.all(consumerTags.map((tag) => connection.channel.cancel(tag)));
        }
      }
    }
  }
}
