import { CallHandler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { catchError, Observable, tap, throwError } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { WorkerManager } from '../WorkerManager/WorkerManager';
import { RPCContext } from '../types/RPCContext.type';

@Injectable()
export class CuramaWorkerInterceptor implements NestInterceptor {
  constructor(private readonly workerManager: WorkerManager) {}

  intercept(context: ExecutionContext, next: CallHandler<any>): Observable<any> | Promise<Observable<any>> {
    const contextType = context.getType<'http' | 'rmq'>();

    if (contextType !== 'rmq') {
      return next.handle();
    }

    const rpcContext = context.switchToRpc().getContext<RPCContext>();
    const currentTaskId = uuidv4();
    this.workerManager.addTask(currentTaskId, rpcContext);

    return next.handle().pipe(
      tap(() => {
        this.workerManager.completedTask(currentTaskId);
      }),
      catchError((err) => {
        this.workerManager.completedTask(currentTaskId);
        return throwError(err);
      }),
    );
  }
}
