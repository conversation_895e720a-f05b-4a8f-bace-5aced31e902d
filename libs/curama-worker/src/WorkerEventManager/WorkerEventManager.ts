import { Injectable } from '@nestjs/common';
import { DiscoveryService } from '@golevelup/nestjs-discovery';
import { WORKER_EVENTS } from '@vietnam/curama-worker/constant/Worker.const';

@Injectable()
export class WorkerEventManager {
  private eventManager: Map<string, any>;

  constructor(private readonly discoveryService: DiscoveryService) {}

  private addEvent(eventName: string, instance: any) {
    if (!this.eventManager) {
      this.eventManager = new Map<string, any>();
    }
    return this.eventManager.set(eventName, instance);
  }

  public async getAllEvents() {
    const events = await this.discoveryService.providersWithMetaAtKey(WORKER_EVENTS);

    events.map((event) => {
      this.addEvent(event.meta as string, event.discoveredClass.instance);
      return event;
    });

    return this.eventManager;
  }
}
