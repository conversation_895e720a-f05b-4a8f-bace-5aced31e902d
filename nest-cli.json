{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"webpack": false, "assets": [{"include": "../public", "outDir": "dist/public", "watchAssets": true}, {"include": "../views", "outDir": "dist/views", "watchAssets": true}, {"include": "lang/**/*", "watchAssets": true, "outDir": "dist/src"}, {"include": "migrations/csv/*", "watchAssets": true, "outDir": "dist/src"}, {"include": "modules/shared/search/index-mappings/*.json", "outDir": "dist/src", "watchAssets": true}]}, "projects": {"auth": {"type": "library", "root": "libs/auth", "entryFile": "index", "sourceRoot": "libs/auth/src", "compilerOptions": {"tsConfigPath": "libs/auth/tsconfig.lib.json"}}, "curama-opensearch": {"type": "library", "root": "libs/curama-opensearch", "entryFile": "index", "sourceRoot": "libs/curama-opensearch/src", "compilerOptions": {"tsConfigPath": "libs/curama-opensearch/tsconfig.lib.json"}}}}