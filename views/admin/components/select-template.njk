{% macro modalSelectTemplate() %}
  <script>
    function SelectTemplate(props) {
      return {
        $template: '#modal-select-template',
        level: 1,
        subCategory: [],
        templates: [],
        filteredList: [],
        timerId: null,
        isSearchMode: false,
        search(event) {
          this.debounceSearch(event.target.value)()
        },
        clearInput() {
          document.getElementById('search-input').value = '';
        },
        mounted() {
          this.filteredList = props.largeCategories;
          $("#modalSelectTemplate").on('hidden.bs.modal', () => {
            this.level = 1;
            this.subCategory = [],
            this.templates = [],
            this.clearInput();
            this.filteredList = props.largeCategories;
            this.timerId = null;
            this.isSearchMode = false;
          });
        },
        async select(value) {
          this.isLoadingMsg = true;
          this.level = this.level + 1;
          this.clearInput();
          if (this.level === 2) {
            const subCategoryData = await axiosClient.get(`/admin/consulting-message/api/templates/sub-categories`, {
              params: {
                largeCategoryId: value.id
              }
            });
            this.subCategory = subCategoryData
              ?.data
                ?.data ?? [];
            this.filteredList = this.subCategory;
          }
          if (this.level === 3) {
            const templateData = await axiosClient.get(`/admin/consulting-message/api/templates`, {
              params: {
                groupId: value.id
              }
            });
            this.templates = templateData
              ?.data
                ?.data ?? [];;
            this.filteredList = this.templates;
          }
          this.isLoadingMsg = false;
          if (this.level === 4 || this.isSearchMode) {
            props.callback
              ?.(value);
            $('#modalSelectTemplate').modal('hide');
          }
        },
        back() {
          if (this.isSearchMode) {
            this.isSearchMode = false;
          } else {
            this.level = this.level - 1;
          }
          this.clearInput();
          switch (this.level) {
            case 3:
              this.filteredList = this.templates;
              break;
            case 2:
              this.filteredList = this.subCategory;
              break;
            case 1:
              this.filteredList = props.largeCategories;
              break;
            case 0:
              $('#modalSelectTemplate').modal('hide');
          }
        },
        debounceSearch(textSearch) {
          return() => {
            clearTimeout(this.timerId);
            this.timerId = setTimeout(async () => {
              this.isSearchMode = true;
              if (!textSearch) {
                this.back()
                return;
              }
              this.isLoadingMsg = true;
              const templateData = await axiosClient.get(`/admin/consulting-message/api/templates`, {
                params: {
                  search: textSearch
                }
              });
              this.filteredList = templateData
                ?.data
                  ?.data ?? [];
              this.isLoadingMsg = false;
            }, 1000);
          };
        }
      }
    }
  </script>

  {% raw %}
    <template id="modal-select-template" >
      <div id="modalSelectTemplate" class="modal fade" tabindex="-1" role="dialog" @vue:mounted="mounted">
        <div class="modal-dialog">
          <div class="modal-content w-480">
            <ul class="pager pl-15 mb-0" v-if="level !== 1 || isSearchMode">
              <li class="previous action">
                <a @click="back()">
                  <span aria-hidden="true">&larr;</span> 戻る</a>
              </li>
            </ul>
            <div class="modal-body">
              <div class="list-group action">
                <a class="list-group-item content no-border overflow-anywhere text-left" v-for="(data, key) in filteredList" :key="data.id" @click="select(data)">{{data.name}}
                  <span class="glyphicon glyphicon-chevron-right" v-if="level<3 && !isSearchMode"></span></a>
              </div>
              <div class="input-group">
                <span class="input-group-addon no-border-right" id="basic-addon1">
                  <span class="glyphicon glyphicon-search"></span></span>
                <input id="search-input" type="text" class="form-control no-border-left" aria-describedby="basic-addon1" @input="search" placeholder="検索ワードを入力" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}