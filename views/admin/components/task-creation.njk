{% macro modalUpsertTask() %}
  <script>
    function UpsertTask() {
      return {
        $template: '#modal-upsert-task',
        errorStatus: "",
        errorWorkType: "",
        errorTeam: "",
        errorMemo: "",
        id: "",
        workTypeId: "",
        status: "",
        taskNo: "",
        selectedWorkType: "",
        selectedStatus: "",
        selectedTeam: "",
        selectedAdmin: "",
        nextConfirmationDate: "",
        memo: "",
        listStatus: [],
        listAdmin: [],
        listWorkType: [],
        listTeam: [],
        isDisabled: true,
        isLoadingTaskContent: false,
        async getDataOfCreateTask () {
          this.isLoadingTaskContent = true;
          const response = await axiosClient.get('/admin/consulting-message/api/tasks/static-data');

          if (response.status === 200) {
            const data = response.data.data;

            this.listStatus = data.status;
            this.listWorkType = data.workTypes;
            this.listTeam = data.teams;
            this.listAdmin = data.admins;
            if (data.teamId) {
              this.selectedTeam = data.teamId;
              this.selectedAdmin = data.adminId;
            }
          }

          $('.text').text('選択してください');
          $('.clear-confirm-date').hide();
          this.isDisabled = false;
          this.isLoadingTaskContent = false;
        },

        async getDataOfEditTask(taskId) {
          this.isLoadingTaskContent = true;
          const response = await axiosClient.get(`/admin/consulting-message/api/tasks/${taskId}/static-data`);

          if (response.status === 200) {
            const data = response.data;
            this.id = data.task.id;
            this.listStatus = data.status;
            this.listWorkType = data.workTypes;
            this.listTeam = data.teams;
            this.listAdmin = data.admins;
            this.selectedWorkType = data.task.workType.name
            this.selectedStatus = data.task.status;
            this.selectedTeam = data.task.assignmentTeamId;
            this.selectedAdmin = data.task.assignmentAdminId;
            this.nextConfirmationDate = data.task.nextConfirmationDate;
            this.memo = data.task.memo;
            this.taskNo = data.task.taskNo;

            if (this.nextConfirmationDate) {
              $('.text').text(this.nextConfirmationDate)
              $('.clear-confirm-date').show();
              $('.input-datepicker').datepicker('setDate', this.nextConfirmationDate);
            } else {
              $('.text').text('選択してください');
              $('.clear-confirm-date').hide();
            }

            if (this.selectedTeam == null || this.selectedAdmin == null) {
              this.selectedTeam = "";
              this.selectedAdmin = "";
            }

            this.isDisabled = false;
          }
          this.isLoadingTaskContent = false;
        },

        mounted() {
          const self = this;
          $("#modalUpsertTask").on('show.bs.modal', (
            async function (e) {
              let id = $(e.relatedTarget).attr('data-id');
              let taskNo = $(e.relatedTarget).attr('data-taskNo');
              if (id == 0 || id == undefined) {
                await self.getDataOfCreateTask();
              } else {
                self.id = id;
                self.taskNo = taskNo;
                await self.getDataOfEditTask(id);
              }
            }
          ));

          $('.input-datepicker').datepicker({
            format: 'yyyy-mm-dd',
            language: "ja",
            autoclose: true,
            todayHighlight: true,
            zIndexOffset: 1000,
            }).on('changeDate', function (e) {
              var _this = e.currentTarget;
              $(_this).find('input .date-picker').val(e.format('yyyy-mm-dd'));
              $(_this).find('.text').text(e.format('yyyy-mm-dd'));
              self.nextConfirmationDate = e.format('yyyy-mm-dd');
              updateClearConfirmDate($(_this));
            }).on("show", function(e){
              e.preventDefault();
              e.stopPropagation();
            }).on("hide", function(e){
              e.preventDefault();
              e.stopPropagation();
            });

          $('.clear-confirm-date').on('click', function(event){
            event.stopPropagation();
            var _parent = $(this).closest('.input-datepicker');
            _parent.datepicker('clearDates');
            _parent.find('input').val('');
            _parent.find('.text').text('選択してください');
            updateClearConfirmDate(_parent);
          });
          
          function updateClearConfirmDate(_element) {
            var clearButton = _element.find('.clear-confirm-date');
            if(_element.find('input').val()) {
              clearButton.show();
            } else {
              clearButton.hide();
            }
          }

          $("#modalUpsertTask").on('hide.bs.modal', () => {
            this.id = "";
            this.workTypeId = "";
            this.status = "";
            this.nextConfirmationDate = "";
            this.memo = "";
            this.taskNo = "";
            this.errorStatus = "";
            this.errorWorkType = "";
            this.errorMemo = "";
            this.errorTeam = "";
            this.selectedWorkType = "";
            this.selectedStatus = "";
            this.selectedTeam = "";
            this.selectedAdmin = "";
            this.listAdmin = [];
            this.isDisabled = true;
            $('.clear-confirm-date').trigger('click');
          });
        },

        async changeTeam(event) {
          this.errorTeam = "";
          const listAdminData = await axiosClient.get(`/admin/consulting-message/api/admins`, {
            params: {
              teamId: event.target.value
            }
          });
          this.listAdmin = listAdminData
            ?.data
              ?.data ?? [];
          this.selectedAdmin = "";
        },

        resetStatus() {
          this.errorStatus = "";
        },

        resetWorktype() {
          this.errorWorkType = "";
        },

        validateForm() {
          let isValid = true;
          if (!this.selectedStatus) {
            this.errorStatus = "このフィールドは必須です。";
            isValid = false;
          }
          if (!this.selectedWorkType) {
            this.errorWorkType = "このフィールドは必須です。";
            isValid = false;
          }
          if (!this.selectedTeam) {
            this.errorTeam = "このフィールドは必須です。";
            isValid = false;
          }
          if (this.memo.length > 1000) {
            this.errorMemo = "1000文字以内のメモを書いてください。";
            isValid = false;
          }
          
          return isValid;
        },

        async upsertTask() {
          if (!this.validateForm()) {
            return;
          }
          const data = {
            id: this.id,
            workTypeId: this.selectedWorkType,
            status: this.selectedStatus,
            assignmentTeamId: this.selectedTeam,
            assignmentAdminId: this.selectedAdmin,
            nextConfirmationDate: this.nextConfirmationDate,
            memo: this.memo.trim(),
            storeId: this.store.id,
            storeName: this.store.name,
            threadType: this.threadType,
          };

          const response = await axiosClient.post('/admin/consulting-message/api/tasks', data);
          if (response.status === 201) {
            window.location.reload();
          }

          $('#modalUpsertTask').modal('hide');

          if (response.status === 400) {
            alert(response.message);
          }
        },
      }
    }

  </script>

  {% raw %}
    <template id="modal-upsert-task" >
      <div id="modalUpsertTask" class="modal fade" tabindex="-1" role="dialog" @vue:mounted="mounted" data-backdrop="static">
        <div class="modal-dialog">
          <div class="modal-content p-20" v-cloak>
            <div class="close-modal">
              <span class="glyphicon glyphicon-remove action" data-dismiss="modal"></span>
            </div>
            <h4 class="modal-title">タスク{{taskNo}}を{{id ? '編集' : '作成'}}</h4>
            <form @submit.prevent="upsertTask" id="formUpsertTask">
              <input name= "id" type="hidden" v-model="id"/>
              <div class="modal-body">
                <div class="loading-container" v-show="isLoadingTaskContent">
                  <div class="loader"></div>
                </div>
                <div class="modal-body--content">
                  <div class="form-group row">
                    <label for="workTypeId" class="col-sm-6 col-form-label">業務 (必須)</label>
                    <div class="col-sm-6">
                      <div>
                        <span v-if="id != 0">{{ selectedWorkType }}</span>
                        <select v-else v-model="selectedWorkType" name="workTypeId" class="form-control flex-1 format-text mb-10" @change="resetWorktype">
                          <option value="">選択してください</option>
                          <option v-for="option in listWorkType" v-bind:value="option.id" >{{ option.name }}</option>
                        </select>
                      </div>
                      <span v-if="errorWorkType" class="error">{{ errorWorkType }}</span>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="status" class="col-sm-6 col-form-label">ステータス (必須)</label>
                    <div class="col-sm-6">
                      <select class="form-control flex-1 format-text mb-10" v-model="selectedStatus" @change="resetStatus">
                        <option value="">選択してください</option>
                        <option v-for="option in listStatus" v-bind:value="option.id" >{{ option.name }}</option>
                      </select>
                      <span v-if="errorStatus" class="error">{{ errorStatus }}</span>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="selectedTeam" class="col-sm-6 col-form-label">担当チーム (必須)</label>
                    <div class="col-sm-6">
                      <select class="form-control flex-1 format-text mb-10" v-model="selectedTeam" @change="changeTeam">
                        <option value="">選択してください</option>
                        <option v-for="option in listTeam" v-bind:value="option.id" >{{ option.name }}</option>
                      </select>
                      <span v-if="errorTeam" class="error">{{ errorTeam }}</span>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="selectedAdmin" class="col-sm-6 col-form-label">担当者</label>
                    <div class="col-sm-6">
                      <select class="form-control flex-1 format-text mb-10" v-model="selectedAdmin">
                        <option value="">選択してください</option>
                        <option v-for="option in listAdmin" :key="option.id" v-bind:value="option.id" >{{ option.name }}</option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-6 col-form-label">次回確認日</label>
                    <div class="col-sm-6">
                      <button id="nextConfirmationDate" type="button" class="form-control flex-1 mb-10 action input-datepicker">
                        <span class="text-left text">{{ nextConfirmationDate ? nextConfirmationDate : '選択してください' }}</span>
                        <div class="icons">
                          <span class="glyphicon glyphicon-remove clear-confirm-date" aria-hidden="true"></span>
                          <span class="glyphicon glyphicon-calendar text-right" aria-hidden="true"></span>
                        </div>
                        <input class="date-picker" type="hidden" v-model="nextConfirmationDate">
                      </button>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="memo" class="col-sm-12 col-form-label">メモ</label>
                    <div class="col-sm-12">
                      <textarea class="block-resize flex-1 form-control mb-10" rows="5" v-model="memo" maxlength="1000"></textarea>
                      <span v-if="errorMemo" class="error">{{ errorMemo }}</span>
                    </div>
                  </div>
                </div>
                <div class="gap-20 text-center">
                  <button type="submit" class="btn btn-primary width-25" :disabled="isDisabled">{{ id ? '変更を保存' : 'タスクを作成'}}</button>
                </div>
              </div>
            </form>
          </div>
          </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}