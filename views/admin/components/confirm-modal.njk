{% macro confirmModal() %}
  <script>
    function ConfirmModal({id, content, onConfirm, onCancel}) {

      return {$template: '#confirm-modal-template', id, content, onConfirm, onCancel}
    }
  </script>
  {% raw %}
    <template id="confirm-modal-template">
      <div v-bind:id="id" class="modal fade" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
          <!-- Modal content-->
          <div class="modal-content w-480 p-20">
            <div class="close-modal">
              <span class="glyphicon glyphicon-remove action" @click="onCancel"></span>
            </div>
            <div class="modal-body text-center pt-0">
              <p>{{content}}</p>
            </div>
            <div class="flex-center gap-20">
              <button type="button" class="btn btn-primary btn-delete" @click="onConfirm">OK</button>
              <button type="button" class="btn btn-secondary btn-delete" @click="onCancel">Cancel</button>
            </div>
          </div>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}