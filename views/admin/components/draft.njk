{% macro draftInfo() %}
  <script>
    function DraftInfo(draft) {
      return {
        $template: '#draft-template',
        content: draft.contentEscape,
        attachments: draft.attachments,
        displayStatus: draft.displayStatus,
        adminName: draft.admin
          ?.name,
        updatedAt: draft.updatedAt,
        pdfIcon: '{{ assets.url("assets/img/pdf-icon.png") }}',
        updateDraft() {
          this.isShowDraft = false;
          this.content = draft.content;
        },
        async saveDraftToMessage() {
          this.isLoadingMsg = true;
          const response = await axiosClient.put(`/admin/consulting-message/api/drafts/${this.threadDraft.id}/send`);
          this.isLoadingMsg = false;
          if (response.data.success) {
            this.status = response.data.statusDisplay;
            this.threadStatus = response.data.status;
            this.threadDraft = {};
            this.isShowDraft = false;
            await this.refetchMessage();
          }
        },
        async deleteDraft() {
          this.isLoadingMsg = true;
          const response = await axiosClient.delete(`/admin/consulting-message/api/drafts/${this.threadDraft.id}`);
          this.isLoadingMsg = false;
          if (response.data.success) {
            this.threadDraft = {};
            this.isShowDraft = false;
          }

        }
      }
    };
  </script>
  {% raw %}
    <template id="draft-template">
      <div class="list-group mb-0 mt-10">
        <div class="list-group-item content">
          <div class="font-bold">保存されている下書き</div>
          <div>最終編集：{{adminName}}
            {{dateUtils.formatCreatedAt(updatedAt)}}</div>
        </div>
        <div class="list-group-item msg-admin bg-gray" v-if="content">
          <div v-html="content" class="mb-10 draft-preview"></div>
          <div class="flex">
            <template v-for="(attachment) in attachments" key="attachment.id">
              <a :href="attachment.url" target="_blank">
                <div class="attachment-preview" v-bind:style="{'background-image': attachment.attachmentType === 1 ? 'url('+attachment.urlThumb+')': 'url(' + pdfIcon + ')', 'background-size': 'cover'}"></div>
              </a>
            </template>
          </div>
        </div>
        <div class="list-group-item">
          <div>ステータス：{{displayStatus}}</div>
        </div>
        <div class="list-group-item footer flex-between-center">
          <button type="button" class="btn btn-default" @click="deleteDraft">下書きを削除</button>
          <div>
            <button type="button" class="btn btn-default mr-15" @click="updateDraft">編集</button>
            <button type="button" class="btn btn-primary" @click="saveDraftToMessage">この内容で送信する</button>
          </div>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}