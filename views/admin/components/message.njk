{% macro threadMessage() %}
  <script>
    function MessageBox(msg, store, hasDeletePermission) {
      return {
        $template: '#message-template',
        type: msg.type,
        id: msg.id,
        isShowHeader: msg.type === 1 && !!msg.fromAdminId,
        mainContent: msg.content,
        admin: msg.admin,
        attachments: msg.attachments,
        fromAdminId: msg.fromAdminId,
        deletedAt: msg.deletedAt,
        hasDeletePermission: hasDeletePermission,
        defaultIcon: '{{ assets.url("assets/img/icon_w_curamakun.svg") }}',
        pdfIcon: '{{ assets.url("assets/img/pdf-icon.png") }}',
        async deleteMsg() {
          this.isLoadingMsg = true;
          const response = await axiosClient.delete(`/admin/consulting-message/api/messages/${msg.id}`);
          if (response.data.success) {
            this.mainContent = '（このメッセージは削除されました）';
            this.deletedAt = new Date();
          }
          this.isLoadingMsg = false;
        },
        footerName: msg.fromAdminId || msg.type === 2
          ? msg.admin?.name
          : `${store.name}より`,
      }
    };
  </script>
  {% raw %}
    <template id="message-template">
      <div v-bind:id="['confirmDeleteModal'+id]" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog">
          <div class="modal-content w-480 p-20">
            <div class="close-modal">
              <span class="glyphicon glyphicon-remove action" data-dismiss="modal"></span>
            </div>
            <div class="modal-body text-center pt-0">
              <p>このメッセージを削除しますか？</p>
            </div>
            <div v-html="mainContent" class="mb-20 draft-preview"></div>
            <div class="flex-center gap-20">
              <button type="button" class="btn btn-secondary btn-delete" data-dismiss="modal">キャンセル</button>
              <button type="button" class="btn btn-primary btn-delete" data-dismiss="modal" @click="deleteMsg">削除</button>
            </div>
          </div>
        </div>
      </div>
      <div class="flex" :class="type === 1 && !!!fromAdminId && 'row-reverse'">
        <div class="msg-img" v-if="type === 1" :style="{'background-image': fromAdminId ? 'url(' + defaultIcon + ')': 'url('+avatarShop+')'}" :class="fromAdminId ? 'msg-admin bg-green':'msg-shop'"></div>
        <div class="msg-img" v-if="type === 2"></div>
        <div :class="type === 2 ? 'bg-msg-memo' : fromAdminId ? 'bg-msg-admin':'bg-msg-shop'" class="msg flex-1">
          <div>
            <div v-if="msg && msg.label" class="msg-system">{{ msg.label }}</div>
            <div class="btn-delete-msg" v-if="!deletedAt && hasDeletePermission && isShowHeader" >
              <a class="action" data-toggle="modal" :data-target="'#confirmDeleteModal'+id" data-backdrop="static" data-keyboard="false">削除</a>
            </div>
            <div v-html="mainContent" class="draft-preview"></div>
            <div class="flex mt-10" v-if="!deletedAt">
              <template v-for="(attachment) in attachments" key="attachment.id">
                <a :href="attachment.url" target="_blank">
                  <div class="attachment-preview" v-bind:style="{'background-image': attachment.attachmentType === 1 ? 'url('+attachment.urlThumb+')': 'url(' + pdfIcon + ')', 'background-size': 'cover'}"></div>
                </a>
              </template>
            </div>
          </div>
          <div class="footer">{{footerName}} {{dateUtils.formatCreatedAt(msg.createdAt)}}</div>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}