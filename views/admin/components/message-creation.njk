{% macro messageCreation(canSendMsg = true) %}
  <script>
    function MessageCreation(threadDraft, threadStatus) {
      return {
        $template: '#message-creation-template',
        draftId: threadDraft.id ?? "",
        message: threadDraft.content ?? "",
        attachments: [...(threadDraft.attachments) ?? []],
        currentStatus: threadDraft.status ?? threadStatus ?? "",
        errorContent: "",
        errorNgWord: "",
        errorFile: "",
        errorStatus: "",
        pdfIcon: "{{ assets.url('assets/img/pdf-icon.png') }}",
        callbackSelectTemplate(template) {
          this.message += template.content;
          const msgLength = this.message.trim().length;
          if (msgLength < 10) {
            this.errorContent = '10文字以上のメッセージを書いてください。';
          } else if (msgLength > 2000) {
            this.errorContent = '2000文字以下で入力してください。';
          } else {
            this.errorContent = "";
          }
        },
        removeImg(index) {
          this.errorFile = "";
          this
            .attachments
            .splice(index, 1);
          document.getElementById("file-input").value = "";
          if (!this.message && !this.attachments.length) {
            this.errorContent = "";
          }
        },
        imageCompressor(file) {
          return new Promise((resolve, reject) => {
            new Compressor(file, {
              quality: 0.65,
              success(blob) {
                resolve(blob);
              },
              error() {
                reject("画像の圧縮に失敗しました。\n再度、画像の選択をお願いします。");
              }
            });
          });
        },
        async uploadFile() {
          const SIZE_5MB = 5 * 1000 * 1000;
          const SIZE_25MB = 25 * 1000 * 1000;
          const fileList = [...this.$refs.file.files];
          if (fileList.some(e => !new RegExp(/^(image\/(png|jpe?g)|application\/pdf)$/).exec(e.type))) {
            this.$refs.file.value = null;
            this.errorFile = "jpg、pngまたはpdf以外のファイルが含まれているため、登録できませんでした。";
            return;
          }
          if (fileList.length + this.attachments.length > 5) {
            this.$refs.file.value = null;
            this.errorFile = "添付ファイルの上限数は5ファイルです。（jpg、pngまたはpdf）"
            return;
          }
          for (const [index, file] of fileList.entries()) {
            if (file.size > SIZE_5MB && file.type === 'application/pdf') {
              this.$refs.file.value = null;
              this.errorFile = "サイズが大きすぎるPDFファイルが含まれているため、登録できませんでした。\n 5MB以下のPDFファイルを選択してください。"
              return;
            }
            if (file.type !== 'application/pdf') {
              if (file.size > SIZE_25MB) {
                this.$refs.file.value = null;
                this.errorFile = "サイズが大きすぎる画像が含まれているため、登録できませんでした。\n 25MB以下の画像を選択してください。"
                return;
              }
              if (file.size > SIZE_5MB) {
                const compressedFile = await this.imageCompressor(file)
                if (compressedFile.size > SIZE_5MB) {
                  this.$refs.file.value = null;
                  this.errorFile = "圧縮後の画像サイズが大きすぎる画像が含まれているため、登録できませんでした。"
                  return;
                }
                fileList[index] = compressedFile
              }
            }
          }
          this.errorFile = "";
          this
            .attachments
            .push(...fileList.map(e => ({
              id: "",
              attachmentType: e.type === 'application/pdf'
                ? 2
                : 1,
              url: URL.createObjectURL(e),
              file: e
            })))
        },
        isAttachmentsChanged() {
          if (this.attachments.length !== threadDraft.attachments.length || [...this.$refs.file.files].length) 
            return true;
          return false;
        },
        async upsertDraft() {
          if (!this.message && this.currentStatus === threadStatus) {
            this.errorContent = 'メッセージを入力するか、ステータスの変更をしてください。';
            return;
          }
          if (!this.message && this.attachments.length) {
            this.errorContent = '10文字以上のメッセージを書いてください。';
          }
          if (!this.currentStatus || !['2', '3'].includes(this.currentStatus.toString())) {
            this.errorStatus = "ステータスを選択してください。";
          }
          if (this.errorContent || this.errorNgWord || this.errorStatus) 
            return;
          if (threadDraft.id && JSON.stringify({content: threadDraft.content, status: threadDraft.status}) === JSON.stringify({
            content: this.message,
            status: Number(this.currentStatus)
          }) && !this.isAttachmentsChanged()) {
            this.isShowDraft = true;
            return;
          }
          this.isLoadingMsg = true;
          const formData = new FormData();
          const newFiles = this
            .attachments
            .filter(e => !e.id);
          newFiles.forEach((file, index) => {
            formData.append(`files`, file.file);
          })
          formData.append(
            'content', this.message
            ?.trim());
          formData.append('threadType', this.threadType);
          formData.append('draftId', this.draftId);
          formData.append('storeId', this.store.id);
          formData.append('storeName', this.store.name);
          formData.append('status', this.currentStatus);
          formData.append('uploadedFile', JSON.stringify(this.attachments.filter(e => !!e.id)));
          const response = await axiosClient.post(`/admin/consulting-message/api/drafts`, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          });
          this.isLoadingMsg = false;
          if (!response.data.success && response.data.errors === "NG_WORD_ERROR") {
            this.errorNgWord = `NGワード${response
              .data
              .meta
              .join(', ')}が含まれているため保存できませんでした。`;
            return
          }
          if (response.data.success) {
            this.threadDraft = {
              ...response.data.draft
            };
            this.threadId = response.data.threadId;
            this.isShowDraft = true;
          }
        },
        handleChangeStatus(event) {
          if (['2', '3'].includes(event.target.value)) {
            this.errorStatus = ""
          } else {
            this.errorStatus = "ステータスを選択してください。";
          }
          if (event.target.value !== threadStatus && !this.message) {
            this.errorContent = '';
          }
        },
        handleChangeContent(e) {
          this.errorNgWord = "";
          this.errorStatus = "";
          this.errorFile = "";
          this.message = e.target.value;
          if (!this.message && !this.attachments.length) {
            this.errorContent = "";
            return;
          }
          const msgLength = this.message.trim().length;
          if (msgLength < 10) {
            this.errorContent = '10文字以上のメッセージを書いてください。';
            return;
          } else if (msgLength > 2000) {
            this.errorContent = '2000文字以下で入力してください。';
            return;
          } else {
            this.errorContent = "";
            return;
          }
        }
      }
    };
  </script>
  {% raw %}
    <template id="message-creation-template">
      <div v-scope="SelectTemplate({largeCategories, callback: callbackSelectTemplate})"></div>
      <div v-if="canSendMsg" class="list-group mb-0 mt-10">
        <div class="list-group-item panel-body">
          <textarea class="hide-textarea mb-10" rows="10" id="comment" :value="message" placeholder="店舗へのメッセージを入力（任意）" @input="handleChangeContent"></textarea>
          <div class="flex">
            <div v-for="(attachment, index) in attachments" :key="index" class="attachment-preview" v-bind:style="{'position': 'relative', 'background-image': attachment.attachmentType === 1 ? 'url('+attachment.url+')': 'url(' + pdfIcon + ')', 'background-size': 'cover'}">
              <div class="remove-attachment" @click="removeImg(index)">
                <span class="glyphicon glyphicon-remove"></span>
              </div>
            </div>
          </div>
          <div class="flex">
            <button type="button" class="btn action-msg" @click="document.getElementById('file-input').click()">
              <span>ファイルを添付</span>
              <span class="glyphicon glyphicon-paperclip top-2"></span>
              <input type="file" class="form-control hidden" id="file-input" @change="uploadFile" ref="file" multiple accept="application/pdf,image/png,image/jpeg"/>
            </button>
            <button type="button" class="btn action-msg" data-toggle="modal" data-target="#modalSelectTemplate">
              <span class="template-name-text">テンプレートを使う</span>
              <span class="glyphicon glyphicon-file pull-right top-2"></span>
            </button>
          </div>
        </div>
        <div class="list-group-item">
          <select class="form-control flex-1 format-text w-40" v-model="currentStatus" @change="handleChangeStatus">
            <option value="" disabled>ステータスを変更</option>
            <option v-for="option in listStatus" v-bind:value="option.id" :disabled="option.id==='1'? true:false" :hidden="option.id==='1'? true:false">{{ option.name }}</option>
          </select>
        </div>
        <div class="list-group-item flex-between-center gap-10">
          <div v-if="errorContent || errorNgWord || errorFile || errorStatus" class="color-red">
            <div v-if="errorContent">{{errorContent}}</div>
            <div v-html="errorFile" v-if="errorFile"></div>
            <div v-if="errorStatus">{{errorStatus}}</div>
            <div v-if="errorNgWord">{{errorNgWord}}</div>
          </div>
          <div v-else></div>
          <button type="button" class="btn btn-primary" @click="upsertDraft">下書き保存</button>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}