{% macro messagePreview(message, attachmentFiles, label) %}
    <div class="flex">
        <div class="msg-img msg-admin bg-green" style="background-image: url('{{ assets.url('/assets/img/icon_w_curamakun.svg') }}');"></div>
        <div class="msg flex-1 bg-msg-admin">
            <div>
                {% if label %}
                    <div class="msg-system">{{ label }}</div>
                {% endif %}
                <div class="draft-preview">{{ message | safe }}</div>
                <div class="flex mt-10">
                    {% for attachment in attachmentFiles %}
                        {% set backgroundImage = "" %}
                        {% if attachment.attachmentType === 1 %}
                            {% set backgroundImage = attachment.url %}
                        {% elseif attachment.attachmentType === 2 %}
                            {% set backgroundImage = assets.url('assets/img/pdf-icon.png') %}
                        {% endif %}
                        <a target="_blank" href="{{ attachment.url }}">
                            <div class="attachment-preview" style="background-image: url('{{ backgroundImage }}'); background-size: cover;"></div>
                        </a>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
{% endmacro %}