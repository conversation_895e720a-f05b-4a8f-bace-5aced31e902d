{% macro taskInfo(canSendMsg = true) %}
  <script>
    function TaskInfo(task) {
      isShowTaskUpsert = false;
      return {
        $template: '#task-info-template',
        id: task.id,
        taskNo: task.taskNo,
        workType: task.workType || {},
        status: task.status,
        displayStatus: task.displayStatus,
        team: task.assignmentTeam || {},
        admin: task.assignmentAdmin || {},
        nextConfirmDate: task.nextConfirmationDate,
        memo: task.memo,

        async showModalEdit() {
          this.isShowTaskUpsert = true;
        },

        redirectActivityDetails() {
          window.open(`/admin/activity/detail/${task.activityId}`);
        }
      }
    };

  </script>
  {% raw %}
    <template id="task-info-template">
      <div v-bind:class="status === 5 ? 'task-info task-completed' : 'task-info'">
        <div class="task-info-title content">
          <h4>タスク{{taskNo}}</h4>
          <a v-if="canSendMsg" @click="showModalEdit" data-target="#modalUpsertTask" data-toggle="modal" v-bind:data-taskNo="taskNo" v-bind:data-id="id">編集</a>
        </div>
        <div class="task-info-content">
          <p>業務: {{workType.name}}</p>
          <p>ステータス: {{displayStatus}}</p>
          <template v-if="task.workType?.name !== 'AIメッセージパトロール'">
            <p>担当チーム: {{team.name}}</p>
            <p>担当者: {{admin.name}}</p>
            <p>次回確認日: {{dateUtils.formatConfirmedDate(nextConfirmDate)}}</p>
            <p>メモ:</p>
          </template>
          <div v-html="memo" class="mb-10 draft-preview"></div>
          <button
            v-if="task.workType?.name === 'AIメッセージパトロール'" 
            type="button" 
            class="btn btn-default mb-10"
            @click="redirectActivityDetails"
          >
            AI Task
          </button>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}