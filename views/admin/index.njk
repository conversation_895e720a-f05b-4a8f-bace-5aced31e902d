{% extends "layout/admin/_macro.njk" %}
{% from "components/sidebarAdmin/_macro.njk" import cngaSidebarAdmin %}
{% from "components/adminStore/storePageTabs/_macro.njk" import cngaAdminStoreTabs %}

{% block sideBar %}
    {{ cngaSidebarAdmin(dataSidebar, activePage) }}
{% endblock %}
{% block mainContent %}
    {{ cngaAdminStoreTabs(store, activeTab, storeTabs) }}
    {{ threadList | dump | safe  | replace('"', '&quot;') }}
{% endblock %}