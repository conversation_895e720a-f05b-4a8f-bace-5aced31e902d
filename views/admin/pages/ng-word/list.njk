{% extends "layout/admin/_macro.njk" %}
{% from "components/sidebarAdmin/_macro.njk" import cngaSidebarAdmin %}
{% from "components/adminStore/storePageTabs/_macro.njk" import cngaAdminStoreTabs %}
{% from "components/paginators/_macro.njk" import cngaTerryPaginationSimple %}

{% set titlePage = "NG Word" %}
{% block sideBar %}
  {{cngaSidebarAdmin(dataSidebar,activePage)}}
{% endblock %}
{% block stylesheets %}
  <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
  <style>[v-cloak]{display:none}</style>
{% endblock %}
{% block scripts %}
  <script src="{{ assets.url("js/axios.min.js") }}"></script>
  <script src="{{ assets.url("js/petite-vue.js") }}" defer init></script>
  <script src="{{ assets.url("js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}
{% block mainContent %}
  {{ security.renderTemplate("initialData", listNgWord | dump) }}
  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    function NgWord() {
      return {
        listNgWord: initialData.list,
        content: "",
        contentDelete: "",
        reset() {
          this.content = "";
          this.contentDelete = "";
          window.location.href = window.location.pathname;
        },
        trimContent(content) {
          return content.trim();
        },
        async addNgWord(el, content) {
          el.setAttribute('disabled', 'disabled');
          content = this.trimContent(content);
          if (content === '' || content.length == 0) {
            $('.alert-content-empty').show();
            return;
          }
          const result = await axiosClient.post('/admin/consulting-message/api/ng-words', {
            content: content
          });

          if (result.data.status === 200) {
            this.reset();
          }

          el.removeAttribute('disabled');

          if (result.data.status === 400) {
            $('.alert-content-exist').show();
            return;
          }
        },
        async showModalDeleteNgWord(id) {
          this.contentDelete = this.listNgWord.find(item => item.id === id).content;
          $('#confirmDeleteModal').modal('show');
          $('#confirmDeleteModal .btn-delete').data('id', id);
        },
        async deleteNgWord() {
          const id = $('#confirmDeleteModal .btn-delete').data('id');
          await axiosClient.delete('/admin/consulting-message/api/ng-words/' + id);
          this.reset();
        },
      }
    }
  </script>
  {% raw %}
  <div v-scope="NgWord()" v-cloak>
    <h2 class="panel-title mt-10">NGワード設定</h2><br/>
    <div class="row">
      <div class="col-sm-6">
        <input class="form-control" type="text" id="content" v-model="content" maxlength="255" placeholder="登録するワードを入力">
        <div class="alert-content-empty" style="display:none" v-if="content.trim() === ''">
          <span class="text-danger">NGワードを入力してください。</span>
        </div>
        <div class="alert-content-exist" style="display:none" v-if="content !== ''  && content.trim() !== ''">
          <span class="text-danger">NGワードは既に存在しています。</span>
        </div>
      </div>
      <div class="col-sm-6">
        <button type="button" class="btn btn-primary" :disabled="content.trim() == ''" @click="addNgWord($el, content)">登録</button>
      </div>
    </div>
    <div class="panel panel-primary mt-10">
      <div class="panel-heading">
          <h3 class="panel-title">NGワード一覧</h3>
      </div>
      <div class="panel-body">
        <div class="panel-content">
          <div class="row pl-15 pr-15 mt-10">
            <table class="table table-striped borderless">
              <thead>
                <tr>
                  <th>対象ワード</th>
                  <th></th>
                </tr>
              </thead>
              <tbody v-cloak>
                <tr v-for="item in listNgWord" :key="item.id">
                  <td class="width-90 overflow-anywhere space-pre-wrap">{{item.content}}</td>
                  <td class="width-10">
                    <div class="action btn-delete" data-id="{{ item.id }}" @click="showModalDeleteNgWord(item.id)">
                      <a>削除</a>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div v-bind:id="['confirmDeleteModal']" class="modal fade" role="dialog" data-backdrop="static">
      <div class="modal-dialog">
        <div class="modal-content w-480 p-20">
          <div class="close-modal">
            <span class="glyphicon glyphicon-remove action" data-dismiss="modal"></span>
          </div>
          <div class="modal-body text-center pt-0">
            <p>このNGワードを削除しますか?</p>
            <p class="title overflow-break-word space-pre-wrap">{{ contentDelete }}</p>
          </div>
          <div class="flex-center gap-20">
            <button type="button" class="btn btn-default btn-delete" data-dismiss="modal">キャンセル</button>
            <button type="button" class="btn btn-primary btn-delete" data-dismiss="modal" @click="deleteNgWord">削除</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  {% endraw %}
  <div class="text-center">
    {{ cngaTerryPaginationSimple(listNgWord.pagination) }}
  </div>
{% endblock %}