{% extends "layout/admin/_macro.njk" %}
{% from "components/sidebarAdmin/_macro.njk" import cngaSidebarAdmin %}
{% from "components/adminStore/storePageTabs/_macro.njk" import cngaAdminStoreTabs %}
{% from "components/paginators/_macro.njk" import cngaTerryPaginationSimple %}

{% set titlePage = "List task" %}

{% block sideBar %}
    {{ cngaSidebarAdmin(dataSidebar, activePage) }}
{% endblock %}

{% block stylesheets %}
    <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ assets.url("css/admin/bootstrap-datepicker.min.css") }}">
    <style>[v-cloak]{display: none}</style>
{% endblock %}

{% block scripts %}
    <script src="{{ assets.url("js/axios.min.js") }}"></script>
    <script src="{{ assets.url("js/bootstrap-datepicker.min.js") }}"></script>
    <script src="{{ assets.url("js/bootstrap-datepicker.ja.min.js") }}"></script>
    <script src="{{ assets.url("js/petite-vue.js") }}" defer init></script>
    <script src="{{ assets.url("js/dayjs.min.js") }}"></script>
    <script src="{{ assets.url("js/dayjs.ja.js") }}"></script>
    <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
    <script src="{{ assets.url("js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}

{% block mainContent %}
    {{ security.renderTemplate("initialData", data | dump) }}
    {{ security.renderTemplate("errors", errors | dump) }}
    <script>
        const initialData = {{ security.getTemplateContent("initialData") }}
        const errors = {{ security.getTemplateContent("errors") }}
        $(function () {
            $('.datepicker-input').datepicker({
                format: 'yyyy-mm-dd',
                language: "ja",
                autoclose: true,
                todayHighlight: true
            }).on('changeDate', (e) => {
                var _this = e.currentTarget;
                $(_this).find('input .date-picker').val(e.format('yyyy-mm-dd'));
                $(_this).find('.text').text(e.format('yyyy年mm月dd日'));
                updateClearConfirmDate($(_this));
                updateClearUpdatedAt($(_this));
            });

            $('.clear-confirm-date').on('click', function(event){
                event.stopPropagation();
                var _parent = $(this).closest('.datepicker-input');
                _parent.datepicker('clearDates');
                _parent.find('input').val('');
                _parent.find('.text').text('次回確認日');
                updateClearConfirmDate(_parent);
            });

            $('.clear-updated-at').on('click', function(event){
                event.stopPropagation();
                var _parent = $(this).closest('.datepicker-input');
                _parent.datepicker('clearDates');
                _parent.find('input').val('');
                _parent.find('.text').text('更新日');
                updateClearUpdatedAt(_parent);
            });

            updateClearConfirmDate($('#nextConfirmationDate'));
            updateClearUpdatedAt($('#updatedAt'));
        })

        function updateClearConfirmDate(_element) {
            var clearButton = _element.find('.clear-confirm-date');
            if(_element.find('input').val()) {
                clearButton.show();
            } else {
                clearButton.hide();
            }
        }

        function updateClearUpdatedAt(_element) {
            var clearButton = _element.find('.clear-updated-at');
            if(_element.find('input').val()) {
                clearButton.show();
            } else {
                clearButton.hide();
            }
        }

        function formatDateFilter(dateString, defaultVal = '') {
            if (!dateString) return defaultVal;
            const date = new Date(dateString);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${year}年${month}月${day}日`;
        }

        function Task() {
            return {
                listTask: initialData.tasks,
                listStatus: initialData.statuses,
                selectedTeamId: initialData.query?.assignmentTeamId || "",
                listTeam: initialData.teams,
                selectedAdminId: initialData.query?.assignmentAdminId || "",
                listAdmin: initialData.admins,
                listWorkType: initialData.workTypes,
                selectedWorkTypeId: initialData.query?.workTypeId || "",
                searchStoreId: initialData.query?.storeId || "",
                selectedStatus: initialData.query?.status || "",
                searchNextConfirmationDate: initialData.query?.nextConfirmationDate || "",
                displayFilterNextConfirmationDate: formatDateFilter(initialData.query?.nextConfirmationDate, '次回確認日'),
                searchUpdatedAt: initialData.query?.updatedAt || "",
                displayFilterUpdatedAt: formatDateFilter(initialData.query?.updatedAt, '更新日'),
                orderNextConfirmationDate: initialData.query?.orderNextConfirmationDate || "",
                errors: errors,
                async changeTeam(event) {
                    const listAdminData = await axiosClient.get(`/admin/consulting-message/api/admins`, {
                        params: {
                            teamId: event.target.value
                        }
                    });
                    this.listAdmin = listAdminData
                        ?.data
                        ?.data ?? [];
                },
                getThreadTypeKey(threadType) {
                    const threadKeyParams = {
                        1: 'consultant',
                        2: 'quality-management'
                    }; 

                    return threadKeyParams[threadType];
                },
                getThreadTypeDetailUrl(storeId, threadType) {
                    const threadTypeKey = this.getThreadTypeKey(threadType);
                    return `/admin/consulting-message/stores/${storeId}/messages/${threadTypeKey}`;
                }
            }
        }
    </script>
    {% raw %}
        <div v-scope="Task()" v-cloak>
            <div class="panel panel-primary mt-10">
                <div class="panel-heading">
                    <h3 class="panel-title">タスク管理</h3>
                </div>
                <div class="panel-body" v-cloak>
                    <div>
                        <div class="row pl-15 pr-15">
                            <div class="error mb-10" v-if="errors && errors.length > 0">
                                <span v-for="message in errors">{{ message }}</span>
                            </div>
                            <form method="GET" name="taskSearchForm" class="form-inline">
                                <div class="row">
                                    <div class="col-md-3">
                                        <input v-model="searchStoreId" type="text" name="storeId" class="form-control" placeholder="店舗ID">
                                    </div>
                                    <div class="col-md-3">
                                        <select v-model="selectedTeamId" class="form-control" name="assignmentTeamId" @change="changeTeam">
                                            <option value="">チーム</option>
                                            <option v-for="option in listTeam" v-bind:value="option.id">{{ option.name }}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select v-model="selectedAdminId" class="form-control" name="assignmentAdminId">
                                            <option value="">担当者</option>
                                            <option v-for="option in listAdmin" v-bind:value="option.id">{{ option.name }}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select v-model="selectedWorkTypeId" class="form-control" name="workTypeId">
                                            <option value="">業務</option>
                                            <option v-for="option in listWorkType" v-bind:value="option.id">{{ option.name }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <select v-model="selectedStatus" class="form-control" name="status">
                                            <option value="">ステータス</option>
                                            <option v-for="option in listStatus" v-bind:value="option.id">{{ option.name }}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button id="nextConfirmationDate" type="button" class="btn btn-default btn-md datepicker-input">
                                            <span class="text">{{ displayFilterNextConfirmationDate }}</span>
                                            <div class="icons">
                                                <span class="glyphicon glyphicon-remove clear-confirm-date" aria-hidden="true"></span>
                                                <span class="glyphicon glyphicon-calendar" aria-hidden="true"></span>
                                            </div>
                                            <input v-model="searchNextConfirmationDate" type="hidden" name="nextConfirmationDate">
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <select v-model="orderNextConfirmationDate" class="form-control" name="orderNextConfirmationDate">
                                            <option value="">次回確認日でソート</option>
                                            <option value="1">次回確認日昇順</option>
                                            <option value="2">次回確認日降順</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button id="updatedAt" type="button" class="btn btn-default btn-md datepicker-input ">
                                            <span class="text">{{ displayFilterUpdatedAt }}</span>
                                            <div class="icons">
                                                <span class="glyphicon glyphicon-remove clear-updated-at" aria-hidden="true"></span>
                                                <span class="glyphicon glyphicon-calendar" aria-hidden="true"></span>
                                            </div>
                                            <input v-model="searchUpdatedAt" type="hidden" name="updatedAt">
                                        </button>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-search">検索</button>
                            </form>
                        </div>

                        <div class="row pl-15 pr-15 mt-10">
                            <table class="table borderless table-striped">
                                <thead>
                                <tr>
                                    <th>店舗名</th>
                                    <th>業務</th>
                                    <th>チーム</th>
                                    <th>担当者</th>
                                    <th>ステータス</th>
                                    <th>次回確認日</th>
                                    <th>更新日</th>
                                    <th></th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="item in listTask">
                                    <td>{{ item.thread.storeName }}</td>
                                    <td>{{ item.workType.name }}</td>
                                    <td>{{ item.assignmentTeam.name }}</td>
                                    <td>{{ item.assignmentAdmin ? item.assignmentAdmin.name : '' }}</td>
                                    <td>{{ item.displayStatus }}</td>
                                    <td>{{ dateUtils.formatConfirmedDate(item.nextConfirmationDate) }}</td>
                                    <td>{{ dateUtils.formatUpdatedAt(item.updatedAt) }}</td>
                                    <td>
                                        <a :href="getThreadTypeDetailUrl(item.thread.storeId, item.thread.threadType)">
                                            開く
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endraw %}

    <div class="text-center">
        {{ cngaTerryPaginationSimple(data.pagination) }}
    </div>
{% endblock %}