{% extends "layout/admin/_macro.njk" %}
{% from "components/sidebarAdmin/_macro.njk" import cngaSidebarAdmin %}
{% from "components/adminStore/storePageTabs/_macro.njk" import cngaAdminStoreTabs %}
{% from "admin/components/message.njk" import threadMessage %}
{% from "admin/components/select-template.njk" import modalSelectTemplate %}
{% from "admin/components/task-info.njk" import taskInfo %}
{% from "admin/components/draft.njk" import draftInfo %}
{% from "admin/components/message-creation.njk" import messageCreation %}
{% from "admin/components/task-creation.njk" import modalUpsertTask %}

{% block sideBar %}
  {{cngaSidebarAdmin(dataSidebar,activePage)}}
{% endblock %}
{% block stylesheets %}
  <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
  <link rel="stylesheet" type="text/css" href="{{ assets.url("css/admin/bootstrap-datepicker.min.css") }}">
  <style>[v-cloak]{display:none}</style>
{% endblock %}
{% block scripts %}
  <script src="{{ assets.url("js/axios.min.js") }}"></script>
  <script src="{{ assets.url("js/compressor.min.js") }}"></script>
  <script src="{{ assets.url("js/bootstrap-datepicker.min.js") }}"></script>
  <script src="{{ assets.url("js/bootstrap-datepicker.ja.min.js") }}"></script>
  <script src="{{ assets.url("js/petite-vue.js") }}"></script>
  <script src="{{ assets.url("js/dayjs.min.js") }}"></script>
  <script src="{{ assets.url("js/dayjs.ja.js") }}"></script>
  <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
  <script src="{{ assets.url("js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}
{% block mainContent %}
  {% set combined = {
    canRelease: canRelease,
    canSendMsg: canSendMsg,
    taskQuantity: taskQuantity,
    threadDraft: threadDraft,
    totalMessage: totalMessage,
    totalTask: totalTask,
    teamName: teamName,
    assignmentAdmin: assignmentAdmin,
    threadDraft: threadDraft,
    threadMessages: threadMessages,
    threadTasks: threadTasks,
    threadId: threadId,
    store: store,
    threadType: threadType,
    status: status,
    threadStatus: threadStatus,
    largeCategories: largeCategories,
    listStatus: listStatus,
    hasDeletePermission: hasDeletePermission,
    avatarShop: static.publicImage(40, 40, store.store_manager_photo_path)
  } %}
  {{ security.renderTemplate("initialData", combined | dump) }}
  {{ cngaAdminStoreTabs(store, activeTab, storeTabs) }}
  {{ threadMessage() }}
  {{ modalSelectTemplate() }}
  {{ taskInfo(canSendMsg) }}
  {{ draftInfo() }}
  {{ messageCreation(canSendMsg) }}
  {{ modalUpsertTask() }}
  {% raw %}
    <div v-scope="StoreThreadDetails()" style="padding-top: 20px" @vue:mounted="mounted" v-cloak>
      <div v-if="!canRelease">
        <h3>この店舗にはメッセージは送れません。</h3>
      </div>
      <div v-else>
        <div v-cloak>
          <div :class="isLoadingMsg ? 'tranparent-block' : ''" class="panel panel-primary message-block">
            <div class="panel-heading">
              <h3 class="panel-title">{{teamName}}</h3>
              <h3 class="panel-title">担当者：{{assignmentAdmin.name ?? ''}}</h3>
              <h3 class="panel-title">ステータス：{{status}}</h3>
            </div>
            <div class="panel-body pr-0">
              <div style="position: relative">
                <div class="loading-mask" v-if="isLoadingMsg">
                  <div class="loader"></div>
                </div>
                <div class="msg-scroll-reverse" id="infinite-list">
                  <div v-for="(msg, key) in threadMessages" :key="msg.id" v-scope="MessageBox(msg, store, hasDeletePermission)"></div>
                </div>
              </div>
              <div v-if="isShowDraft" v-scope="DraftInfo(threadDraft)" class="pr-15"></div>
              <div v-else v-scope="MessageCreation(threadDraft, threadStatus)" class="pr-15"></div>
            </div>
          </div>
          <div :class="isLoadingTask ? 'tranparent-block' : ''" class="task-block">
            <h3 class="mt-0">タスク一覧</h3>
            <div style="text-align: right">
              <div v-if="isShowTaskUpsert" v-scope="UpsertTask()"></div>
              <button v-if="canSendMsg" type="button" class="btn btn-primary" @click="showModalCreateTask" data-toggle="modal" data-target="#modalUpsertTask" data-id="0">
                タスクを作成
              </button>
            </div>
            <div v-if="taskQuantity">
              <input type="checkbox" id="checkbox" name="checkbox" v-model="withCompletedTask" @click="changeFilterTask">
              <label for="checkbox">終了したタスクを表示する</label>
            </div>
            <div style="position: relative">
              <div class="loading-mask" v-if="isLoadingTask">
                <div class="loader"></div>
              </div>
              <div class="task-data" id="infinite-list-task">
                <div v-for="(task) in threadTasks" :key="task.id" v-scope="TaskInfo(task)"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  {% endraw %}
  <script defer>
    const initialData = {{ security.getTemplateContent("initialData") }}
    function StoreThreadDetails(props) {
      return {
        canRelease: initialData.canRelease,
        canSendMsg: initialData.canSendMsg,
        withCompletedTask: false,
        taskQuantity: initialData.taskQuantity,
        isShowDraft: !!initialData.threadDraft.id,
        totalMessage: initialData.totalMessage,
        totalTask: initialData.totalTask,
        teamName: initialData.teamName,
        assignmentAdmin: initialData.assignmentAdmin,
        threadDraft: initialData.threadDraft,
        threadMessages: initialData.threadMessages,
        threadTasks: initialData.threadTasks,
        threadId: initialData.threadId,
        store: initialData.store,
        avatarShop: initialData.avatarShop,
        threadType: initialData.threadType,
        status: initialData.status,
        threadStatus: initialData.threadStatus,
        largeCategories: initialData.largeCategories,
        listStatus: initialData.listStatus,
        hasDeletePermission: initialData.hasDeletePermission,
        showModal: false,
        isLoadingMsg: false,
        isLoadingTask: false,
        wordsError: [],
        isShowTaskUpsert: false,
        async refetchMessage() {
          this.isLoadingMsg = true;
          const response = await axiosClient.get(`/admin/consulting-message/api/messages`, {
            params: {
              threadId: this.threadId,
              limit: 10,
              offset: 0
            }
          });
          if (response.data.success) {
            const [messages, total] = response.data.data;
            this.threadMessages = messages;
            this.totalMessage = total;
          }

          const msgList = document.getElementById('infinite-list');
          msgList && msgList.scrollTo({top: msgList.scrollHeight});
          this.isLoadingMsg = false;
        },
        async loadMoreMessage() {
          this.isLoadingMsg = true;
          const response = await axiosClient.get(`/admin/consulting-message/api/messages`, {
            params: {
              threadId: this.threadId,
              limit: 10,
              offset: this.threadMessages.length
            }
          });
          if (response.data.success) {
            const [messages, total] = response.data.data;
            this
              .threadMessages
              .push(...messages);
            this.totalMessage = total;
          }
          this.isLoadingMsg = false;
        },
        async loadTask(offset, withCompleted) {
          this.isLoadingTask = true;
          const response = await axiosClient.get(`/admin/consulting-message/api/tasks`, {
            params: {
              threadId: this.threadId,
              limit: 10,
              offset,
              withCompleted
            }
          });
          this.isLoadingTask = false;
          if (response.data.success) {
            const [tasks, total] = response.data.data;
            return [tasks, total];
          }
          return [[], 0];
        },
        async changeFilterTask(event) {
          const [tasks, total] = await this.loadTask(0, event.target.checked)
          this.threadTasks = tasks;
          this.totalTask = total;
          const taskList = document.getElementById('infinite-list-task');
          taskList && taskList.scrollTo({top: 0});
        },
        mounted() {
          const msgList = document.getElementById('infinite-list');
          if (msgList) {
            msgList.addEventListener('scroll', async (e) => {
              // +10 px to load more message before scroll to bottom, isLoadingMsg false to prevent multiple request
              if ((this.totalMessage > this.threadMessages.length) && (Math.abs(msgList.scrollTop) + msgList.clientHeight + 10 >= msgList.scrollHeight) && !this.isLoadingMsg) {
                await this.loadMoreMessage()
              }
            })
          }
          const taskList = document.getElementById('infinite-list-task');
          if (taskList) {
            taskList.addEventListener('scroll', async (e) => {
              if ((this.totalTask > this.threadTasks.length) && (taskList.scrollTop + taskList.clientHeight + 10 >= taskList.scrollHeight) && !this.isLoadingTask) {
                const [tasks, total] = await this.loadTask(this.threadTasks.length, this.withCompletedTask)
                this
                  .threadTasks
                  .push(...tasks);
                this.totalTask = total;
              }
            })
          }
        },
        async showModalCreateTask() {
          this.isShowTaskUpsert = true;
        }
      }
    };

    PetiteVue
      .createApp({StoreThreadDetails, TaskInfo, MessageBox, SelectTemplate, UpsertTask})
      .mount();
  </script>
{% endblock %}