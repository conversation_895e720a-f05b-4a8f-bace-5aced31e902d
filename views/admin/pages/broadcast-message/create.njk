{% extends "layout/admin/_macro.njk" %}
{% from "components/sidebarAdmin/_macro.njk" import cngaSidebarAdmin %}
{% from "components/adminStore/storePageTabs/_macro.njk" import cngaAdminStoreTabs %}
{% from "components/paginators/_macro.njk" import cngaTerryPaginationSimple %}

{% set titlePage = "Create/Edit broadcast-message" %}

{% block sideBar %}
    {{ cngaSidebarAdmin(dataSidebar, activePage) }}
{% endblock %}

{% block stylesheets %}
    <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ assets.url("css/admin/bootstrap-datepicker.min.css") }}">
    <style>[v-cloak]{display:none}</style>
{% endblock %}

{% block scripts %}
    <script src="{{ assets.url("js/axios.min.js") }}"></script>
    <script src="{{ assets.url("js/bootstrap-datepicker.min.js") }}"></script>
    <script src="{{ assets.url("js/bootstrap-datepicker.ja.min.js") }}"></script>
    <script src="{{ assets.url("js/petite-vue.js") }}" defer init></script>
    <script src="{{ assets.url("js/dayjs.min.js") }}"></script>
    <script src="{{ assets.url("js/dayjs.ja.js") }}"></script>
    <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
    <script src="{{ assets.url("js/compressor.min.js") }}"></script>
    <script src="{{ assets.url("js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
    <script src="{{ assets.url("js/papaparse.min.js") }}"></script>
{% endblock %}

{% block mainContent %}
    {% set combined = {
        broadcastMessage: broadcastMessage,
        allowEdit: allowEdit
    } %}
    {{ security.renderTemplate("initialData", combined | dump) }}
    {{ security.renderTemplate("errors", errors | dump) }}
    <script>
        const initialData = {{ security.getTemplateContent("initialData") }}
        const errors = {{ security.getTemplateContent("errors") }}
        function CreateOrUpdateMessage() {
            return {
                id: initialData.broadcastMessage
                    ?.id || "",
                title: initialData.broadcastMessage
                    ?.title || "",
                content: initialData.broadcastMessage
                    ?.content || "",
                description: initialData.broadcastMessage
                    ?.description || "",
                status: initialData.broadcastMessage
                    ?.status || 0,
                loading: false,
                selectedAllStores: false,
                allowEdit: initialData.allowEdit,
                titleError: "",
                contentError: "",
                attachmentsFileName: initialData.broadcastMessage
                    ?.csvName || "",
                descriptionError: "",
                largeCategoryError: "",
                subCategoryError: "",
                errorFile: "",
                errorCSV: "",
                importCount: initialData.broadcastMessage
                    ?.canSendStoreCount || 0,
                storeIds: "",
                attachments: [],
                csvAttachments: [],
                storeList: initialData.broadcastMessage
                    ?.storeList || [],
                fileList: [],
                removedFiles: [],
                pdfIcon: "{{ assets.url('assets/img/pdf-icon.png') }}",

                // === Form Validation and Error Handling ===
                trimAll() {
                    this.title = this
                        .title
                        .trim();
                    this.content = this
                        .content
                        .trim();
                    this.description = this
                        .description
                        .trim();
                },
                resetForm() {
                    this.title = "";
                    this.content = "";
                    this.description = "";
                    this.csvAttachments = [];
                    this.fileList = [];
                    this.selectedAllStores = false;
                },
                resetErrorMessages() {
                    this.nameError = "";
                    this.contentError = "";
                    this.titleError = "";
                    this.descriptionError = "";
                },
                validateForm() {
                    let isValid = true;
                    this.resetErrorMessages();

                    const cleanString = (str) => str
                        .replace(/\r\n/g, '\n')
                        .replace(/[\u200B-\u200D\uFEFF]/g, '')
                        .trim();
                    const checkField = (field, maxLength, errorField, requiredMessage, lengthMessage) => {
                        if (this[field].trim() === "") {
                            this[errorField] = requiredMessage;
                            return false;
                        } else if (this[field].trim().length > maxLength) {
                            this[errorField] = lengthMessage;
                            return false;
                        }
                        return true;
                    };

                    this.title = cleanString(this.title);
                    this.content = cleanString(this.content);
                    this.description = cleanString(this.description);

                    isValid &= checkField('title', 200, 'titleError', "このフィールドは必須です。", "200文字以下で入力してください。");
                    isValid &= checkField('description', 200, 'descriptionError', "このフィールドは必須です。", "200文字以下で入力してください。");

                    if (this.content.trim() === "") {
                        this.contentError = "このフィールドは必須です。";
                        isValid = false;
                    } else if (this.content.trim().length > 2000) {
                        this.contentError = "2000文字以下で入力してください。";
                        isValid = false;
                    }

                    return Boolean(isValid);
                },
                // === File Upload Logic ===
                triggerFileInput(refName) {
                    this
                        .$refs[refName]
                        .click();
                },
                async uploadFile() {
                    this.loading = true;
                    this.errorCSV = "";
                    const files = document
                        .getElementById("csv-file-input")
                        .files;
                        // Check if no file is selected
                    if (!files || files.length === 0) {
                        this.loading = false; // Reset loading state
                        return;
                    }
                    if (this.csvAttachments.length > 0 && files[0] === this.csvAttachments[0]) {
                        this.loading = false; // Reset loading state
                        return;
                    }
                    this.attachmentsFileName = "";
                    const formData = new FormData();
                    formData.append('files', files[0]);
                    const fileArray = Array.from(files);
                    if (fileArray.some(file => !/^(text\/csv)$/i.test(file.type))) {
                        document
                            .getElementById("file-input")
                            .value = [];
                        this.errorCSV = "CSVファイルのみアップロードしてください。";
                        this.loading = false;
                        return;
                    }
                    let rowCount = 0;
                    let aborted = false; // Flag to track if parsing was aborted

                    try {
                        const reader = new FileReader();
                        reader.onload = async (e) => {
                            const csvData = e.target.result;
                            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

                            Papa.parse(csvData, {
                                header: true,
                                worker: true,
                                step: function (row, parser) {
                                    const currentRow = row.data;

                                    if (!currentRow || typeof currentRow.id !== 'string') {
                                        return;
                                    }
                                    // Perform your validation or processing on the current row
                                    if (!uuidRegex.test(currentRow.id)) {
                                        // Set aborted flag to true
                                        aborted = true;
                                        parser.abort();
                                    } else {
                                        rowCount++;
                                    }
                                },
                                complete: async (results) => {
                                    if (aborted || rowCount < 1) {
                                        this.errorCSV = 'CSVファイルのデータにエラーがありますので見直し再度アップロードしてください。';
                                        this.loading = false;
                                    }
                                    if (!aborted && rowCount > 0) {
                                        try {
                                            const response = await axiosClient.post(`/admin/consulting-message/api/broadcast-message/count-csv`, formData, {
                                                headers: {
                                                    'Content-Type': 'multipart/form-data'
                                                }
                                            });

                                            if (response && response.data.listStore) {
                                                const responseData = response.data.listStore;
                                                this.importCount = rowCount;

                                                this.storeList = responseData
                                                    .publicAndSuspendedStores
                                                    .map(store => ({id: store.id, name: store.name}));

                                                this.attachmentsFileName = files[0].name;
                                                this.csvAttachments = [files[0]];
                                                this.loading = false;
                                            } else {
                                                console.error('Response data is undefined or null');
                                            }
                                        } catch (error) {
                                            console.error('Error during file upload:', error);
                                        }
                                    }
                                },
                                error: (error) => {
                                    console.error("Error parsing the CSV file", error);
                                }
                            });
                        };

                        reader.readAsText(files[0]);
                    } catch (error) {
                        console.error('File processing error:', error.message);
                    }

                },

                async uploadImageFile() {
                    const SIZE_5MB = 5 * 1000 * 1000;
                    const SIZE_25MB = 25 * 1000 * 1000;
                    this.errorFile = "";
                    const files = document
                        .getElementById("file-input")
                        .files;
                    const fileList = [...files];
                    if (fileList.some(e => !new RegExp(/^(image\/(png|jpe?g)|application\/pdf)$/).exec(e.type))) {
                        document
                            .getElementById("file-input")
                            .value = [];
                        this.errorFile = "jpg、pngまたはpdf以外のファイルが含まれているため、登録できませんでした。";
                        return;
                    }
                    if (fileList.length + this.attachments.length > 5) {
                        document
                            .getElementById("file-input")
                            .value = [];
                        this.errorFile = "添付ファイルの上限数は5ファイルです。（jpg、pngまたはpdf）";
                        return;
                    }
                    for (const [index, file] of fileList.entries()) {
                        if (file.size > SIZE_5MB && file.type === "application/pdf") {
                            document
                                .getElementById("file-input")
                                .value = [];
                            this.errorFile = "サイズが大きすぎるPDFファイルが含まれているため、登録できませんでした。\n 5MB以下のPDFファイルを選択してください。";
                            return;
                        }
                        if (file.type !== "application/pdf") {
                            if (file.size > SIZE_25MB) {
                                document
                                    .getElementById("file-input")
                                    .value = [];
                                this.errorFile = "サイズが大きすぎる画像が含まれているため、登録できませんでした。\n 25MB以下の画像を選択してください。";
                                return;
                            }
                            if (file.size > SIZE_5MB) {
                                const compressedFile = await this.imageCompressor(file);
                                if (compressedFile.size > SIZE_5MB) {
                                    document
                                        .getElementById("file-input")
                                        .value = [];
                                    this.errorFile = "圧縮後の画像サイズが大きすぎる画像が含まれているため、登録できませんでした。";
                                    return;
                                }
                                fileList[index] = compressedFile;
                            }
                        }
                    }
                    this
                        .attachments
                        .push(...fileList.map(e => ({
                            id: "",
                            name: e.name,
                            attachmentType: e.type === "application/pdf"
                                ? 2
                                : 1,
                            url: URL.createObjectURL(e)
                        })));
                    this
                        .fileList
                        .push(...fileList);
                },

                validateFile() {
                    const SIZE_5MB = 5 * 1000 * 1000;
                    const SIZE_25MB = 25 * 1000 * 1000;
                    const files = document
                        .getElementById("file-input")
                        .files;
                    if (files || files.length === 0) {
                        this.errorFile = "";
                        return true;
                    }

                    if (files.length > 5) {
                        this.errorFile = "添付ファイルの上限数は5ファイルです。（jpg、pngまたはpdf）";
                        return false;
                    }
                    for (const file of files) {
                        if (file.size > SIZE_5MB && file.type === "application/pdf") {
                            this.errorFile = "サイズが大きすぎるPDFファイルが含まれているため、登録できませんでした。\n 5MB以下のPDFファイルを選択してください。";
                            return false;
                        }
                        if (file.type !== "application/pdf") {
                            if (file.size > SIZE_25MB) {
                                this.errorFile = "サイズが大きすぎる画像が含まれているため、登録できませんでした。\n 25MB以下の画像を選択してください。";
                                return false;
                            }
                            if (file.size > SIZE_5MB) {
                                this.errorFile = "圧縮後の画像サイズが大きすぎる画像が含まれているため、登録できませんでした。";
                                return false;
                            }
                        }
                    }

                    this.errorFile = "";
                    return true;
                },

                // === API Calls ===
                async createBroadcastMsg() {
                    if (!this.validateForm()) {
                        return;
                    }
                    this.loading = true;
                    this.trimAll();
                    const messageData = new FormData();
                    if (this.id !== "") {
                        messageData.append(
                            "id", this
                            ?.id);
                    }
                    messageData.append("title", this.title);
                    messageData.append("content", this.content);
                    messageData.append("description", this.description);
                    messageData.append("selectAllStores", this.selectedAllStores);
                    if (this.removedFiles.length > 0) {
                        this
                            .removedFiles
                            .forEach(file => {
                                messageData.append("removedFiles[]", file);
                            });
                    }
                    if (this.fileList.length > 0) {
                        for (const file of this.fileList) {
                            messageData.append("attachments", file, file.name);
                        }
                    }
                    if (!this.selectedAllStores && this.csvAttachments.length > 0) {
                        messageData.append("files", this.csvAttachments[0], this.csvAttachments[0].name);
                    }
                    const broadcastMessageUrl = this.id !== ''
                        ? `/admin/consulting-message/api/broadcast-message/${this.id}`
                        : '/admin/consulting-message/api/broadcast-message/';
                    return axiosClient({
                        method: "post",
                        url: broadcastMessageUrl,
                        data: messageData,
                        headers: {
                            "Content-Type": "multipart/form-data"
                        }
                    })
                        .then(result => {
                            if (result.data.success) {
                                const id = result.data.broadcastMessage
                                    ?.id;
                                sessionStorage.setItem("id", id);
                                this.resetForm();
                                window.location.href = `/admin/consulting-message/broadcast-message/${id}/preview`;
                            } else {
                                this.loading = false;
                            }
                        })
                        .catch(e => {
                            alert(e);
                        });
                },
                // === Utility Functions ===
                imageCompressor(file) {
                    return new Promise((resolve, reject) => {
                        new Compressor(file, {
                            quality: 0.65,
                            success(blob) {
                                resolve(blob);
                            },
                            error() {
                                reject("画像の圧縮に失敗しました。\n再度、画像の選択をお願いします。");
                            }
                        });
                    });
                },
                removeImg(index) {
                    const removedAttachment = this.attachments[index];
                    if (removedAttachment.id !== '') {
                        this
                            .removedFiles
                            .push(removedAttachment.id); // Add to removed files array
                    }
                    this
                        .attachments
                        .splice(index, 1); // Remove from the attachments array
                    this
                        .fileList
                        .splice(index, 1); // Remove from the attachments array
                },
                // === Computed Properties ===
                get isDisabledSendButton() {
                    return !this.title || !this.content || !this.description || !this.selectedAllStores && !this.attachmentsFileName
                },
                get isDisplay() {
                    return !this.allowEdit && this.id === '' || this.allowEdit && this.id !== '';
                },
                // Load existing attachments when editing
                loadExistingAttachments() {
                    if (
                        initialData.broadcastMessage
                        ?.attachments && initialData.broadcastMessage
                            ?.attachments.length > 0) {
                        this.attachments = initialData
                            .broadcastMessage
                            .attachments
                            .map(attachment => ({
                                id: attachment.id,
                                name: attachment.name,
                                attachmentType: attachment.attachmentType,
                                url: attachment.url,
                                urlThumb: attachment.urlThumb || attachment.url
                            }));
                    }
                },
                parseCsvName(csvName) {
                    if (csvName.startsWith("No CSV")) {
                        return {hasCsv: false, name: null};
                    }
                    return {hasCsv: true, name: csvName};
                },
                eventLogger(event) {
                    if (event.type === "pageshow") {
                        this.loading = true;
                        const id = sessionStorage.getItem("id");
                        if (id) {
                            const url = new URL(window.location.href);
                            let pathname = url
                                .pathname
                                .replace(/\/create$/, `/${id}`);
                            const newUrl = `${url.origin}${pathname}${url.search}`;
                            sessionStorage.removeItem("id");
                            if (window.location.href !== newUrl) {
                                window
                                    .location
                                    .replace(newUrl);
                            } else {
                                window
                                    .location
                                    .reload();
                            }
                        }
                        this.loading = false;
                    }
                },
                // === Lifecycle Hooks ===
                mounted() {
                    window.addEventListener("pageshow", this.eventLogger);
                    if (this.status === 1) {
                        this.loadExistingAttachments(); // Load existing attachments when editing
                    }
                    if (!this.allowEdit && this.id !== '') {
                        window.location.href = `/admin/consulting-message/broadcast-message/${this.id}/preview/`;
                    }
                    if (this.id !== '') {
                        const resultCSV = this.parseCsvName(this.attachmentsFileName);
                        if (!resultCSV.hasCsv) {
                            this.selectedAllStores = true;
                            this.importCount = 0;
                            this.storeList = [];
                        }
                        this.attachmentsFileName = this
                            .parseCsvName(this.attachmentsFileName)
                            .name
                    }
                    this.loadExistingAttachments(); // Load existing attachments when editing
                },
                beforeDestroy() {
                    window.removeEventListener("popstate", this.eventLogger);
                    window.removeEventListener("pageshow", this.eventLogger);
                }
            };
        }
    </script>
    {% raw %}
        <div class="container-broadcast-message" v-scope="CreateOrUpdateMessage()" @vue:mounted="mounted" v-cloak>
            <div class="loading-wrap" v-if="loading || (!allowEdit && id !== '')">
                <div class="loader"></div>
            </div>
            <h2 class="mt-10">
                <strong>{{ id ? 'テンプレートの編集' : 'コンサルメッセージの一斉送信' }}</strong>
            </h2><br/>
            <div class="panel">
                <form @submit.prevent="createBroadcastMsg">
                    <input type="hidden" v-model="id">
                    <div class="modal-body">
                        <!-- Title Input -->
                        <div class="form-group-item row">
                            <textarea class="hide-textarea mb-10" rows="1" id="title" v-model="title" placeholder="タイトルを入力 (この内容は送信されません)"></textarea>
                            <span v-if="titleError" class="error">{{ titleError }}</span>
                        </div>
                        <div class="form-group-item row">
                            <textarea class="hide-textarea mb-10" rows="10" id="content" v-model="content" placeholder="テキストを入力 (2000文字まで)"></textarea>
                            <div class="flex">
                                <div v-for="(attachment, index) in attachments" :key="index" class="attachment-preview" v-bind:style="{'position': 'relative', 'backgroundImage': attachment.attachmentType === 1 ? 'url('+attachment.url+')': 'url(' + pdfIcon + ')', 'background-size': 'cover'}">
                                    <div class="remove-attachment" @click="removeImg(index)">
                                        <span class="glyphicon glyphicon-remove"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex">
                                <button type="button" class="btn action-msg" @click="document.getElementById('file-input').click()">
                                    <span>ファイルを添付</span>
                                    <span class="glyphicon glyphicon-paperclip top-2"></span>
                                    <input id="file-input" ref="file" @change="uploadImageFile" class="hidden" type="file" multiple accept=".jpg,.jpeg,.png,.pdf" name="csvAttachments"/>
                                </button>
                            </div>
                            <span v-if="contentError" class="error">{{ contentError }}</span>
                            <span v-if="errorFile" class="error">{{ errorFile }}</span>
                        </div>

                        <!-- Additional Textarea -->
                        <div class="form-group-item row">
                            <textarea class="hide-textarea mb-10" rows="1" v-model="description" placeholder="送信先詳細を入力 (この内容は送信されません)"></textarea>
                            <span v-if="descriptionError" class="error">{{ descriptionError }}</span>
                        </div>

                        <h5 class="form-group row mt-10">送付先</h5>

                        <!-- Checkbox and File Selection Button -->
                        <div class="form-group group-item row">
                            <input type="checkbox" id="selectedAllStores" v-model="selectedAllStores">
                            <label for="selectedAllStores">新規申込み/審査中/出店中/保留中/停止中の全店舗に送付</label>
                        </div>

                        <div class="form-group group-item row">
                            <button
                                class='csv-button'
                                type="button" 
                                @click="triggerFileInput('csvFile')" 
                                :disabled="selectedAllStores"
                                >
                                    ファイルを選択
                            </button>
                            <input id="csv-file-input" ref="csvFile" @change="uploadFile" type="file"
                                            accept=".csv" name="attachments" style="display: none;"/>
                            <span v-if="errorCSV" class="error">{{ errorCSV }}</span>
                            <span :style="selectedAllStores ? { color: 'gray' } : { color: 'black' }">{{ attachmentsFileName }}</span>
                        </div>

                        <div v-if="storeList.length > 0 && !selectedAllStores" class="form-group group-item row">
                            <label>インポート送付件数: {{ importCount }}件</label>
                        </div>

                        <!-- Store List Table -->
                        <div v-if="storeList.length > 0 && !selectedAllStores" class="form-group store-item row">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>店舗ID</th>
                                        <th>店舗名</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="store in storeList" :key="store.id">
                                        <td>{{ store.id }}</td>
                                        <td>{{ store.name }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Right-aligned Submit Button -->
                        <div style="text-align: right;">
                            <button :disabled="isDisabledSendButton" type="submit" class="btn btn-primary" style="margin-top: 20px;">
                                {{ id ? '完了' : '下書き保存してプレビュー' }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    {% endraw %}
{% endblock %}