{% extends "layout/admin/_macro.njk" %}
{% from "components/sidebarAdmin/_macro.njk" import cngaSidebarAdmin %}
{% from "components/adminStore/storePageTabs/_macro.njk" import cngaAdminStoreTabs %}
{% from "components/paginators/_macro.njk" import cngaTerryPaginationSimple %}

{% set titlePage = "List all bulk messaage" %}

{% block sideBar %}
    {{ cngaSidebarAdmin(dataSidebar, activePage) }}
{% endblock %}

{% block stylesheets %}
    <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ assets.url("css/admin/bootstrap-datepicker.min.css") }}">
    <style>[v-cloak]{display:none}</style>
{% endblock %}
{% block scripts %}
    <script src="{{ assets.url("js/axios.min.js") }}"></script>
    <script src="{{ assets.url("js/bootstrap-datepicker.min.js") }}"></script>
    <script src="{{ assets.url("js/bootstrap-datepicker.ja.min.js") }}"></script>
    <script src="{{ assets.url("js/petite-vue.js") }}" defer init></script>
    <script src="{{ assets.url("js/dayjs.min.js") }}"></script>
    <script src="{{ assets.url("js/dayjs.ja.js") }}"></script>
    <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
    <script src="{{ assets.url("js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}

{% block mainContent %}
    {{ security.renderTemplate("initialData", listBroadcastMessage | dump) }}
    {{ security.renderTemplate("errors", errors | dump) }}
    <script>
        const initialData = {{ security.getTemplateContent("initialData") }}
        const errors = {{ security.getTemplateContent("errors") }}
        function BroadcastMessage() {
            return {
                id: "",
                title: "",
                content: "",
                description: "",
                keySearch: initialData.pagination.request.query
                    ?.search || "",
                status: 0,
                loading: false,
                listMessages: initialData.listBroadcastMessages,
                selectedAllStores: false,
                loadingMessageTemplateModalContent: false,
                titleError: "",
                contentError: "",
                attachmentsFileName: "",
                descriptionError: "",
                largeCategoryError: "",
                subCategoryError: "",
                storeList: "",
                importCount: 0,
                failedCount: 0,
                storeIds: "",
                attachments: [],
                csvAttachments: [],
                fileList: [],
                isDisabledSendButton: false,
                statusMap: {
                    1: '下書き',
                    2: '未承認',
                    3: '承認済み',
                    4: '送信設定完了',
                    5: '送信済み',
                    6: '送信中'
                },
                isShowDelete(status) {
                    return status === 5 || status === 6
                },
                showModalPreview(id, status) {
                    if (status === 5) {
                        return `/admin/consulting-message/broadcast-message/${id}/detail-history/`;
                    } else {
                        return `/admin/consulting-message/broadcast-message/${id}/preview/`;
                    }
                },
                async deleteTemplateMsg() {
                    this.loading = true;
                    try {
                        // Proceed with form submission or other logic
                        const broadcastMessage = await axiosClient.delete('/admin/consulting-message/api/broadcast-message/' + this.id);
                        if (broadcastMessage) {
                            window.location.reload();
                        } 
                    } catch (error) {
                        if (axios.isAxiosError(error)) {
                            console.log(error);
                            if (error.response && error.response.status === 400) { 
                                alert('システムが別の処理を実行中のため、削除できません。');
                                window.location.reload();         
                            }
                        }
                        console.error('Unexpected error:', error);
                    }
                },
                processAndSubmit() {
                    // Check if keySearch is not empty
                    if (this.keySearch.trim() !== '') {
                        // Process keySearch
                        this.keySearch = this.keySearch
                            //.replace(/[\u200B-\u200D\uFEFF]/g, '') // Remove zero-width characters
                            .trim(); // Remove leading/trailing whitespace
                    }
                    // Submit the form programmatically
                    this.$refs.threadSearchForm.submit();
                },
                adjustHeight(event) {
                    const textarea = event.target;
                    textarea.style.height = '34px';
                    textarea.scrollTop = textarea.scrollHeight; // Ensure scrolling shows the last row
                },
                eventLogger(event) {
                    if (event.type === "pageshow") {
                        this.loading = true;
                        const id = sessionStorage.getItem("id");
                        if (id) {
                            sessionStorage.removeItem("id");
                        }
                        this.loading = false;
                    }
                },
                mounted() {
                    const events = ["pageshow"];
                    events.forEach((eventName) => window.addEventListener(eventName, this.eventLogger));
                }
            }
        }
    </script>

    {% raw %}
        <div class="container-thread-data" v-scope="BroadcastMessage()" @vue:mounted="mounted" v-cloak>
            <div class="loading-wrap" v-if="loading">
                <div class="loader"></div>
            </div>
            <h2 class="panel-title mt-10">一斉送信管理</h2><br/>
            <a class="btn btn-primary" href="/admin/consulting-message/broadcast-message/create">
                新規送信作成
            </a>
            <div class="panel panel-primary mt-10">
                <div class="panel-heading">
                    <h3 class="panel-title">一斉送信メッセージ一覧</h3>
                </div>
                <div class="panel-body">
                    <div class="position-relative">
                        <div class="row pl-15 pr-15">
                            <form method="GET" ref="threadSearchForm" name="threadSearchForm" class="form-inline" @submit.prevent="processAndSubmit">
                                <div class="col-sm-6 row-group">
                                    <textarea 
                                        v-model="keySearch"
                                        v-on:input="adjustHeight"
                                        name="search" 
                                        class="form-control" 
                                        placeholder="検索ワードを入力" 
                                        rows='1' 
                                        style="height: 34px; resize: none; overflow: hidden; line-height: 34px; padding: 0 10px;" 
                                        block-resize>
                                    </textarea>
                                    <button type="submit" class="btn btn-search">検索</button>
                                </div>
                            </form>
                        </div>
                        <div class="row pl-15 pr-15 mt-10">
                            <div class="table-responsive">
                                <table class="threads table borderless table-striped">
                                    <thead>
                                        <tr>
                                            <th>ステータス</th>
                                            <th>送信日時</th>
                                            <th>タイトル</th>
                                            <th>送信先リスト</th>
                                            <th>送信先リスト説明</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="item in listMessages">
                                            <td>{{ statusMap[item.status] }}</td>
                                            <td>{{ dateUtils.formatCreatedAtYear(item.scheduledSendTime) || '-' }}</td>
                                            <td>{{ item.title }}</td>
                                            <td>{{ item.csvName }}</td>
                                            <td>{{ item.description }}</td>
                                            <td style="min-width: 100px">
                                                <a style="margin-right: 20%" :href="showModalPreview(item.id, item.status)">開く</a>
                                                <a :hidden="isShowDelete(item.status)" @click="id = item.id, title = item.title" data-toggle="modal" data-target="#deleteMessageTemplateModal" href='#'>削除</a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="deleteMessageTemplateModal" class="modal fade" role="dialog" data-backdrop="static">
                    <div class="modal-dialog">
                        <div class="modal-content w-480 p-20">
                            <div class="close-modal">
                                <span class="glyphicon glyphicon-remove action" data-dismiss="modal"></span>
                            </div>
                            <div class="modal-body text-center pt-0">
                                <p>この一斉送信を削除しますか？</p>
                                <p class="title overflow-break-word space-pre-wrap">タイトル: {{ title }}</p>
                            </div>
                            <div class="flex-center gap-20">
                                <button type="button" class="btn btn-default btn-delete" data-dismiss="modal">キャンセル</button>
                                <button type="button" class="btn btn-primary btn-delete" data-dismiss="modal" @click="deleteTemplateMsg">削除</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endraw %}

    <div class="text-center">
        {{ cngaTerryPaginationSimple(listBroadcastMessage.pagination) }}
    </div>
{% endblock %}