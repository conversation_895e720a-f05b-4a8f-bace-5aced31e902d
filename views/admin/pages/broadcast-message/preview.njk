{% extends "layout/admin/_macro.njk" %}
{% from "components/sidebarAdmin/_macro.njk" import cngaSidebarAdmin %}
{% from "components/adminStore/storePageTabs/_macro.njk" import cngaAdminStoreTabs %}
{% from "components/paginators/_macro.njk" import cngaTerryPaginationSimple %}
{% from "admin/components/message-preview.njk" import messagePreview %}

{% set titlePage = "Create/Edit broadcast-message" %}

{% block sideBar %}
    {{ cngaSidebarAdmin(dataSidebar, activePage) }}
{% endblock %}

{% block stylesheets %}
    <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ assets.url("css/admin/bootstrap-datepicker.min.css") }}">
    <style>[v-cloak]{display:none}</style>
{% endblock %}
{% block scripts %}
    <script src="{{ assets.url("js/axios.min.js") }}"></script>
    <script src="{{ assets.url("js/bootstrap-datepicker.min.js") }}"></script>
    <script src="{{ assets.url("js/bootstrap-datepicker.ja.min.js") }}"></script>
    <script src="{{ assets.url("js/petite-vue.js") }}" defer init></script>
    <script src="{{ assets.url("js/dayjs.min.js") }}"></script>
    <script src="{{ assets.url("js/dayjs.ja.js") }}"></script>
    <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
    <script src="{{ assets.url("js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}

{% block mainContent %}
    {% set combined = {
        broadcastMessage: broadcastMessage,
        hasApprovePermission: hasApprovePermission,
        adminId: adminId,
        firstTime: firstTime
    } %}
    {{ security.renderTemplate("initialData", combined | dump) }}
    {{ security.renderTemplate("errors", errors | dump) }}
    <!-- Render Nunjucks Macro for Message Preview outside Vue scope -->
    <div id="nunjucksMessagePreviewContainer" style="display:none;">
        {{ messagePreview(broadcastMessage.escapeUrlizeContent, broadcastMessage.attachments, label) | safe }}
    </div>
    <script>
        // Function to format the date (can be reused anywhere)
        function formatDateFilter(dateString, defaultVal = '') {
            if (!dateString) 
                return defaultVal;
            const date = new Date(dateString);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${year}年${month}月${day}日`;
        }
        const initialData = {{ security.getTemplateContent("initialData") }}
        const errors = {{ security.getTemplateContent("errors") }}
        function previewMessageTemplate() {
            return {
                id: initialData.broadcastMessage
                    ?.id || "",
                title: initialData.broadcastMessage
                    ?.title || "",
                content: initialData.broadcastMessage
                    ?.content || "",
                description: initialData.broadcastMessage
                    ?.description || "",
                status: initialData.broadcastMessage
                    ?.status || 0,
                createdBy: initialData.broadcastMessage
                    ?.createdBy || "",
                adminId: initialData
                    ?.adminId || "",
                scheduledSendTime: initialData.broadcastMessage
                    ?.scheduledSendTime || "",
                loading: false,
                loadingCSV: false,
                selectedAllStores: false,
                titleError: "",
                contentError: "",
                attachmentsFileName: "",
                descriptionError: "",
                largeCategoryError: "",
                subCategoryError: "",
                timeError: "",
                importCount: initialData.broadcastMessage
                    ?.canSendStoreCount || 0,
                failedCount: initialData.broadcastMessage
                    ?.cannotSendStoreCount || 0,
                storeIds: "",
                attachments: [],
                csvAttachments: [],
                storeList: initialData.broadcastMessage
                    ?.storeList || [],
                fileList: [],
                isDisabledSendButton: false,
                statusMap: {
                    1: '下書き',
                    2: '未承認',
                    3: '承認済み',
                    4: '送信設定完了',
                    5: '送信済み'
                },
                permission: initialData.hasApprovePermission || false,
                sendingTimeNeeded: initialData.hasApprovePermission
                    ? false
                    : true, // Default based on permission
                scheduleHour: '',
                scheduleMinute: '',
                selectedDate: '',
                firstTime: initialData.firstTime || false,
                csvName: initialData.broadcastMessage
                    ?.csvName || '',

                // Method to submit the form
                async submitMessage() {
                    this.loading = true;
                    this.timeError = '';
                    let data = {
                        sendNow: !this.sendingTimeNeeded, // If sendingTimeNeeded is false, send immediately
                    };

                    if (this.sendingTimeNeeded) {
                        // Get the selected date and time only if scheduling is required
                        const selectedDateTime = this.getSelectedDateTime();
                        if (!selectedDateTime) {
                            console.error('Unable to submit the message. Please select date and time.');
                            return; // Exit if date and time are not selected
                        }
                        data.dateTime = selectedDateTime; // Add the selected date and time to the request payload
                    }

                    try {
                        // Proceed with form submission or other logic
                        const broadcastMessage = await axiosClient.post(`/admin/consulting-message/api/broadcast-message/handle-time/${this.id}`, data);
                        if (broadcastMessage && broadcastMessage.data.success) {
                            window.location.href = `/admin/consulting-message/broadcast-message`;
                        } else {
                            if (broadcastMessage.data.message === 'Cannot set time for message have schedule nearby!') {
                                this.timeError = '別の送信予約時間と近いため、送信日時を変更してください。';
                                this.loading = false;
                            } else {
                                this.timeError = '過去の日時は選択できません、未来の日時を入力してください。';
                                this.loading = false;
                            }
                        }
                    } catch (error) {
                        if (axios.isAxiosError(error)) {
                            if (error.response && error.response.status === 400) { 
                                this.timeError = '他のユーザーが同時に操作しています、しばらくしてから再度お試しください。';
                                this.loading = false;                  
                            }
                        }
                        console.error('Unexpected error:', error);
                    }
                },
                // Initialize the date picker within the component
                initDatePicker() {
                    let today = new Date();
                    let formattedToday = today
                        .toISOString()
                        .split('T')[0];

                    // Set selectedDate to today's date by default
                    this.selectedDate = formattedToday;

                    // Initialize the date picker and set the default date to today
                    $('#scheduleDatePicker')
                        .datepicker({
                            format: 'yyyy-mm-dd',
                            language: 'ja',
                            autoclose: true,
                            todayHighlight: true,
                            startDate: formattedToday,
                            defaultViewDate: {
                                year: today.getFullYear(),
                                month: today.getMonth(),
                                day: today.getDate()
                            }
                        })
                        .on('changeDate', (e) => {
                            // Update the selected date and time options when the date changes
                            this.selectedDate = e.format('yyyy-mm-dd');
                            this.updateAvailableTimeOptions();
                        })
                        .on('input', (e) => {
                            const value = e.target.value.trim();
                            if (!value) {
                                this.selectedDate = null; // Clear the selected date if input is empty
                                this.updateAvailableTimeOptions(); // Update other dependent options
                            }
                        });
                        
                    // Use focusout to reset the input field if it's cleared
                    $('#scheduleDatePicker').on('focusout', (e) => {
                        const inputValue = e.target.value.trim(); // Get the current input value

                        if (!inputValue) {
                            // If the input is empty, reset it to the last valid date
                            $('#scheduleDate').val(this.selectedDate);
                        }
                    });
                    // Set the input field's value to today
                    $('#scheduleDate').val(formattedToday);
                    this.updateAvailableTimeOptions(); // Initial update for today’s date
                },

                // Method to check if the selected date is today
                isTodaySelected() {
                    const today = new Date()
                        .toISOString()
                        .split('T')[0];
                    return this.selectedDate === today;
                },

                // Update available hours and minutes based on the selected date
                updateAvailableTimeOptions() {
                    this.scheduleHour = ''; // Reset hour and minute on date change
                    this.scheduleMinute = '';
                },

                // Dynamically available hours based on the selected date
                get availableHours() {
                    const now = new Date();
                    const currentHour = now.getHours();
                    const currentMinute = now.getMinutes();

                    // Adjust startHour based on current minute and permission
                    let startHour = this.isTodaySelected()
                        ? currentHour
                        : (this.permission ? 0 : 9);

                    if (this.isTodaySelected()) {
                        if (this.permission && currentMinute >= 55) {
                            startHour = currentHour + 1; // Shift start hour for permission case
                        } else if (!this.permission && currentMinute >= 45) {
                            startHour = currentHour + 1; // Shift start hour for no permission case
                        }
                    }

                    const endHour = this.permission
                        ? 24
                        : 20; // End hour based on permission

                    return Array.from({ length: endHour - startHour }, (_, i) => i + startHour);
                },

                // Dynamically available minutes based on the selected date and selected hour
                get availableMinutes() {
                    const now = new Date();
                    const currentMinute = now.getMinutes();
                    const step = this.permission
                        ? 5
                        : 15;

                    // Start minutes from the current time if today and the selected hour matches the current hour
                    const startMinute = (this.isTodaySelected() && this.scheduleHour == now.getHours())
                        ? Math.ceil(currentMinute / step) * step
                        : 0;
                    return Array
                        .from({
                            length: 60 / step
                        }, (_, i) => i * step)
                        .filter(min => min >= startMinute);
                },

                getSelectedDateTime() {
                    const selectedDate = document
                        .getElementById('scheduleDate')
                        .value;
                    const selectedHour = this.scheduleHour !== null && this.scheduleHour !== undefined
                        ? this.scheduleHour.toString() : "";
                    const selectedMinute = this.scheduleMinute !== null && this.scheduleMinute !== undefined
                        ? this
                            .scheduleMinute
                            .toString()
                            .padStart(2, '0') // Ensures that single digits are padded
                        : "";
                    if (selectedDate && selectedHour !== "" && selectedMinute !== "") {
                        const dateTimeString = `${selectedDate}T${selectedHour.padStart(2, '0')}:${selectedMinute.padStart(2, '0')}:00`;
                        const dateObject = new Date(dateTimeString);
                        return !isNaN(dateObject.getTime())
                            ? dateObject.toISOString()
                            : null;
                    }
                    console.error('Date or time values are missing.');
                    return null;
                },
                getTitleName() {
                    const titlePreview = {
                        1: 'プレビュー画面①（作成者以外のプレビュー画面）',
                        2: 'プレビュー画面②（作成者以外のプレビュー画面）',
                        3: 'プレビュー（承認後のプレビュー画面）',
                        4: 'プレビュー（送信設定後のプレビュー画面）',
                        6: 'プレビュー（送信設定後のプレビュー画面）'
                    };
                    if (this.createdBy === this.adminId) {
                        switch (this.status) {
                            case 1:
                                return 'プレビュー画面①（作成者のプレビュー画面）';
                            case 2:
                                return 'プレビュー画面②（作成者のプレビュー画面）';
                            default:
                                return titlePreview[this.status] || '';
                        }
                    }
                    return titlePreview[this.status] || '';
                },
                // Computed property to check if the "送信する" button should be disabled
                get isSubmitDisabled() {
                    // If user selects "今すぐ送信" (immediate send), only the radio button needs to be selected
                    if (!this.sendingTimeNeeded) {
                        return false; // Enable button for "今すぐ送信"
                    }

                    // If user selects "予約送信", ensure date and time are selected
                    if (this.sendingTimeNeeded) {
                        return this.scheduleHour === '' || this.scheduleMinute === '' || this.selectedDate === null; // Check for null or undefined
                    }

                    return true; // Disable if no radio button is selected
                },
                get isCancelDisabled() {
                    // Check if scheduledSendTime exists and is a valid date
                    if (this.scheduledSendTime) {
                        // Create Date object for the scheduledSendTime
                        const scheduledTime = new Date(this.scheduledSendTime);

                        // Get the current date and time
                        const now = new Date();

                        // Return true if scheduledSendTime is in the past (or not valid), false if it's in the future
                        return scheduledTime <= now;
                    }
                    // If there's no scheduledSendTime, disable the cancel button
                    return false;
                },
                get isShowStore() {
                    return this.loadingCSV || this.csvName === 'No CSV'
                },
                async approvedMessage(cancel) {
                    this.loading = true;
                    const data = {
                        status: cancel
                            ? this.status - 1
                            : this.status + 1
                    };
                    const broadcastMessage = await axiosClient.put(`/admin/consulting-message/api/broadcast-message/${this.id}`, data);
                    if (broadcastMessage && broadcastMessage.data.message) {
                        if (this.status === 4) {
                            window.location.href = `/admin/consulting-message/broadcast-message/`;
                            $('#previewMessageTemplateModal').modal('hide');
                        } else {
                            window.location.href = `/admin/consulting-message/broadcast-message/${this.id}/preview`;
                            $('#previewMessageTemplateModal').modal('hide');
                        }
                    } else {
                        console.error('broadcastMessage data is undefined or null');
                    }
                },
                async editMessage() {
                    this.loading = true;
                    window.location.href = `/admin/consulting-message/broadcast-message/${this.id}`;
                },
                async showModalSend() {
                    $('#sendScheduledMessageModal').modal('show');
                },
                async showModalCancel() {
                    $('#cancelMessageTemplateModal').modal('show');
                },
                // Load existing attachments when editing
                loadExistingAttachments() {
                    this.loadingCSV = true;
                    let intervalId = setInterval(async () => {
                        try {
                            const response = await axiosClient.get(`/admin/consulting-message/api/broadcast-message/tracking/${this.id}`);
                            const broadcastMessage = response.data.broadcastMessage;
                            // Check if result array has length greater than 0
                            if (broadcastMessage && broadcastMessage.canSendStoreCount !== null && broadcastMessage.cannotSendStoreCount !== null) {
                                this.importCount = broadcastMessage.canSendStoreCount;
                                this.failedCount = broadcastMessage.cannotSendStoreCount;
                                this.loadingCSV = false;
                                this.scrollToEnd();
                                clearInterval(intervalId); // Stop the interval
                            }
                        } catch (error) {
                            console.error('Error while polling:', error);
                            clearInterval(intervalId); // Stop the interval
                        }
                    }, 3000);
                },
                scrollToEnd() {
                    const submitButton = document.getElementById("store-table");
                    if (submitButton) {
                        submitButton.scrollTop = submitButton.scrollHeight;
                    }
                },
                eventLogger(event) {
                    switch (event.type) {
                        case "pageshow":
                            {
                                if (event.type === "pageshow" && event.persisted) {
                                    this.loading = true;
                                    window
                                        .location
                                        .reload();
                                }
                                break;
                            }
                        default:
                            break;
                    }
                },
                mounted() {
                    this.initDatePicker();
                    // Move content from nunjucks container to message preview container
                    const nunjucksPreview = document
                        .getElementById('nunjucksMessagePreviewContainer')
                        .innerHTML;
                    document
                        .getElementById('messagePreviewContainer')
                        .innerHTML = nunjucksPreview;
                    const events = ["pageshow"];
                    events.forEach((eventName) => window.addEventListener(eventName, this.eventLogger));
                    if (this.status === 1 || this.status === 2) {
                        this.loadExistingAttachments(); // Load existing attachments when editing
                    }
                    if (this.status === 5) {
                        window.location.href = `/admin/consulting-message/broadcast-message/${this.id}/detail-history/`;
                    }
                }
            }
        }
    </script>
    {% raw %}
        <div class="container-thread-data" v-scope="previewMessageTemplate()" @vue:mounted="mounted" v-cloak>
            <div class="loading-wrap" v-if="loading || status === 5">
                <div class="loader"></div>
            </div>
            <h4 class="modal-title" style="margin-bottom: 20px;">{{ getTitleName() }}</h4>
            <div id="concurrentMessage" class="panel-body">
                <div id="messagePreviewContainer"></div>
                <!-- File Details and Store Information -->
                <div>
                    <h5>送付先プレビュー</h5>
                    <p class="store-item" :hidden="isShowStore">{{ csvName }}</p>
                    <p class="store-item" :hidden="loadingCSV">送付件数: <strong>{{ importCount }}件</strong>
                    </p>
                    <p class="store-item" :hidden="isShowStore" style="color: red;">送付不可件数: <span>{{ failedCount }}件</span></p>
                </div>

                <!-- Store List Table -->
                <div class="loading-div-wrap" v-if="loadingCSV">
                    <div class="loader"></div>
                </div>
                <div id="store-table" :hidden="isShowStore" class="form-group store-item row">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>店舗ID</th>
                                <th>店舗名</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="store in storeList" :key="store.id">
                                <td>{{ store.id }}</td>
                                <td>{{ store.name }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="submit-button" id="submit-button" style="margin-bottom: 20px; text-align: right;">
                    <button v-if="status===1 || status===2 && createdBy === adminId" :disabled="createdBy !== adminId || loadingCSV" type="button" class="btn btn-default" style="padding: 10px 40px;" @click="editMessage">編集</button>
                    <button v-if="status===1 || status===2 && createdBy === adminId" :disabled="createdBy !== adminId || status === 2 || loadingCSV || importCount < 1" type="button" 
                        :class="['btn', {'btn-primary': !(createdBy !== adminId || status === 2 || loadingCSV), 'btn-disable': createdBy !== adminId || status === 2 || loadingCSV}]"
                    style="padding: 10px 40px;" @click="approvedMessage(false)">作成完了</button>
                    <button v-if="status===2 && permission === true && createdBy !== adminId" :disabled="importCount < 1" type="button" class="btn btn-primary" style="padding: 10px 40px;" @click="approvedMessage(false)">承認する</button>
                    <button v-if="status===3" type="button" class="btn btn-primary" style="padding: 10px 40px;" @click="showModalSend">送信予約へ進む</button>
                    <button v-if="status===4 || status===6" :disabled="isCancelDisabled" type="button" class="btn btn-primary" style="padding: 10px 40px;" @click="showModalCancel">送信予約を取り消す</button>
                </div>
            </div>
            <!-- Modal for Scheduled Message -->
            <div id="sendScheduledMessageModal" class="modal fade" role="dialog" data-backdrop="static">
                <div class="modal-dialog">
                    <div class="modal-content" style="padding: 20px;">

                        <!-- Close Button -->
                        <div class="close-modal">
                            <span class="glyphicon glyphicon-remove action" data-dismiss="modal"></span>
                        </div>

                        <!-- Modal Title -->
                        <h4 class="modal-title">送信する</h4>
                        <h5 v-if="!permission" class="modal-title" style="margin-top: 5px;">予約送信</h5>

                        <!-- Date Picker and Time Inputs -->
                        <div class="modal-body">
                            <!-- Radio buttons, visible only if permission is false -->
                            <div v-if="permission">
                                <!-- Yes option -->
                                <input type="radio" id="yes" :value="false" v-model="sendingTimeNeeded" required>
                                <label for="yes">今すぐ送信</label>

                                <!-- No option -->
                                <input type="radio" id="no" :value="true" v-model="sendingTimeNeeded" required>
                                <label for="no">予約送信</label>
                            </div>

                            <!-- Automatically set sendingTimeNeeded to false if permission is true -->
                            <div v-if="permission">
                                <input type="hidden" v-model="sendingTimeNeeded" :value="false">
                            </div>

                            <!-- Date Picker, visible if sendingTimeNeeded is true -->
                            <div v-show="sendingTimeNeeded" class="form-group">
                                <label for="scheduleDate">選択してください</label>
                                <div class="input-group date" id="scheduleDatePicker">
                                    <input type="text" class="form-control" id="scheduleDate" placeholder="選択してください">
                                    <span class="input-group-addon">
                                        <i class="glyphicon glyphicon-calendar"></i>
                                    </span>
                                </div>
                            </div>

                            <!-- Time Picker, visible if sendingTimeNeeded is true -->
                            <div v-show="sendingTimeNeeded" class="form-group">
                                <label for="scheduleHour">時間を選択</label>
                                <div class="input-group" style="display: flex; align-items: center;">
                                    <select id="scheduleHour" v-model="scheduleHour" class="form-control" style="width: 100%; max-width: 80px; text-align: center;">
                                        <option value="">時</option>
                                        <option v-for="hour in availableHours" :value="hour">{{ hour }}</option>
                                    </select>
                                    <span style="margin: 0 10px; font-size: 18px;">:</span>
                                    <select v-model="scheduleMinute" class="form-control" style="width: 100%; max-width: 80px; text-align: center;">
                                        <option value="">分</option>
                                        <option v-for="minute in availableMinutes" :value="minute">{{ minute.toString().padStart(1, '0') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="modal-footer">
                            <button @click="submitMessage" type="button" class="btn btn-primary" :disabled="isSubmitDisabled">確定する</button>
                            <span v-if="timeError" class="text-danger d-block">{{ timeError }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div id="cancelMessageTemplateModal" class="modal fade" role="dialog" data-backdrop="static">
                <div class="modal-dialog">
                    <div class="modal-content w-480 p-20">
                        <div class="close-modal">
                            <span class="glyphicon glyphicon-remove action" data-dismiss="modal"></span>
                        </div>
                        <div class="modal-body text-center pt-0">
                            <p>送信を取り消しますか?</p>
                        </div>
                        <div class="flex-center gap-20">
                            <button type="button" class="btn btn-default btn-delete" data-dismiss="modal">キャンセル</button>
                            <button type="button" class="btn btn-primary btn-delete" data-dismiss="modal" @click="approvedMessage(true)" style="min-width: 150px;">送信予約を取り消す</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endraw %}
{% endblock %}