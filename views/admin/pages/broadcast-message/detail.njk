{% extends "layout/admin/_macro.njk" %}
{% from "components/sidebarAdmin/_macro.njk" import cngaSidebarAdmin %}
{% from "components/adminStore/storePageTabs/_macro.njk" import cngaAdminStoreTabs %}
{% from "admin/components/message-preview.njk" import messagePreview %}

{% set titlePage = "送信履歴画面" %}

{% block sideBar %}
    {{ cngaSidebarAdmin(dataSidebar, activePage) }}
{% endblock %}

{% block stylesheets %}
    <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
{% endblock %}

{% block scripts %}
    <script src="{{ assets.url("js/petite-vue.js") }}" defer init></script>
{% endblock %}

{% block mainContent %}
    <div id="concurrentMessage">
        <div class="panel panel-primary mt-10">
            <div class="panel-heading">
                <h3 class="panel-title">送信履歴画面</h3>
            </div>
            <div class="panel-body">
                {{ messagePreview(message, attachmentFiles, label) }}
                <div class="mt-10">
                    <p>
                        <span>送付件数：<span>{{ storeSuccessCount }}</span>件</span>
                        {% if linkDownload %}
                            <span><a href="{{ linkDownload }}" target="_blank" class="btn btn-secondary">ダウンロード</a></span>
                        {% endif %}
                    </p>
                    <p>
                        <span>送付不可件数：<span>{{ storeUnsuccessCount }}</span>件</span>
                    </p>
                    <p>作成者：{{ createdBy }}（{{ createdAt }}）</p>
                    <p>承認者：{{ approvedBy }} {% if approvedBy %}（{{ approvedAt }}）{% endif %}</p>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
