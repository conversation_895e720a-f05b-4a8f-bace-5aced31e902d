{% extends "layout/admin/_macro.njk" %}
{% from "components/sidebarAdmin/_macro.njk" import cngaSidebarAdmin %}
{% from "components/adminStore/storePageTabs/_macro.njk" import cngaAdminStoreTabs %}
{% from "components/paginators/_macro.njk" import cngaTerryPaginationSimple %}
{% from "admin/components/confirm-modal.njk" import confirmModal %}

{% set titlePage = "Message Template" %}
{% block sideBar %}
  {{cngaSidebarAdmin(dataSidebar,activePage)}}
{% endblock %}
{% block stylesheets %}
  <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
  <style>[v-cloak]{display:none}</style>
{% endblock %}
{% block scripts %}
    <script src="{{ assets.url("js/axios.min.js") }}"></script>
    <script src="{{ assets.url("js/petite-vue.js") }}" defer init></script>
    <script src="{{ assets.url("js/dayjs.min.js") }}"></script>
    <script src="{{ assets.url("js/dayjs.ja.js") }}"></script>
    <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
    <script src="{{ assets.url("js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}

{% block mainContent %}
    {{ security.renderTemplate("initialData", listMessageTemplate | dump) }}
    {{ confirmModal() }}
    <script>
        function MessageTemplate() {
            const initialData = {{ security.getTemplateContent("initialData") }}
            const searchParams = new URLSearchParams(window.location.search);
            return {
                listMessageTemplate: initialData.messageTemplate,
                selectedLargeCategoryId: "",
                keySearch: searchParams.get('search') || "",
                async handleSubmit(event) {
                    event.preventDefault();
                    const inputs = {};
                    $('form[name=searchForm]').find(':input').each(function() {
                        if (this.name) {
                            inputs[this.name] = $(this).val();
                        }
                    });
                    const query = new URLSearchParams(inputs);
                    window.location.replace(`/admin/consulting-message/templates?${query.toString()}`);
                },
                id: "",
                name: "",
                subject: "",
                content: "",
                selectedLargeCategory: "",
                listLargeCategory: initialData.listLargeCategory,
                listSubCategory: initialData.listSubCategory,
                filteredListSubCategory: [],
                selectedSubCategory: "",
                nameError: "",
                contentError: "",
                largeCategoryError: "",
                subCategoryError: "",
                loadingMessageTemplateModalContent: false,
                reset() {
                    this.keySearch = "";
                    window.location.href = `/admin/consulting-message/templates`;
                },
                validateForm() {
                    let isValid = true;
                    this.resetErrorMessages();
                    if (this.name.trim() === "") {
                        this.nameError = "このフィールドは必須です。";
                        isValid = false;
                    }
                    if (this.content.trim() === "") {
                        this.contentError = "このフィールドは必須です。";
                        isValid = false;
                    } else if (this.content.length > 2000) {
                        this.contentError = "2000文字以下で入力してください。";
                        isValid = false;
                    }
                    if (!this.selectedLargeCategory) {
                        this.largeCategoryError = "このフィールドは必須です。";
                        isValid = false;
                    }
                    if (!this.selectedSubCategory) {
                        this.subCategoryError = "このフィールドは必須です。";
                        isValid = false;
                    }
                    return isValid;
                },
                resetErrorMessages() {
                    this.nameError = "";
                    this.contentError = "";
                    this.largeCategoryError = "";
                    this.subCategoryError = "";
                },
                trimAll() {
                    this.name = this.name.trim();
                    this.subject = this.subject.trim();
                    this.content = this.content.trim();
                },
                async showModalCreate() {
                    this.id = "";
                    this.name = "";
                    this.content = "";
                    this.selectedLargeCategory = "";
                    this.filteredListSubCategory = [];
                    $('#createMessageTemplateModal').modal('show');
                },

                async showModalEdit(messageTemplateId) {
                    $('#createMessageTemplateModal').modal('show');
                    this.loadingMessageTemplateModalContent = true;
                    const response = await axiosClient.get('/admin/consulting-message/api/templates/' + messageTemplateId);
                    const data = response.data;
                    this.filteredListSubCategory = this.listSubCategory.filter((item) => item.parentId === data.groupParent.parentId);
                    this.id = data.id;
                    this.name = data.name;
                    this.content = data.content;
                    this.selectedLargeCategory = data.groupParent.parentId
                    this.selectedSubCategory = data.groupId;
                    this.loadingMessageTemplateModalContent = false;
                },

                async showModalDelete(id) {
                    this.id = id;
                    this.name = this.listMessageTemplate.find(item => item.id === id).name;
                    $('#deleteMessageTemplateModal').modal('show');
                    $('#deleteMessageTemplateModal .btn-delete').data('id', id);
                },

                async deleteTemplateMsg() {
                    const id = $('#deleteMessageTemplateModal .btn-delete').data('id');
                    await axiosClient.delete('/admin/consulting-message/api/templates/' + id);
                    this.reset();
                },

                async createTemplateMsg() {
                    if (!this.validateForm()) {
                        return;
                    }
                    this.trimAll();
                    const data = {
                        id: this.id,
                        name: this.name,
                        content: this.content,
                        subCategoryId: this.selectedSubCategory,
                    };

                    const response = await axiosClient.post('/admin/consulting-message/api/templates', data);

                    if (response.status === 201) {
                        window.location.replace(`/admin/consulting-message/templates`);
                    }

                    if (response.status === 400) {
                        alert(response.message);
                    }

                    $('#createMessageTemplateModal').modal('hide');
                },

                async changeLargeCategory(event) {
                    this.selectedSubCategory = "";
                    const listSubCategoryData = await axiosClient.get(`/admin/consulting-message/api/templates/sub-categories`, {
                        params: {
                            largeCategoryId: event.target.value
                        }
                    });
                    this.filteredListSubCategory = listSubCategoryData
                        ?.data
                        ?.data ?? [];
                },
            }
        }
    </script>

    {% raw %}
        <div v-scope="MessageTemplate()" v-cloak>
          <h3 class="panel-title mt-10">テンプレート設定</h3><br/>
            <div class="btn btn-primary" @click="showModalCreate()">
                新規テンプレート作成
            </div>
            <div class="panel panel-primary mt-10">
                <div class="panel-heading">
                    <h3 class="panel-title">テンプレート一覧</h3>
                </div>
                <div class="panel-body">
                    <div>
                      <form @submit="handleSubmit" name="searchForm">
                        <div class="row">
                            <div class="col-sm-5">
                                <input v-model="keySearch" type="text" name="search" class="form-control" placeholder="検索ワードを入力" maxlength="255" autocomplete="false">
                            </div>
                            <div class="col-sm-4">
                                <button type="submit" class="btn btn-search">検索</button>
                            </div>
                        </div>
                      </form>
                    </div>
                </div>

                <table class="table table-striped borderless mt-10" style="border-top:none;">
                    <thead>
                        <tr>
                            <th>大区分</th>
                            <th>小区分</th>
                            <th class="width-33">タイトル</th>
                            <th>更新日</th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody v-cloak>
                        <tr v-for="item in listMessageTemplate" :key="item.id">
                            <td>{{ item.groupParent.groupParent.name }}</td>
                            <td>{{ item.groupParent.name }}</td>
                            <td class="overflow-anywhere space-pre-wrap">{{ item.name }}</td>
                            <td>{{ dateUtils.formatUpdatedAt(item.updatedAt) }}</td>
                            <td>
                                <a @click="showModalEdit(item.id)" data-id="item.id">
                                    編集
                                </a>
                            </td>
                            <td>
                                <a @click="showModalDelete(item.id)" data-id="item.id">
                                    削除
                                </a>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div id="createMessageTemplateModal" class="modal fade" role="dialog" data-backdrop="static">
                    <div class="modal-dialog">
                        <div class="modal-content p-20">
                            <div class="close-modal">
                                <span class="glyphicon glyphicon-remove action" data-dismiss="modal" @click="resetErrorMessages"></span>
                            </div>
                            <h4 class="modal-title">{{ id ? 'テンプレートの編集' : '新規テンプレート作成' }}</h4>
                            <div class="loading-container" v-show="loadingMessageTemplateModalContent">
                                <div class="loader"></div>
                            </div>
                            <form @submit.prevent="createTemplateMsg">
                                <input type="hidden" v-model="id"></input>
                                <div class="modal-body">
                                    <div>
                                        <div class="form-group row">
                                            <input class="flex-1 form-control mb-10" type="text" v-model="name" maxlength="255" placeholder="タイトルを入力 (店舗側には表示されません)"></input>
                                            <span v-if="nameError" class="error">{{ nameError }}</span>
                                        </div>
                                        <div class="form-group row">
                                            <textarea class="block-resize flex-1 form-control mb-10" rows="5" v-model="content" maxlength="2000" placeholder="本文を入力"></textarea>
                                            <span v-if="contentError" class="error">{{ contentError }}</span>
                                        </div>
                                        <div class="form-group row">
                                            <select class="glyphicon form-control flex-1 format-text mb-10" v-model="selectedLargeCategory" @change="changeLargeCategory">
                                                <option value="">&#xe117;&#32;大区分を選択</option>
                                                <option v-for="option in listLargeCategory" v-bind:value="option.id" >{{ option.name }}</option>
                                            </select>
                                            <span v-if="largeCategoryError" class="error">{{ largeCategoryError }}</span>
                                        </div>
                                        <div class="form-group row">
                                            <select class="glyphicon form-control flex-1 format-text mb-10" v-model="selectedSubCategory">
                                                <option class="glyphicon" value="">&#xe117;&#32;小区分を選択</option>
                                                <option v-for="option in filteredListSubCategory" :key="option.id" v-bind:value="option.id" >{{ option.name }}</option>
                                            </select>
                                            <span v-if="subCategoryError" class="error">{{ subCategoryError }}</span>
                                        </div>
                                    </div>
                                    <div class="gap-20 text-center">
                                        <button type="submit" class="btn btn-primary width-25">{{ id ? '完了' : '作成' }}</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div id="deleteMessageTemplateModal" class="modal fade" role="dialog" data-backdrop="static">
                    <div class="modal-dialog">
                        <div class="modal-content w-480 p-20">
                            <div class="close-modal">
                            <span class="glyphicon glyphicon-remove action" data-dismiss="modal"></span>
                            </div>
                            <div class="modal-body text-center pt-0">
                                <p>このテンプレートを削除しますか?</p>
                                <p class="title overflow-break-word space-pre-wrap">{{ name }}</p>
                            </div>
                            <div class="flex-center gap-20">
                                <button type="button" class="btn btn-default btn-delete" data-dismiss="modal">キャンセル</button>
                                <button type="button" class="btn btn-primary btn-delete" data-dismiss="modal" @click="deleteTemplateMsg">削除</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endraw %}

    <div class="text-center">
        {{ cngaTerryPaginationSimple(listMessageTemplate.pagination) }}
    </div>
{% endblock %}