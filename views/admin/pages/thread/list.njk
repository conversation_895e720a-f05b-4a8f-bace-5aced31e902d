{% extends "layout/admin/_macro.njk" %}
{% from "components/sidebarAdmin/_macro.njk" import cngaSidebarAdmin %}
{% from "components/adminStore/storePageTabs/_macro.njk" import cngaAdminStoreTabs %}
{% from "components/paginators/_macro.njk" import cngaTerryPaginationSimple %}

{% set titlePage = "List all inquiry thread" %}

{% block sideBar %}
    {{ cngaSidebarAdmin(dataSidebar, activePage) }}
{% endblock %}

{% block stylesheets %}
    <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ assets.url("css/admin/bootstrap-datepicker.min.css") }}">
    <style>[v-cloak]{display:none}</style>
{% endblock %}

{% block scripts %}
    <script src="{{ assets.url("js/axios.min.js") }}"></script>
    <script src="{{ assets.url("js/bootstrap-datepicker.min.js") }}"></script>
    <script src="{{ assets.url("js/bootstrap-datepicker.ja.min.js") }}"></script>
    <script src="{{ assets.url("js/petite-vue.js") }}" defer init></script>
    <script src="{{ assets.url("js/dayjs.min.js") }}"></script>
    <script src="{{ assets.url("js/dayjs.ja.js") }}"></script>
    <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
    <script src="{{ assets.url("js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}

{% block mainContent %}
    {{ security.renderTemplate("initialData", data | dump) }}
    {{ security.renderTemplate("errors", errors | dump) }}
    <script>
        const initialData = {{ security.getTemplateContent("initialData") }}
        const errors = {{ security.getTemplateContent("errors") }}
        $(function () {
            $('.datepicker-input').datepicker({
                format: 'yyyy-mm-dd',
                language: "ja",
                autoclose: true,
                todayHighlight: true
            }).on('changeDate', (e) => {
                var _this = e.currentTarget;
                $(_this).find('input').val(e.format('yyyy-mm-dd'));
                $(_this).find('.text').text(e.format('yyyy年mm月dd日'));
                updateClearButton($(_this));
            });

            $('.clear-date').on('click', function(event){
                event.stopPropagation();
                var _parent = $(this).closest('.datepicker-input');
                _parent.datepicker('clearDates');
                _parent.find('input').val('');
                _parent.find('.text').text('更新日');
                updateClearButton(_parent);
            });

            function updateClearButton(element) {
                var clearButton = element.find('.clear-date');
                if(element.find('input').val()) {
                    clearButton.show();
                } else {
                    clearButton.hide();
                }
            }
            updateClearButton($('.datepicker-input'));
        })

        function InquiryThread() {
            return {
                listThread: initialData.threads,
                listStatus: initialData.statuses,
                selectedTeamId: initialData.query?.assignmentTeamId || "",
                listTeam: initialData.teams,
                selectedAdminId: initialData.query?.assignmentAdminId || "",
                listAdmin: initialData.admins,
                searchStoreId: initialData.query?.storeId || "",
                selectedStatus: initialData.query?.status || "",
                searchNextConfirmationDate: initialData.query.nextConfirmationDate || "",
                searchUpdatedAt: initialData.query?.updatedAt || "",
                displayFilterUpdatedAt: dateUtils.formatDateFilter(initialData.query?.updatedAt, '更新日'),
                keySearch: initialData.query?.search || "",
                errors: errors,
                async changeTeam(event) {
                    const listAdminData = await axiosClient.get(`/admin/consulting-message/api/admins`, {
                        params: {
                            teamId: event.target.value
                        }
                    });
                    this.listAdmin = listAdminData
                        ?.data
                        ?.data ?? [];
                },
                getThreadTypeKey(threadType) {
                    const threadKeyParams = {
                        1: 'consultant',
                        2: 'quality-management'
                    };

                    return threadKeyParams[threadType];
                },
                getThreadTypeDetailUrl(storeId, threadType) {
                    const threadTypeKey = this.getThreadTypeKey(threadType);
                    return `/admin/consulting-message/stores/${storeId}/messages/${threadTypeKey}`;
                }
            }
        }
    </script>

    {% raw %}
        <div class="btn-group pull-right" id="pageThreads">
            <a href="/admin/consulting-message/broadcast-message" class="btn">一斉送信管理</a>
            <a href="/admin/consulting-message/templates" class="btn">テンプレート設定</a>
            <a href="/admin/consulting-message/ng-words" class="btn">NGワード設定</a>
        </div>

        <div class="container-thread-data" v-scope="InquiryThread()" v-cloak>
            <div class="panel panel-primary mt-10">
                <div class="panel-heading">
                    <h3 class="panel-title">メッセージ管理</h3>
                </div>
                <div class="panel-body">
                    <div class="position-relative">
                        <div class="row pl-15 pr-15">
                            <div class="error mb-10" v-if="errors && errors.length > 0">
                                <span v-for="message in errors">{{ message }}</span>
                            </div>
                            <form method="GET" name="threadSearchForm" class="form-inline">
                                <div class="row-group">
                                    <input v-model="searchStoreId" type="text" name="storeId" class="form-control" placeholder="店舗ID">
                                    <select v-model="selectedTeamId" class="form-control" name="assignmentTeamId" @change="changeTeam">
                                        <option value="">チーム</option>
                                        <option v-for="option in listTeam" v-bind:value="option.id">{{ option.name }}</option>
                                    </select>
                                    <select v-model="selectedAdminId" class="form-control" name="assignmentAdminId">
                                        <option value="">担当者</option>
                                        <option v-for="option in listAdmin" v-bind:value="option.id">{{ option.name }}</option>
                                    </select>
                                    <select v-model="selectedStatus" class="form-control" name="status">
                                        <option value="">ステータス</option>
                                        <option v-for="option in listStatus" v-bind:value="option.id">{{ option.name }}</option>
                                    </select>
                                    <button id="updatedAt" type="button" class="btn btn-default btn-md datepicker-input">
                                        <span class="text">{{ displayFilterUpdatedAt }}</span>
                                        <div class="icons">
                                            <span class="glyphicon glyphicon-remove clear-date" aria-hidden="true"></span>
                                            <span class="glyphicon glyphicon-calendar" aria-hidden="true"></span>
                                        </div>
                                        <input v-model="searchUpdatedAt" type="hidden" name="updatedAt">
                                    </button>
                                </div>
                                <div class="row-group">
                                    <input v-model="keySearch" type="text" name="search" class="form-control" placeholder="検索ワードを入力" autocomplete="false">
                                </div>
                                <div class="row-group">
                                    <button type="submit" class="btn btn-search">検索</button>
                                </div>
                            </form>
                        </div>

                        <div class="row pl-15 pr-15 mt-10">
                            <table class="threads table borderless table-striped">
                                <thead>
                                    <tr>
                                        <th>店舗名</th>
                                        <th>チーム</th>
                                        <th>担当者</th>
                                        <th>ステータス</th>
                                        <th>更新日</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="item in listThread">
                                        <td v-html="item.store_name"></td>
                                        <td>{{ item.team_name }}</td>
                                        <td>{{ item.admin_name }}</td>
                                        <td>{{ item.thread_status_name }}</td>
                                        <td>{{ dateUtils.formatUpdatedAt(item.thread_updated_at) }}</td>
                                        <td>
                                            <a :href="getThreadTypeDetailUrl(item.store_id, item.thread_type)">
                                                開く
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endraw %}

    <div class="text-center">
        {{ cngaTerryPaginationSimple(data.pagination) }}
    </div>
{% endblock %}