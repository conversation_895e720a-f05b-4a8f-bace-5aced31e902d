{% extends "layout/admin/_macro.njk" %}
{% from "components/sidebarAdmin/_macro.njk" import cngaSidebarAdmin %}
{% from "components/adminStore/storePageTabs/_macro.njk" import cngaAdminStoreTabs %}
{% from "components/paginators/_macro.njk" import cngaTerryPaginationSimple %}

{% set titlePage = "Permission List" %}
{% block sideBar %}
  {{ cngaSidebarAdmin(dataSidebar, activePage) }}
{% endblock %}
{% block stylesheets %}
  <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet"/>
  <style>.form-input label{font-weight:400}</style>
{% endblock %}
{% block scripts %}
  <script src="{{ assets.url("js/axios.min.js") }}"></script>
  <script src="{{ assets.url("js/petite-vue.js") }}"></script>
  <script src="{{ assets.url("js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}
{% block mainContent %}
  {% set combined = {
    admins: admins,
    policies: policies,
    departmentTemplates: departmentTemplates
  } %}
  {{ security.renderTemplate("initialData", combined | dump) }}
  {% raw %}
    <div class="container-fluid">
      <div v-scope>
        <div class="panel panel-primary mt-10">
          <div class="panel-heading">
            <h3 class="panel-title">ユーザーのポリシー設定</h3>
          </div>
          <div class="panel-body px-4">
            <div class="row">
              <div class="col col-md-4 mb-3">
                <label for="selectAdmin" class="form-label">Admins</label>
                <select class="form-control flex-1 format-text" v-model="selectedAdmin" @change="getAdminPolicies" required>
                  <option value="">メールを選択</option>
                  <option v-for="admin in admins" v-bind:value="admin.id" v-text="`${admin.name} (${admin.email})`"></option>
                </select>
              </div>
            </div>
            <div class="row">
              <div class="col col-md-6">
                <div class="form-group">
                  <label for="selectDepartmentPolicyTemplate" class="form-label">チームによりデフォルトPolicyをチェック</label>
                  <div class="flex">
                    <select
                      id="selectDepartmentPolicyTemplate"
                      class="form-control flex-1 format-text"
                      :disabled="selectedAdmin === ''"
                      v-model="selectedDepartmentTemplate">
                      <option value="">チームを選択</option>
                      <option v-for="template in departmentTemplates" v-bind:value="template.name" v-text="template.name"></option>
                    </select>
                    <button class="btn btn-info" @click="applyPolicies" :disabled="isUpdating || selectedAdmin === '' || isUpdating" type="button">Apply</button>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col col-md-6">
                <div class="form-group">
                  <div class="form-input">
                    <input
                      type="checkbox"
                      :disabled="selectedAdmin === ''"
                      id="checkBox__checkAll"
                      value="true"
                      v-model="isCheckAllPolicies"
                      @change="onCheckAllChange"/>
                    <label for="checkBox__checkAll">すべてチェック</label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="panel-body px-4">
            <div class="row">
              <div class="col-md-12">
                <div class="loading-mask" v-if="isLoadingPolicy">
                  <div class="loader"></div>
                </div>
                <ul class="list-group">
                  <template v-for="(actions, resource) in policies" :key="resource">
                    <li class="list-group-item flex">
                      <label class="form-label" v-text="resource"></label>
                      <div class="form-group flex mb-0">
                        <div class="form-input mr-15" v-for="action in actions">
                          <input
                            type="checkbox"
                            class="checkboxPolicy"
                            v-model="checkedPolicies"
                            :disabled="selectedAdmin === ''"
                            v-bind:id="`checkBox__${resource}_${action}`"
                            v-bind:value="`${resource}_${action}`"/>
                          <label :for="`checkBox__${resource}_${action}`" v-text="action"></span>
                        </div>
                      </div>
                    </div>
                  </template>
              </ul>
            </div>
            <div class="col col-md-6">
              <div class="form-group">
                <button class="btn btn-primary" :disabled="isUpdating || selectedAdmin === ''" @click="updatePolicies" type="button">Update User
                    Policies</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endraw %}
<script>
  const initialData = {{ security.getTemplateContent("initialData") }}
  PetiteVue
    .createApp({
      admins: initialData.admins,
      policies: initialData.policies,
      departmentTemplates: initialData.departmentTemplates,
      checkedPolicies: [],
      selectedAdmin: '',
      selectedDepartmentTemplate: '',
      isCheckAllPolicies: false,
      isUpdating: false,
      isLoadingPolicy: false,
      async getAdminPolicies() {
        this.isLoadingPolicy = true;
        this.checkedPolicies = [];
        if (this.selectedAdmin === '') {
          return;
        }
        const uri = `/admin/consulting-message/api/authorization/${this.selectedAdmin}/policies`;
        const response = await axiosClient.get(uri);
        for (const [resource, actions] of Object.entries(response.data.userPolicies)) {
          for (const action of actions) {
            this
              .checkedPolicies
              .push(`${resource}_${action}`);
          }
        }
        this.isLoadingPolicy = false;
      },
      isChecked(resource, action) {
        return this
          .checkedPolicies
          .includes(`${resource}_${action}`);
      },
      onCheckAllChange() {
        if (this.isCheckAllPolicies) {
          this.checkedPolicies = [];
          for (const [resource, actions] of Object.entries(this.policies)) {
            for (const action of actions) {
              this
                .checkedPolicies
                .push(`${resource}_${action}`);
            }
          }
        } else {
          this.checkedPolicies = [];
        }
      },
      updatePolicies() {
        const uri = `/admin/consulting-message/api/authorization/${this.selectedAdmin}/policies`;
        this.isUpdating = true;
        axiosClient
          .post(uri, {policies: this.checkedPolicies})
          .then((response) => {
            alert(`[SUCCESS]: ${response.data.message}`);
            this.isUpdating = false;
          })
          .catch((error) => {
            alert(`[ERROR]: ${error.response.data.message}`);
            this.isUpdating = false;
          });
      },
      applyPolicies() {
        const template = this
          .departmentTemplates
          .find((template) => template.name === this.selectedDepartmentTemplate);
        const policies = [];
        if (template) {
          for (const [resource, actions] of Object.entries(template.value)) {
            for (const action of actions) {
              policies.push(`${resource}_${action}${resource}`);
            }
          }
        }
        this.checkedPolicies = policies;
      }
    })
    .mount()
</script>
{% endblock %}