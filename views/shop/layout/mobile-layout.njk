{% import "../../../../node_modules/@vietnam/cnga-frontend/dist/views/components/marketing-js-tags/_macro.njk" as marketingJsTags %}
{% import "../../../../node_modules/@vietnam/cnga-frontend/dist/views/components/copyright/_macro.njk" as copyright %}
{% import "../../../../node_modules/@vietnam/cnga-frontend/dist/views/components/icon/_macro.njk" as cngaIcon %}
{% import "../../../../node_modules/@vietnam/cnga-frontend/dist/views/components/security-macros/_macro.njk" as security %}

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="ja" lang="ja">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
        <meta name="author" content="https://www.minma.jp/">
        <meta name="theme-color" content="#0277bd">
        <meta name="format-detection" content="telephone=no">

        <title>{{ titlePage }}{{ " - " if titlePage }}くらしのマーケット 店舗管理システム</title>

        <link rel="index" href="/shop/"/>
        <link rel="icon" href="{{ static.legacy(helpers.url.substituteFavicon({
            productionPath: 'images/shop/favicon.ico',
            kaizenMasterPath: 'images/shop/master-favicon.ico',
            localPath: 'images/favicons/master-favicon.ico'
        })) }}">

        {% if newMaterialize %}
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
        {% endif %}
        <link rel="stylesheet" href="{{ static.css("shop/materialize.css") }}">
        <link rel="stylesheet" href="{{ static.external("fonts/curama-icons/style.css") }}">
        <link rel="stylesheet" href="{{ static.css("shop/smart-app-banner.css") }}">
        {% block styles %}{% endblock %}

        {% if newMaterialize %}
            <script type="text/javascript" src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
        {% else %}
            <script src="{{ static.legacy("shop/common/js/jquery-1.9.1.min.js") }}"></script>
        {% endif %}

        <script src="{{ static.legacy("vendor/object-fit-images/ofi.min.js") }}"></script>
        <script src="{{ static.js("shop/common.js") }}"></script>
        {% block headScript %}{% endblock %}

        {% if newMaterialize %}
            <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
        {% else %}
            <script src="{{ static.vendor("materialize-css/dist/js/materialize.min.js") }}"></script>
        {% endif %}
        {{ marketingJsTags.global() }}
    </head>
    <body id="shopUser" class="{{ bodyClass }}">
        {{ marketingJsTags.globalAfterBody() }}
        <main>
            {% block main_header %}{% endblock %}
            <div class="container {{ containerClass }}">
                {% block body %}{% endblock %}
            </div>
            <div class="mar-t_a">
                {% block main_footer %}{% endblock %}
            </div>
        </main>

        <script>
            var ScriptShop = function () {
                return {
                    initMaterializeCss: function () {
                        $('.collapsible').collapsible();
                        {% if newMaterialize %}
                            $(".button-collapse").sidenav({menuWidth: 240});
                        {% else %}
                            $(".button-collapse").sideNav({menuWidth: 240});
                        {% endif %}
                        $('.modal').modal();
                        $('.tooltipped')
                            .tooltip()
                            .each(function () {
                                var customClass = $(this).data('custom-class');
                                if (customClass) {
                                    $("#" + $(this).data('tooltip-id') + '.material-tooltip').addClass(customClass);
                                }
                            });
                    },
                    checkIE: function () {
                        var uaIE = window
                            .navigator
                            .userAgent
                            .match(/msie/i);
                        if (uaIE) {
                            $('#header').before('<p class="txt_c warning-browser"><i class="fa fa-exclamation-triangle" aria-hidden="true"></i>お使いのブラウザではサポートされていない機能があります。すべての機能を利用するには、最新のブラウザを使用してください。</p>');
                        }
                    },
                    objectFitImages: function () {
                        objectFitImages('.image-zoom img, .shop-img img'); // object-fit-images for IE
                    }
                };
            }();

            "use strict";
            $(function () {
                ScriptShop.initMaterializeCss();
                ScriptShop.checkIE();
                ScriptShop.objectFitImages();
            });
        </script>

        <div style="display: none">
            {{ marketingJsTags.gaGlobalLite() }}
            {% block tracking %}{% endblock %}
            {{ marketingJsTags.gaGlobalLiteAfter() }}
        </div>

        {% block scripts %}{% endblock %}

        {{ marketingJsTags.globalBeforeCloseBody() }}
    </body>

</html>