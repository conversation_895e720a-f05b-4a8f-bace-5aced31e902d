{% raw %}
<div class="row" v-scope @vue:mounted="mounted">
    <div class="loading" v-if="loading">
        <div class="preloader-wrapper small active">
            <div class="spinner-layer spinner-blue-only">
                <div class="circle-clipper left">
                    <div class="circle"></div>
                </div>
                <div class="gap-patch">
                    <div class="circle"></div>
                </div>
                <div class="circle-clipper right">
                    <div class="circle"></div>
                </div>
            </div>
        </div>
    </div>
    <div id="chat" @scroll="reloadOnScroll" class="scrollable chat col s12">
      <template v-for="message in [...listMessages].reverse()" :key="message.id">
        <div class="row wrap-message-row">
          <div v-if="message.fromAdminId" class="col s1 comment-face">
            <div class="trim-img-circle" style="background-color: #33803a">
                        <img style="height: 23px; width: 23px;" :src="curamaAvatar">
                    </div>
                </div>
                <div class="col s11">
                    <div :class="messageClass(message)">
                        <div v-if="message.label" class="msg-system">{{ message.label }}</div>
                        <div class="comment-balloon-content" v-html="message.content">
                        </div>
                        <div class="wrap-attachment">
                            <ul class="attachment-list">
                                <li v-for="attachment in message.attachments">
                                    <a :href="attachment.url" target="_blank" class="modal-trigger">
                                        <img v-if="attachment.attachmentType === 1" class="attachment-thumb"
                                            :src="attachment.urlThumb" />
                                        <img v-if="attachment.attachmentType === 2" class="attachment-thumb"
                                            :src="pdfIcon" />
                                    </a>
                                </li>

                            </ul>
                        </div>
                        <div class="comment-balloon-bottom">
                            <small>{{ dateUtils.formatShopWebCreatedAt(message.createdAt) }}</small>
                        </div>
                    </div>
                </div>

                <div v-if="!message.fromAdminId" class="col s1 comment-face">
                    <div class="trim-img-circle">
                        <img :src="storeAvatar" :alt="storeName" />
                    </div>
                </div>

            </div>
        </template>
    </div>
    <div class="wrap-input-comment msg-create">
        <div class="container">
            <div class="row isUploading" style="display:none">
                <div class="col s11 progress">
                    <div class="indeterminate"></div>
                </div>
            </div>
            <div class="row">
                <div class="col s11">
                    <div class="wrap-comment-balloon balloon-left z-depth-2">
                        <div class="comment-balloon-head">
                            <span class="truncate">{{ threadTypeName }}</span>
                        </div>

                        <form id="message-form"
                              enctype="multipart/form-data" method="post">
                            <input name="threadTypeId" type="hidden" :value="threadTypeId" />
                            <input name="threadId" type="hidden" :value="threadId" />

                            <div class="comment-balloon-inner">
                                <div class="input-field">
                                    <textarea name="message" v-on:change="onDataChange" v-on:paste="onDataChange"
                                              v-on:keyup="onDataChange" v-model="message"
                                              v-on:focus="scrollToTop" v-on:blur="scrollToTop"
                                              id="inputMessage" cols="40"
                                              rows="5"
                                              class="materialize-textarea"></textarea>
                                    <label for="inputMessage">メッセージを入力</label>
                                </div>
                                <div class="validation-error">
                                    <span class="error-text" v-if="errorText">{{ errorText }}</span>
                                    <span class="error-text" v-if="errorFile">{{ errorFile }}</span>
                                </div>
                            </div>

                            <div class="comment-balloon-sabmit clearfix">
                                <div class="wrap-attachment">
                                    <label for="file-input" class="btn-attachment">
                                        <i class="material-icons icon-clip"></i>ファイルを添付
                                    </label>
                                    <div class="attachment-list">
                                        <div v-for="(attachment, index) in attachments" :key="index" class="attachment-item">
                                            <button class="close delete-attachment" type="button" @click="removeImg(index)">
                                                <span aria-hidden="true">×</span>
                                            </button>
                                            <div v-bind:style="{'height': '36px', 'width': '36px', 'position': 'relative', 'background-image': attachment.attachmentType === 1 ? 'url('+attachment.url+')': 'url(' + pdfIcon + ')', 'background-size': 'cover'}"></div>
                                        </div>
                                    </div>
                                    <input id="file-input" ref="file" @change="uploadFile" class="hidden" type="file"
                                           multiple
                                           accept=".jpg,.jpeg,.png,.pdf" name="attachments"
                                    />
                                    <button id="send-message-button" type="button"
                                            @mouseDown="sendMessage"
                                            class="btn waves-effect waves-light btn-submit"
                                            :disabled="isDisabledSendButton">
                                        送信する
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col s1 comment-face">
                    <div class="trim-img-circle">
                        <img class="img-" :src="storeAvatar" :alt="storeName">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endraw %}

{% block scripts %}
    <script>
      PetiteVue.createApp({
        message: "",
        loading: true,
        isAdmin: {{ isAdmin | safe }},
        threadTypeId: {{ threadType.id }},
        threadId: '{{ thread.id }}',
        threadTypeSlug: '{{ threadTypeSlug }}',
        threadTypeName: '{{ threadType.name }}へのメッセージ',
        attachments: [],
        fileList: [],
        listMessages: [],
        latestSentDate: new Date(),
        currentPage: 1,
        perPage: 16,
        errorFile: "",
        errorText: "",
        isDisabledSendButton: true,
        loadMore: false,
        curamaAvatar: "{{ assets.url('/assets/img/icon_w_curamakun.svg') }}",
        pdfIcon: "{{ assets.url('assets/img/pdf-icon.png') }}",
        storeAvatar: "{{ static.publicImage(60, 60, shop.dataShop.store.avatarPath) }}",
        storeName: "{{ shop.dataShop.store.staff_name | escape | nl2br }}",
        keyValStorage: `${window.location.pathname}/{{ shop.dataShop.store.id }}`,
        keyStorage: 'message-input-state',
        formatDate(date, format) {
          return dayjs(date).format(format);
        },
        isShowDate(date) {
          if (this.formatDate(date, "YYYYMMDD") === this.formatDate(this.latestSentDate, "YYYYMMDD")) {
            return false;
          }
          this.latestSentDate = date;
          return true;
        },
        messageClass(message) {
          if (message.fromAdminId) {
            return "wrap-comment-balloon balloon-right";
          }

          return "wrap-comment-balloon balloon-left";
        },
        showAttachmentModal(e) {
          const modalId = $(e.target.parentElement).data("id");
          $("#" + modalId).modal("open");
        },
        getMessage(page = 1, perPage = 10, scrollToEnd = false) {
          this.loading = true;
          return axiosClient({
            method: "get",
            url: `/shop/inbox/detail/curama/${this.threadTypeSlug}/messages/?page=${page}&perPage=${perPage}`
          })
            .then(result => {
              if (result.data.success) {
                if (result.data.data) {
                  this.listMessages.push(...result.data.data);
                  if (result.data.meta && this.listMessages.length < result.data.meta.total) {
                    this.loadMore = true;
                  } else {
                    this.loadMore = false;
                  }
                  this.loading = false;
                  this.$nextTick(() => {
                    if (scrollToEnd) {
                      this.scrollToEnd();
                    }
                  });
                }
              }
            })
            .catch(e => {
              console.log(e);
            });
        },
        scrollToEnd() {
          const thread = document.getElementById("chat");
          if (thread) {
            thread.scrollTo({ top: thread.scrollHeight });
          }
        },
        calculatePerPage() {
          const chatHeight = $("#chat").height();
          return Math.ceil(chatHeight / 100 + 5);
        },

        async reloadOnScroll(e) {
          if (e.target.scrollTop === 0 && this.loadMore) {
            this.loading = true;
            this.currentPage++;
            const oldScrollHeight = e.target.scrollHeight;
            await this.getMessage(this.currentPage, this.perPage, false);
            const newScrollHeight = e.target.scrollHeight;
            e.target.scrollTop = newScrollHeight - oldScrollHeight;
          }
        },
        sendMessage() {
          if (this.isAdmin) {
            alert(JSON.stringify({detail: 'この動作したらアカンで〜'}));
            return;
          }
          const validationResult = this.validateMessage();
          if (!validationResult) {
            return;
          }
          this.loading = true;
          const messageData = new FormData();
          messageData.append("message", this.message?.trim());
          messageData.append("threadTypeId", this.threadTypeId);
          for (const file of this.fileList) {
            messageData.append("attachments", file, file.name);
          }

          const sendMessageUrl = `/shop/inbox/detail/curama/${this.threadTypeSlug}/`;
          return axiosClient({
            method: "post",
            url: sendMessageUrl,
            data: messageData,
            headers: { "Content-Type": "multipart/form-data" }
          })
            .then(result => {
              this.setTempMessages('');
              if (result.data.success) {
                this.loading = false;
                this.message = "";
                this.attachments = [];
                document.getElementById("file-input").value = [];
                this.fileList = [];

                if (result.data.data.message) {
                  this.listMessages.unshift(result.data.data.message);
                  this.isDisabledSendButton = true;
                  this.$nextTick(() => {
                    this.scrollToEnd();
                  });
                }
                $("#inputMessage").height('');
                resizeScroller(getScrollerHeight());
                this.scrollToTop();
              }
            })
            .catch(e => {
              alert(e.response.data.message);
            });
        },
        scrollToTop() {
          $(window).scrollTop(0);
        },
        validateMessage() {
          this.validateFile();
          this.validateText();
          if (!this.message && !this.attachments.length) {
            return false;
          }

          if (this.errorText || this.errorFile) {
            return false;
          }

          return true;
        },
        validateText() {
          if (this.message.length === 0) {
            this.errorText = "";
            return true;
          }

          if (this.message.trim().length < 10) {
            this.errorText = "10文字以上のメッセージを書いてください。";
            return false;
          } else {
            this.errorText = "";
          }

          if (this.message.trim().length > 2000) {
            this.errorText = "2000文字以内のメッセージを書いてください。";
            return false;
          } else {
            this.errorText = "";
          }

          return true;
        },
        async uploadFile() {
          const SIZE_5MB = 5 * 1000 * 1000;
          const SIZE_25MB = 25 * 1000 * 1000;
          this.errorFile = "";
          const files = document.getElementById("file-input").files;
          const fileList = [...files];
          if (fileList.some(e => !new RegExp(/^(image\/(png|jpe?g)|application\/pdf)$/).exec(e.type))) {
            document.getElementById("file-input").value = [];
            this.errorFile = "jpg、pngまたはpdf以外のファイルが含まれているため、登録できませんでした。";
            return;
          }
          if (fileList.length + this.attachments.length > 5) {
            document.getElementById("file-input").value = [];
            this.errorFile = "添付ファイルの上限数は5ファイルです。（jpg、pngまたはpdf）";
            return;
          }
          for (const [index, file] of fileList.entries()) {
            if (file.size > SIZE_5MB && file.type === "application/pdf") {
              document.getElementById("file-input").value = [];
              this.errorFile = "サイズが大きすぎるPDFファイルが含まれているため、登録できませんでした。\n 5MB以下のPDFファイルを選択してください。";
              return;
            }
            if (file.type !== "application/pdf") {
              if (file.size > SIZE_25MB) {
                document.getElementById("file-input").value = [];
                this.errorFile = "サイズが大きすぎる画像が含まれているため、登録できませんでした。\n 25MB以下の画像を選択してください。";
                return;
              }
              if (file.size > SIZE_5MB) {
                const compressedFile = await this.imageCompressor(file);
                if (compressedFile.size > SIZE_5MB) {
                  document.getElementById("file-input").value = [];
                  this.errorFile = "圧縮後の画像サイズが大きすぎる画像が含まれているため、登録できませんでした。";
                  return;
                }
                fileList[index] = compressedFile;
              }
            }
          }
          this
            .attachments
            .push(...fileList.map(e => ({
              id: "",
              name: e.name,
              attachmentType: e.type === "application/pdf"
                ? 2
                : 1,
              url: URL.createObjectURL(e)
            })));
          this.isDisabledSendButton = this.checkShowButton();
          this.fileList.push(...fileList);
          resizeScroller(getScrollerHeight());
        },

        validateFile() {
          const SIZE_5MB = 5 * 1000 * 1000;
          const SIZE_25MB = 25 * 1000 * 1000;
          const files = document.getElementById("file-input").files;
          if (files || files.length === 0) {
            this.errorFile = "";
            return true;
          }

          if (files.length > 5) {
            this.errorFile = "添付ファイルの上限数は5ファイルです。（jpg、pngまたはpdf）";
            return false;
          }
          for (const file of files) {
            if (file.size > SIZE_5MB && file.type === "application/pdf") {
              this.errorFile = "サイズが大きすぎるPDFファイルが含まれているため、登録できませんでした。\n 5MB以下のPDFファイルを選択してください。";
              return false;
            }
            if (file.type !== "application/pdf") {
              if (file.size > SIZE_25MB) {
                this.errorFile = "サイズが大きすぎる画像が含まれているため、登録できませんでした。\n 25MB以下の画像を選択してください。";
                return false;
              }
              if (file.size > SIZE_5MB) {
                this.errorFile = "圧縮後の画像サイズが大きすぎる画像が含まれているため、登録できませんでした。";
                return false;
              }
            }
          }

          this.errorFile = "";
          return true;
        },
        imageCompressor(file) {
          return new Promise((resolve, reject) => {
            new Compressor(file, {
              quality: 0.65,
              success(blob) {
                resolve(blob);
              },
              error() {
                reject("画像の圧縮に失敗しました。\n再度、画像の選択をお願いします。");
              }
            });
          });
        },
        removeImg(index) {
          this.errorFile = "";
          this.attachments.splice(index, 1);
          this.fileList.splice(index, 1);
          document.getElementById("file-input").value = "";
        },
        checkShowButton() {
          return (!this.message && this.attachments.length === 0) || this.errorFile || this.errorText;
        },
        setTempMessages(value) {
          const contentStorage = this.getTempMessages();
          contentStorage[this.keyValStorage] = value;
          sessionStorage.setItem(this.keyStorage, JSON.stringify(contentStorage));
        },
        getTempMessages() {
          const tempMessage = sessionStorage.getItem(this.keyStorage);
          return tempMessage ? JSON.parse(tempMessage) : {};
        },
        onDataChange(e) {
          this.setTempMessages(this.message);
          this.validateMessage();
          this.isDisabledSendButton = this.checkShowButton();
          resizeScroller(getScrollerHeight());
        },
        async mounted() {
          const tempMessage = this.getTempMessages();
          this.message = tempMessage[this.keyValStorage] ?? '';
          resizeScroller(getScrollerHeight());
          this.perPage = this.calculatePerPage();
          await this.getMessage(this.currentPage, this.perPage, true);
          this.onDataChange({});
        }
      }).mount();
    </script>
{% endblock %}