{% extends "../../layout/mobile-layout.njk" %}

{% set titlePage = "メッセージ詳細 - くらしのマーケット 店舗管理システム" %}
{% set showSmartBanner = false %}
{% set newMaterialize = false %}

{% block styles %}
  <link rel="stylesheet" href="{{ assets.url("css/shop/inquiry-thread/assets/mobile-chat/style.css") }}">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
{% endblock %}

{% block body %}
  {% raw %}
    <div class="chatbox" v-cloak v-scope @vue:mounted="mounted">
      <div class="chatbox-loading" v-if="loadingSendMsg">
        <div class="preloader-wrapper small active">
          <div class="spinner-layer spinner-blue-only">
            <div class="circle-clipper left">
              <div class="circle"></div>
            </div>
            <div class="gap-patch">
              <div class="circle"></div>
            </div>
            <div class="circle-clipper right">
              <div class="circle"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="chatbox-wrapper" id="chatbox-wrapper" @scroll="reloadOnScroll">
        <div ref="chats" id="chatbox-messages" class="chatbox-messages" :class="{'h-full': listMessages.length === 0 }">
          <div class="flex-center" v-if="loading">
            <div class="msg-loader"></div>
          </div>
          <div v-for="(message,index) in [...listMessages].reverse()" :key="message.id" class="chatbox-message__item">
            <div v-if="isShowDate(message.createdAt, [...listMessages].reverse()[index-1]?.createdAt)" class="message-date">
              <span>{{ dateUtils.formatGroupMessagesDate(message.createdAt) }}</span>
            </div>
            <div v-if="message.fromAdminId" class="message-item message-item--admin">
              <div class="message-item__avatar">
                <img alt="no_image" :src="curamaAvatar" />
              </div>
              <div class="message-item__contents">
                <div class="message-item__contentsWrapper">
                  <div v-if="message.label" class="msg-system">{{ message.label }}</div>
                  <div class="message-item__content" v-html="message.content"></div>
                  <div class="message-item__attachments">
                    <template v-for="(attachment) in message.attachments" key="attachment.id">
                      <a :href="attachment.url" target="_blank">
                        <div class="attachment-preview"
                          v-bind:style="{'background-image': attachment.attachmentType === 1 ? 'url('+attachment.urlThumb+')': 'url(' + pdfIcon + ')', 'background-size': 'cover'}"></div>
                      </a>
                    </template>
                  </div>
                </div>
              </div>
              <div class="message-item__sentAt">
                <span>{{ formatDate(message.createdAt, 'H:mm') }}</span>
              </div>
            </div>
            <div v-if="!message.fromAdminId" class="message-item message-item--user">
              <div class="message-item__sentAt">
                <span>{{ formatDate(message.createdAt, 'H:mm') }}</span>
              </div>
              <div class="message-item__contents">
                <div class="message-item__contentsWrapper">
                  <div class="message-item__content" v-html="message.content"></div>
                  <div class="message-item__attachments">
                    <template v-for="(attachment) in message.attachments" key="attachment.id">
                      <a :href="attachment.url" target="_blank">
                        <div class="attachment-preview"
                          v-bind:style="{'background-image': attachment.attachmentType === 1 ? 'url('+attachment.urlThumb+')': 'url(' + pdfIcon + ')', 'background-size': 'cover'}"></div>
                      </a>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="chatbox-footer">
        <div class="chatbox-footer__wrapper">
          <div class="chatbox-footer__attachments">
            <div v-for="(attachment, index) in attachments" :key="index" class="attachment-preview"
              v-bind:style="{'position': 'relative', 'background-image': attachment.attachmentType === 1 ? 'url('+attachment.url+')': 'url(' + pdfIcon + ')', 'background-size': 'cover'}">
              <div class="remove-attachment" @click="removeImg(index)">
                <span>
                  <i class="material-icons">close</i>
                </span>
              </div>
            </div>
          </div>
          <div class="chatbox-footer__errors" v-show="errorText || errorFile">
            <span v-if="errorText">{{ errorText }}</span>
            <span v-if="errorFile">{{ errorFile }}</span>
          </div>
          <div class="chatbox-footer__inputs">
            <a href="javascript:void(0);" type="button" @click="document.getElementById('file-input').click();" class="chatbox-input--icon">
              <i class="material-icons">attach_file</i>
            </a>
            <input type="file" id="file-input" ref="file" @change="uploadFile" hidden="hidden" name="attachments"
              multiple accept="application/pdf,image/png,image/jpeg" />

            <div class="chatbox-input--textWrapper">
              <span @paste="onPasteMsg" class="chatbox-input--textContent" id="messageInputEl" contenteditable="true" v-on:input="onMessageInput"
                minlength="10" placeholder="メッセージを入力"></span>
            </div>
            
            <a href="javascript:void(0);" :class="{'chatbox-input--icon': true, 'disable-link': isDisabledSendButton}" @click="sendMessage">
              <i class="material-icons">send</i>
            </a>
          </div>
        </div>
      </div>
      <div id="helpfeelModal" class="modal bottom-sheet">
        <div class="modal-content modal-center">
          <h3>くらしのマーケットに連絡する前に、よくある質問をご参照ください。</h3>
          <a id="helpFeelFaqLink" target="_blank" href="https://helpfeel.com/curama-shop">よくある質問を見る</a>
          <button id="helpFeelCreateMessageButton" disabled class="modal-close waves-effect waves-light btn" type="button">
            内容を入力する
          </button>
        </div>
        <script type="application/javascript">
          $("#helpFeelFaqLink").click(() => {
            $("#helpFeelCreateMessageButton").attr("disabled", false);
          });
        </script>
      </div>
    </div>
  {% endraw %}
{% endblock %}

{% block main_footer %}

{% endblock %}

{% block headScript %}
  <script src="{{ assets.url("js/axios.min.js") }}"></script>
  <script src="{{ assets.url("js/petite-vue.js") }}"></script>
  <script src="{{ assets.url("js/dayjs.min.js") }}"></script>
  <script src="{{ assets.url("js/dayjs.ja.js") }}"></script>
  <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
  <script src="{{ assets.url("js/compressor.min.js") }}"></script>
  <script src="{{ assets.url("js/axios-client.js") }}" csrf="{{ _csrf | safe }}"></script>
{% endblock %}

{% block scripts %}
  <script>
    const isShowHelpFeelModal = {{ isShowHelpFeelModal }};
    if (isShowHelpFeelModal) {
      $("#helpfeelModal").modal("open", { dismissible: false, preventScrolling: true });
    }

    PetiteVue.createApp({
      message: "",
      loading: false,
      loadingSendMsg: false,
      threadTypeId: {{ threadType.id }},
      threadId: '{{ thread.id }}',
      threadTypeSlug: '{{ threadTypeSlug }}',
      attachments: [],
      listMessages: [],
      fileList: [],
      currentPage: 1,
      perPage: 20,
      errorFile: "",
      errorText: "",
      loadMore: false,
      isDisabledSendButton: true,
      pdfIcon: "{{ assets.url('assets/img/pdf-icon.png') }}",
      curamaAvatar: "{{ assets.url('/assets/img/icon_w_curamakun.svg') }}",
      storeAvatar: "{{ static.publicImage(60, 60, shop.dataShop.store.avatarPath) }}",
      storeName: "{{ shop.dataShop.store.staff_name }}",
      formatDate(date, format) {
        if (!date) {
          return undefined;
        }
        return dayjs(date).format(format);
      },
      isShowDate(date, previousDate) {
        if (this.formatDate(date, "YYYYMMDD") === this.formatDate(previousDate, "YYYYMMDD")) {
          return false;
        }
        return true;
      },
      sendMessage() {
        const validationResult = this.validateMessage();
        if (!validationResult) {
          return;
        }
        this.loadingSendMsg = true;
        const messageData = new FormData();
        messageData.append("message", this.message.trim());
        messageData.append("threadTypeId", this.threadTypeId);
        messageData.append("threadId", this.threadId);
        for (const file of this.fileList) {
          messageData.append("attachments", file, file.name);
        }

        const sendMessageUrl = `/shopapp/consulting-message/v1/threads/${this.threadTypeSlug}/messages`;
    
        axiosClient({
          method: "post",
          url: sendMessageUrl,
          data: messageData,
          headers: { "Content-Type": "multipart/form-data", "Accept": "application/json" }
        })
        .then(result => {
          this.setTempMessages('');
          if (result.data.success) {
            this.loadingSendMsg = false;
            this.message = "";
            document.getElementById("messageInputEl").innerText = "";
            this.attachments = [];
            document.getElementById("file-input").value = [];
            this.fileList = [];
            if (result.data.data.message) {
              this.listMessages.unshift(result.data.data.message);
              this.$nextTick(() => {
                this.scrollToEnd();
              });
            }
          }
        })
        .catch(e => {
          if (e.code === "ERR_NETWORK") {
            if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
              window.flutter_inappwebview.callHandler("ShowToast", "ネットワークに接続していません。");
            } else {
              alert("ネットワークに接続していません。")
            }
          } else {
            alert(e.response.data.message);
          }
          this.loadingSendMsg = false;
        });
      },
      getMessage(page = 1, perPage = 10, scrollToEnd = false) {
        const threadId = '{{ thread.id }}';
        return axiosClient({
          method: "get",
          url: `/shopapp/consulting-message/v1/threads/${this.threadTypeSlug}/messages?page=${page}&perPage=${perPage}`,
          headers: {
            "Accept": "application/json"
          }
        })
        .then(result => {
          if (result.data.success) {
            if (result.data.data) {
              this.listMessages.push(...result.data.data);
              if (result.data.meta && this.listMessages.length < result.data.meta.total) {
                this.loadMore = true;
              } else {
                this.loadMore = false;
              }
              this.$nextTick(() => {
                if (scrollToEnd) {
                  this.scrollToEnd();
                }
              });
            }
          }
        })
        .catch(e => {
          console.log(e);
        });
      },
      scrollToEnd() {
        const chatboxWrapper = document.getElementById("chatbox-wrapper");
        if (chatboxWrapper) {
          chatboxWrapper.scrollTop = chatboxWrapper.scrollHeight;
        }
      },
      validateMessage() {
        this.validateText();
        this.validateFile();
        if (!this.message && !this.attachments.length) {
          return false;
        }

        if (this.errorText || this.errorFile) {
          return false;
        }

        return true;
      },
      validateFile() {
        const SIZE_5MB = 5 * 1000 * 1000;
        const SIZE_25MB = 25 * 1000 * 1000;
        const files = document.getElementById("file-input").files;
        if (files || files.length === 0) {
          this.errorFile = "";
          return true;
        }

        if (files.length > 5) {
          this.errorFile = "添付ファイルの上限数は5ファイルです。（jpg、pngまたはpdf）";
          return false;
        }

        for (const file of files) {
          if (file.size > SIZE_5MB && file.type === "application/pdf") {
            this.errorFile = "サイズが大きすぎるPDFファイルが含まれているため、登録できませんでした。\n 5MB以下のPDFファイルを選択してください。";
            return false;
          }
          if (file.type !== "application/pdf") {
            if (file.size > SIZE_25MB) {
              this.errorFile = "サイズが大きすぎる画像が含まれているため、登録できませんでした。\n 25MB以下の画像を選択してください。";
              return false;
            }
            if (file.size > SIZE_5MB) {
              this.errorFile = "圧縮後の画像サイズが大きすぎる画像が含まれているため、登録できませんでした。";
              return false;
            }
          }
        }

        this.errorFile = "";
        return true;
      },
      validateText() {
        this.errorText = "";
        this.isDisabledSendButton = false;
        if (this.message.length === 0) {
          return true;
        }

        if (this.message.trim().length < 10) {
          this.errorText = "10文字以上のメッセージを書いてください。";
          this.isDisabledSendButton = true;
          return false;
        }

        if (this.message.trim().length > 2000) {
          this.errorText = "2000文字以内のメッセージを書いてください。";
          this.isDisabledSendButton = true;
          return false;
        }

        return true;
      },
      onMessageInput(e) {
        const msgInput = e.target.innerText;
        this.message = msgInput;
        this.setTempMessages(msgInput);
        this.validateText();
      },
      async reloadOnScroll(e) {
        if (e.target.scrollTop === 0 && this.loadMore) {
          this.loading = true;
          this.currentPage++;
          const oldScrollHeight = e.target.scrollHeight;
          await this.getMessage(this.currentPage, this.perPage, false);
          this.loading = false;
          const newScrollHeight = e.target.scrollHeight;
          e.target.scrollTop = newScrollHeight - oldScrollHeight;
        }
      },
      async uploadFile() {
        const SIZE_5MB = 5 * 1000 * 1000;
        const SIZE_25MB = 25 * 1000 * 1000;
        this.errorFile = "";
        const files = document.getElementById("file-input").files;
        const fileList = [...files];
        if (fileList.some(e => !new RegExp(/^(image\/(png|jpe?g)|application\/pdf)$/).exec(e.type))) {
          document.getElementById("file-input").value = [];
          this.errorFile = "jpg、pngまたはpdf以外のファイルが含まれているため、登録できませんでした。";
          this.isDisabledSendButton = true;
          return;
        }
        if (fileList.length + this.attachments.length > 5) {
          document.getElementById("file-input").value = [];
          this.errorFile = "添付ファイルの上限数は5ファイルです。（jpg、pngまたはpdf）";
          this.isDisabledSendButton = true;
          return;
        }
        for (const [index, file] of fileList.entries()) {
          if (file.size > SIZE_5MB && file.type === "application/pdf") {
            document.getElementById("file-input").value = [];
            this.errorFile = "サイズが大きすぎるPDFファイルが含まれているため、登録できませんでした。\n 5MB以下のPDFファイルを選択してください。";
            this.isDisabledSendButton = true;
            return;
          }
          if (file.type !== "application/pdf") {
            if (file.size > SIZE_25MB) {
              document.getElementById("file-input").value = [];
              this.errorFile = "サイズが大きすぎる画像が含まれているため、登録できませんでした。\n 25MB以下の画像を選択してください。";
              this.isDisabledSendButton = true;
              return;
            }

            if (file.size > SIZE_5MB) {
              const compressedFile = await this.imageCompressor(file);
              if (compressedFile.size > SIZE_5MB) {
                document.getElementById("file-input").value = [];
                this.errorFile = "圧縮後の画像サイズが大きすぎる画像が含まれているため、登録できませんでした。";
                this.isDisabledSendButton = true;
                return;
              }
              fileList[index] = compressedFile;
            }
          }
        }
        this.attachments
          .push(...fileList.map(e => ({
            id: "",
            name: e.name,
            attachmentType: e.type === "application/pdf"
                ? 2
                : 1,
            url: URL.createObjectURL(e)
          })));
        this.fileList.push(...fileList); 
        this.isDisabledSendButton = false;
        this.$nextTick(() => {
          this.scrollToEnd();
        });
      },
      imageCompressor(file) {
        return new Promise((resolve, reject) => {
          new Compressor(file, {
            quality: 0.65,
            success(blob) {
              resolve(blob);
            },
            error() {
              reject("画像の圧縮に失敗しました。\n再度、画像の選択をお願いします。");
            }
          });
        });
      },
      removeImg(index) {
        this.errorFile = "";
        this.attachments.splice(index, 1);
        this.fileList.splice(index, 1);
        document.getElementById("file-input").value = "";
        this.$nextTick(() => {
          this.scrollToEnd();
        });
      },
      onPasteMsg(e) {
        e.preventDefault();
        var text = (e.originalEvent || e).clipboardData.getData('text/plain');
        document.execCommand("insertText", false, text);
        const msgInput = document.getElementById("messageInputEl");
        if (msgInput) {
          msgInput.scrollTop = msgInput.scrollHeight;
        }
      },
      setTempMessages(value) {
        window.flutter_inappwebview?.callHandler?.('CacheDraftMessage', value);
      },
      async mounted() {
        window.addEventListener("flutterInAppWebViewPlatformReady", (event) => {
          window.flutter_inappwebview?.callHandler?.('GetCachedMessage').then((cachedMessage) => {
            if (cachedMessage) {
              this.message = cachedMessage;
              document.getElementById("messageInputEl").innerText = cachedMessage;
              this.isDisabledSendButton = false;
              this.scrollToEnd();
            }
          });
        })
        await this.getMessage(this.currentPage, this.perPage, true);
      }
    }).mount();
  </script>
{% endblock %}
