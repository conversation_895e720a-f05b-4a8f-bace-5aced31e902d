.modal-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
}

.hidden-overflow {
  overflow: hidden;
}

.chat {
  display: flex;
  flex-direction: column;
}

.attachment-preview-group {
  display: flex;
  flex-direction: row;
  gap: 5px;
  margin-left: 5px
}

.attachment-preview {
  width: 10rem;
  height: 10rem;
  border-radius: 6px
}

.remove-attachment {
  font-size: 20px;
  width: 24px;
  height: 24px;
  background-color: rgb(255 255 255/79%);
  cursor: pointer;
  color: #3d3c3c;
  text-align: center;
  position: absolute;
  top: 0px;
  right: 0px;
  border-radius: 0 6px 0 0
}

.error-text {
  color: #D32F2F;
  font-size: 1.2rem;
  padding-left: 10px;
}

.loading {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1000;
  display: grid;
  place-items: center;
  background-color: rgba(0, 0, 0, 0.5);
}

.balloon-left {
  .comment-balloon-content {
    color: #ffffff;
    a {
      color: #ffffff;
      text-decoration: underline;
    }
  }
}

.wrap-comment-balloon {
  .comment-balloon-content {
    word-break: break-word;
    white-space: pre-wrap;
    a {
      text-decoration: underline;
    }
  }
}

.comment-balloon-content a {
  text-decoration: underline;
}

.wrap-attachment {
  .attachment-list {
    .attachment-item {
      display: flex;
    }
  }
}

.comment-balloon-inner {
  .validation-error {
    bottom: 0.05em;
    position: absolute;
  }
}

.wrap-message-row {
  .msg-system {
    display: inline-block;
    padding: 8px 10px;
    background-color: #fff;
    border: 2px #e3e3e3 solid;
    margin-top: 1em;
    margin-left: 0.8em;
    border-radius: 4px;
    font-size: 1.2rem;
  }
}
