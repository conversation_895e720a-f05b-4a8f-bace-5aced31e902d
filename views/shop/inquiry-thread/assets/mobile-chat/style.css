body {
  font-size: 100%;
  background: #f5f5f5;
  -ms-text-size-adjust: auto;
}

[v-cloak] {
  display: none !important;
}

.container {
  margin: 0;
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.container .row {
  margin: 0;
  padding: 0;
}

.h-full {
  height: 100%;
}

.chatbox {
  height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: stretch;
  overflow: hidden;
}

.chatbox-wrapper {
  height: 100%;
  position: relative;
  padding: 0.25rem 0.5rem;
  padding-bottom: 2vh;
  overflow-x: hidden;
  overflow-y: auto;
}

.chatbox-loading {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 1;
}

.chatbox-messages {
  position: relative;
}

.chatbox-messages--empty {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chatbox-wrapper .message-date {
  text-align: center;
  margin: 0.5rem auto;
  color: #777;
  font-size: 95%;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.chatbox-wrapper .chatbox-message__item {
  position: relative;
  margin: 10px auto;
}

.chatbox-wrapper .message-item {
  display: flex;
  justify-content: flex-start;
  gap: 0.5rem;
  padding-block: 0.25rem;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.chatbox-wrapper .message-item.message-item--user {
  justify-content: flex-end;
}

.chatbox-wrapper .message-item__avatar {
  display: block;
  position: relative;
  padding: 0.5rem;
  text-align: center;
  width: 40px;
  height: 40px;
  background: green;
  padding: .5rem;
  border-radius: 100%;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.chatbox-wrapper .message-item__avatar img {
  width: 23px;
  height: 23px;
  margin: 0 auto;
}

.chatbox-wrapper .message-item__contents {
  position: relative;
  width: 65%;
}

.chatbox-wrapper .message-item--admin .message-item__contents {
  padding-inline: 2vw 0;
}

.chatbox-wrapper .message-item--user .message-item__contents {
  padding-inline: 0 2vw;
}

.message-item__contentsWrapper {
  position: relative;
  display: block;
  padding: .5rem .8rem;
  background: #fff;
  border-radius: .3125rem;
  border-top-right-radius: 0;
  box-shadow: 0px 1px 2px 0px #d6d6d6;
  -webkit-box-shadow: 0px 1px 2px 0px #d6d6d6;
  -moz-box-shadow: 0px 1px 2px 0px #d6d6d6;
  -webkit-user-select: text;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  -o-user-select: text;
  user-select: text;
}

.message-item.message-item--admin .message-item__contentsWrapper {
  border-top-left-radius: 0;
  border-top-right-radius: .3125rem;
}

.mesasge-item__content a {
  color: #0277bd;
}

.message-item.message-item--user .message-item__contentsWrapper {
  background: #0277bd;
}

.message-item.message-item--admin .message-item__contentsWrapper::after {
  content: '';
  width: 14px;
  height: 7px;
  position: absolute;
  top: 0px;
  left: -4px;
  background: inherit;
  border-radius: 7px 0px 0px 0px;
  transform: skew(57deg, 0deg);
  box-shadow: -2px 0px 1px -1px #d6d6d6;
}

.message-item.message-item--user .message-item__contentsWrapper::after {
  content: '';
  width: 14px;
  height: 7px;
  position: absolute;
  top: 0px;
  right: -4px;
  background: inherit;
  border-radius: 0px 7px 0px 0px;
  transform: skew(-57deg, 0deg);
  box-shadow: 2px 0px 0px -1px #d6d6d6;
}

.message-item__content {
  width: 100%;
  word-break: break-word;
  white-space: pre-wrap;
}

.message-item__content a {
  text-decoration: underline;
}

.message-item.message-item--user .message-item__content {
  color: #fff;
}

.message-item.message-item--user .message-item__content a {
  color: #fff;
  text-decoration: underline;
}

.message-item__sentAt {
  color: #777;
  font-size: 10px;
  position: relative;
  display: flex;
  align-items: flex-end;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.chatbox-footer {
  position: relative;
  width: 100%;
}

.chatbox-footer__wrapper {
  display: flex;
  background-color: #fff;
  flex-direction: column;
  align-items: left;
  padding: 0.5rem;
}

.chatbox-footer__attachments {
  display: flex;
  flex-direction: row;
  gap: 5px;
  overflow-x: auto;
  overflow-y: hidden;
  width: 100%;
  justify-content: flex-start;
}

.attachment-preview {
  width: 15vw;
  height: 15vw;
  border-radius: 6px;
  border: 1px solid #efefef;
}

.remove-attachment {
  background-color: rgb(255 255 255/79%);
  cursor: pointer;
  color: #3d3c3c;
  text-align: center;
  position: absolute;
  top: 0px;
  right: 0px;
  border-radius: 0 6px 0 0;
}

.chatbox-footer__errors span {
  color: #d32f2f;
  font-size: 90%;
  margin-block: 5px;
  padding-left: .625rem;
}

.chatbox-footer__inputs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-block: 5px;
}

.chatbox-footer__inputs .chatbox-input--icon {
  position: relative;
  padding: 0.125rem;
}

.material-icons {
  font-family: 'Material Icons' !important;
}

.chatbox-input--icon i.material-icons {
  font-size: 28px;
}

.chatbox-footer__inputs div.chatbox-input--textWrapper {
  border: none;
  width: 100%;
  border-radius: 1rem;
  display: block;
  padding: 0.5rem 1.25rem;
  margin: 0 1rem;
  font-size: 100%;
  color: #495057;
  background-color: #f5f5f5;
  background-clip: padding-box;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  max-height: 114px;
  max-width: calc(100% - 90px);
}

.chatbox-footer__inputs div.chatbox-input--textWrapper:focus {
  outline: none;
  box-shadow: none;
}

div.chatbox-input--textWrapper .chatbox-input--textContent {
  display: inline-block;
  white-space: pre-wrap;
  width: 100%;
  border: none;
  background: transparent;
  font-size: 100%;
  color: #495057;
  padding: 0;
  margin: 0;
  resize: none;
  overflow: hidden;
  outline: none;
  max-height: 100px;
}

[contenteditable='true']:empty:before {
  content: attr(placeholder);
  pointer-events: none;
  display: block;
  color: #cecece;
}

#helpfeelModal {
  background: #fff;
}

#helpfeelModal h3 {
  font-size: 100%;
}

button#helpFeelCreateMessageButton {
  height: 50px;
  width: 100%;
  color: #fff !important;
  margin-top: 1em;
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 4px;
  opacity: 1;
}

#helpFeelFaqLink {
  display: block;
  margin-block: .5rem;
  text-align: center;
}

.modal-overlay {
  background: unset;
}

.disable-link {
  pointer-events: none;
  cursor: default;
}

.flex-center {
  display: flex;
  justify-content: center;
}

.msg-loader {
  width: 25px;
  aspect-ratio: 1;
  display: grid;
  border-radius: 50%;
  background:
    linear-gradient(0deg ,rgb(0 0 0/25%) 30%,#0000 0 70%,rgb(0 0 0/50%) 0) 50%/8% 100%,
    linear-gradient(90deg,rgb(0 0 0/12.5%) 30%,#0000 0 70%,rgb(0 0 0/32.5% ) 0) 50%/100% 8%;
  background-repeat: no-repeat;
  animation: msg-loader-animation 1s infinite steps(12);
}
.msg-loader::before,
.msg-loader::after {
   content: "";
   grid-area: 1/1;
   border-radius: 50%;
   background: inherit;
   opacity: 0.915;
   transform: rotate(30deg);
}
.msg-loader::after {
   opacity: 0.83;
   transform: rotate(60deg);
}
@keyframes msg-loader-animation {
  100% {transform: rotate(1turn)}
}

.msg-system {
  display: inline-block;
  padding: 7px;
  background: #EEEEEE 0% 0% no-repeat padding-box;
  font: normal normal normal 12px/16px Hiragino Kaku Gothic ProN;
  letter-spacing: 0px;
  color: #000000;
  margin-top: 5px;
  margin-bottom: 10px;
  border-radius: 4px;
}
