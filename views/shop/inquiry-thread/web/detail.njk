{% extends "layout/shop/_macro.njk" %}
{% from "components/navbar/_macro.njk" import cngaNavbar %}
{% from "components/sidebar/_macro.njk" import cngaSidebar %}
{% from "admin/components/message-creation.njk" import messageCreation %}

{% set titlePage = "メッセージ詳細 - くらしのマーケット 店舗管理システム" %}
{% set showSmartBanner = false %}
{% set newMaterialize = false %}

{% block styles %}
    <link rel="stylesheet" href="{{ assets.url("css/shop/inquiry-thread/assets/shop-web.css") }}">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>[v-cloak] { display: none; }</style>
{% endblock %}

{% block main_header %}
    {% if isWebview %}
    {% else %}
        {{ cngaNavbar(shop.dataNavbar) }}
        {{ cngaSidebar(shop.dataSidebar) }}
    {% endif %}
{% endblock %}

{% block body %}
    <div class="page-head">
        <div class="page-head-nav">
            <div class="row">
                <div class="col s8 wrap-breadcrumb">
                    <a href="/shop/inbox/" class="breadcrumb">メッセージ</a>
                    <h1><span class="truncate">{{ threadType.name }}</span></h1>
                </div>
            </div>
        </div>
    </div>

    {% include "../partials/messages-block.njk" %}
{% endblock %}

{% block headScript %}
    <script src="{{ assets.url("js/axios.min.js") }}"></script>
    <script src="{{ assets.url("js/petite-vue.js") }}"></script>
    <script src="{{ assets.url("js/dayjs.min.js") }}"></script>
    <script src="{{ assets.url("js/dayjs.ja.js") }}"></script>
    <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
    <script src="{{ assets.url("js/compressor.min.js") }}"></script>
    <script src="{{ assets.url("js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
    <script>
      $("meta[name='viewport']").attr('content', 'width=860, minimum-scale=0.1, interactive-widget=resizes-content'); 
      let initialViewHeight = window.innerHeight;
      function getScrollerHeight() {
        const headerHeight = $(".scrollable").offset();
        let bottomHeight = $(".wrap-input-comment").outerHeight() + 2;
        if(!$(".wrap-input-comment").outerHeight()){
          bottomHeight = 100;
        }
        if (window.navigator.userAgent.indexOf("Mac") != -1) {
          return Math.round(initialViewHeight - headerHeight.top - bottomHeight);
        } 
        const windowHeight = window.innerHeight;
        return Math.round(windowHeight - headerHeight.top - bottomHeight);
      }

      function resizeScroller(height) {
        $(".scrollable").height(height);
      }
    </script>
{% endblock %}

{% block scripts %}
    <script>
      $(document).ready(function(){
        resizeScroller(getScrollerHeight());
        window.addEventListener("resize", () => {
          resizeScroller(getScrollerHeight());
          initialViewHeight = window.innerHeight;
        });
      });
    </script>
{% endblock %}
