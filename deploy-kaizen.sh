#!/bin/bash

INFO='\033[0;36m'
QUESTION='\033[0;32m'
ERROR='\033[1;31m'
SLACK_WEBHOOK_URL="*********************************************************************************"

# STDOUT log function
log() {
  echo "${INFO}$@";
}

logError() {
  echo "${ERROR}$@";
}

logQuestion() {
  echo "${QUESTION}$@";
}

cluster="curama-vietnam-kaizen"

deployApplication() {
  service="curama-consulting-message"
  task_definition="curama-consulting-message"
  latest_revision=$(aws ecs describe-task-definition --task-definition $task_definition | jq -r '.taskDefinition.revision')
  log "[$service] Deploying.... backend to ECS";
  aws ecs update-service --cluster $cluster  --service $service --task-definition $task_definition:$latest_revision --force-new-deployment --no-cli-pager >> /dev/null
  aws ecs wait services-stable --cluster $cluster --services $service
  log "Deploy curama-consulting-message successfully";
  {
    pushSlackMessage "The [curama-consulting-message] application has been successfully deployed to the Kaizen environment. https://vietnam-test.wkwk.io/consulting-message/admin/consulting-message/login"
  } || {
    echo "An error occurred while sending the Slack message."
  }
}

pushSlackMessage() {
  local message="$1"
  slackMessage="*[Deployement][curama-consulting-message-kaizen]* $message"
  curl -X POST -H "Content-type: application/json" --data "{\"text\":\"$slackMessage\"}" "$SLACK_WEBHOOK_URL"
}

log "Login to ECR"
aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin 613224955743.dkr.ecr.ap-northeast-1.amazonaws.com

deployApplication
