import { DataSource, Repository, In } from 'typeorm';
import { AttachmentEntity, InquiryThreadEntity, MessageEntity } from '@src/entities';
import { Test, TestingModule } from '@nestjs/testing';
import { getDataSourceToken, getRepositoryToken } from '@nestjs/typeorm';
import { PAGINATOR } from '@src/common/constants/paginator';
import { InquiryThreadQueryRequest } from '@src/modules/terry/dtos/inquiry-thread-query-request.dto';
import { MessageDto } from '@modules/shop/dtos/message.dto';
import { ShopRequest } from '@modules/shop/interfaces/shop-request.interface';
import { AttachmentService } from '@modules/shared/attachment/attachment.service';
import { InquiryThreadService } from '@src/modules/shared/inquiry-thread/inquiry-thread.service';
import { AttachmentType } from '@common/enums/attachment-type';
import { ThreadType } from '@common/enums/thread-type';
import * as dayjs from 'dayjs';
import { StoreDto } from '@vietnam/curama-py3-api';
import { SearchService } from '@modules/shared/search/services/search.service';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { getRedisToken } from '@liaoliaots/nestjs-redis';

const mockedAmqpConnection = {
  publish: jest.fn(),
  request: jest.fn(),
  createChannel: jest.fn(),
} as unknown as AmqpConnection;

declare global {
  interface String {
    escape(): string;
    urlize(): string;
    nl2br(): string;
  }
}
String.prototype.escape = jest.fn().mockReturnValue('');
String.prototype.urlize = jest.fn().mockReturnValue('');

String.prototype.nl2br = jest.fn().mockReturnValue('');
// Mock transactionalEntityManager
const transactionalEntityManager = {
  save: jest.fn().mockImplementation((entity) => jest.fn()),
};

// Mock dataSource.manager.transaction
const dataSource = {
  query: jest.fn(),
  manager: {
    transaction: jest.fn().mockImplementation((callback) => {
      callback(transactionalEntityManager);
    }),
    query: jest.fn(),
  },
};

const mockInquiryThreadRepo = {
  createQueryBuilder: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    addSelect: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    innerJoin: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    addOrderBy: jest.fn().mockReturnThis(),
    getRawMany: jest.fn().mockResolvedValue([]),
    getSql: jest.fn().mockReturnValue('SQL_QUERY'),
    getParameters: jest.fn().mockReturnValue([]),
  })),
  query: jest.fn(),
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  findAndCount: jest.fn(),
  find: jest.fn(),
};

describe('InquiryThreadService', () => {
  let inquiryThreadService: InquiryThreadService;
  let inquiryThreadEntity: Repository<InquiryThreadEntity>;
  let attachmentService: AttachmentService;
  let replicaDataSource: DataSource;
  const mockRedis = { set: jest.fn(), get: jest.fn(), del: jest.fn() };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InquiryThreadService,
        {
          provide: getRepositoryToken(InquiryThreadEntity),
          useValue: mockInquiryThreadRepo,
        },
        {
          provide: getRepositoryToken(InquiryThreadEntity, 'replicaConnection'),
          useValue: mockInquiryThreadRepo,
        },
        {
          provide: getRepositoryToken(AttachmentEntity),
          useClass: Repository,
        },
        {
          provide: DataSource,
          useValue: dataSource,
        },
        {
          provide: getDataSourceToken('replicaConnection'),
          useValue: dataSource,
        },
        {
          provide: AttachmentService,
          useValue: {
            uploadFile: jest.fn(),
          },
        },
        {
          provide: SearchService,
          useValue: {
            getThreadsByConditions: jest.fn(),
          },
        },
        { provide: getRedisToken('default'), useValue: mockRedis },
        {
          provide: AmqpConnection,
          useValue: mockedAmqpConnection,
        },
      ],
    }).compile();
    inquiryThreadEntity = module.get<Repository<InquiryThreadEntity>>(getRepositoryToken(InquiryThreadEntity));
    inquiryThreadService = module.get<InquiryThreadService>(InquiryThreadService);
    attachmentService = module.get<AttachmentService>(AttachmentService);
    replicaDataSource = module.get<DataSource>(getDataSourceToken('replicaConnection'));
  });

  describe('createMessageByStore()', () => {
    MessageEntity.prototype.reload = jest.fn();
    AttachmentEntity.prototype.reload = jest.fn();
    it('new thread, no attachment', async () => {
      const store = <StoreDto>{
        id: '307cbfe5-ec06-41bf-82cd-39c331fa78ba',
        name: 'Clear houseキレイエ',
        name_kana: 'クリアハウスキレイエ',
        staff_name: 'スタッフさん',
        store_code: '235793570',
      };

      const messageRequest = <MessageDto>{
        threadId: '',
        threadTypeId: 1,
        message: 'msg',
        attachments: [],
      };

      await inquiryThreadService.createMessageByStore(messageRequest, store, []);

      expect(dataSource.manager.transaction).toHaveBeenCalled();
    });

    it('exist thread, have attachment', async () => {
      const store = <StoreDto>{
        id: '307cbfe5-ec06-41bf-82cd-39c331fa78ba',
        name: 'Clear houseキレイエ',
        name_kana: 'クリアハウスキレイエ',
        staff_name: 'スタッフさん',
        store_code: '235793570',
      };
      const thread = <InquiryThreadEntity>(<unknown>{
        id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
        isRead: false,
        latestMessageTime: '2023-09-02T23:07:00',
        title: 'thread_title1',
        assignmentAdmin: { id: '123-abc-4567-dcdf-3245345', name: 'admin name 1' },
        updatedAt: '2023-06-02T23:07:00',
        attachments: [],
      });
      const messageRequest = <MessageDto>{
        threadId: '673c52ca-8203-4e20-837c-81e822e76466',
        threadTypeId: 1,
        message: 'msg',
        attachments: [],
      };

      const attachments: any = [
        {
          fieldname: 'attachments',
          originalname: 'test image.png',
          encoding: '7bit',
          mimetype: 'image/png',
          buffer: [],
          size: 63179,
        },
      ];

      jest.spyOn(attachmentService, 'uploadFile').mockResolvedValue({
        id: '5e73557e-39e2-4424-b6d1-b98546132456',
        path: 'message/673c52ca-8203-4e20-837c-81e822e76466/5e73557e-39e2-4424-b6d1-b98546132456.png',
        relationId: 'f6c523bf-fab4-49a4-902d-5484ad8e23af',
        attachmentType: AttachmentType.Image,
      });

      const result: any = await inquiryThreadService.createMessageByStore(messageRequest, store, attachments, thread);

      expect(dataSource.manager.transaction).toHaveBeenCalled();
      expect(result.data.message).toHaveProperty('attachments');
    });
  });

  describe('getByStore()', () => {
    it('should get the list of inquiry threads by store with page > 0', async () => {
      const request = <ShopRequest>{ storeInfo: { store: { id: '123' } }, path: 'current-path' };
      const page = 1;
      const relations = ['relation1', 'relation2'];
      const perPage = PAGINATOR.SHOP;
      const pageNumber = 1;
      const list = <InquiryThreadEntity[]>(<unknown>[
        {
          id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
          isRead: false,
          latestMessageTime: '2023-09-02T23:07:00',
          title: 'thread_title1',
          assignmentAdmin: { id: '123-abc-4567-dcdf-3245345', name: 'admin name 1' },
          updatedAt: '2023-06-02T23:07:00',
        },
        {
          id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
          isRead: false,
          latestMessageTime: '2023-09-02T23:07:00',
          title: 'thread_title1',
          assignmentAdmin: { id: '123-abc-4567-dcdf-3245345', name: 'admin name 1' },
          updatedAt: '2023-06-02T23:07:00',
        },
      ]);
      const total = 2;

      // Mock the findAndCount method of the inquiryThreadEntity
      jest.spyOn(inquiryThreadEntity, 'findAndCount').mockResolvedValue([list, total]);

      const result = await inquiryThreadService.getByStore(
        request.storeInfo.store.id,
        request.path,
        page,
        perPage,
        relations,
      );

      // @ts-ignore
      expect(result.list).toEqual(list);
      // @ts-ignore
      expect(result.pagination).toEqual({
        pageNumber,
        total,
        perPage,
        currentPathName: request.path,
      });
      expect(inquiryThreadEntity.findAndCount).toHaveBeenCalledWith({
        where: {
          storeId: request.storeInfo.store.id,
        },
        take: perPage,
        skip: 0,
        relations,
      });
    });

    it('should get the list of inquiry threads by store with page = 0', async () => {
      const request = <ShopRequest>{ storeInfo: { store: { id: '123' } }, path: 'current-path' };
      const page = 0;
      const relations = ['relation1', 'relation2'];
      const perPage = PAGINATOR.SHOP;
      const pageNumber = 1;
      const list = <InquiryThreadEntity[]>(<unknown>[
        {
          id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
          isRead: false,
          latestMessageTime: '2023-09-02T23:07:00',
          title: 'thread_title1',
          assignmentAdmin: { id: '123-abc-4567-dcdf-3245345', name: 'admin name 1' },
          updatedAt: '2023-06-02T23:07:00',
        },
        {
          id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
          isRead: false,
          latestMessageTime: '2023-09-02T23:07:00',
          title: 'thread_title1',
          assignmentAdmin: { id: '123-abc-4567-dcdf-3245345', name: 'admin name 1' },
          updatedAt: '2023-06-02T23:07:00',
        },
      ]);
      const total = 2;

      // Mock the findAndCount method of the inquiryThreadEntity
      jest.spyOn(inquiryThreadEntity, 'findAndCount').mockResolvedValue([list, total]);

      const result = await inquiryThreadService.getByStore(
        request.storeInfo.store.id,
        request.path,
        page,
        perPage,
        relations,
      );

      // @ts-ignore
      expect(result.list).toEqual(list);
      // @ts-ignore
      expect(result.pagination).toEqual({
        pageNumber,
        total,
        perPage,
        currentPathName: request.path,
      });
    });
  });

  describe('getById()', () => {
    it('should return the inquiry thread', async () => {
      const inquiryThread = <InquiryThreadEntity>(<unknown>{
        id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
        isRead: false,
        latestMessageTime: '2023-09-02T23:07:00',
        title: 'thread_title1',
        assignmentAdmin: { id: '123-abc-4567-dcdf-3245345', name: 'admin name 1' },
        updatedAt: '2023-06-02T23:07:00',
      });
      jest.spyOn(inquiryThreadEntity, 'findOne').mockResolvedValue(inquiryThread);
      const result = await inquiryThreadService.getById(inquiryThread.id, ['assignmentAdmin']);
      expect(result).toEqual(inquiryThread);
      expect(inquiryThreadEntity.findOne).toHaveBeenCalledWith({
        where: { id: inquiryThread.id },
        relations: ['assignmentAdmin'],
      });
    });
  });

  describe('getAll()', () => {
    it('should return the list of inquiry threads with pagination', async () => {
      const inquiryThreadQueryRequest = <InquiryThreadQueryRequest>{
        page: undefined,
        storeId: 'd60fe544-065b-4352-8e24-40f791617485',
        assignmentTeamId: '2c4afe27-7c0d-4f4a-94dc-f00df68b3be3',
        assignmentAdminId: '914fd2b4-37a3-4e28-8785-5822da3a3389',
        status: 1,
        updatedAt: '2023-10-01 09:20:00',
        search: 'search message',
        pageNumber: 1,
        perPage: PAGINATOR.SHOP,
      };

      const mockThreads = [
        {
          thread_id: '1',
          store_name: 'Store Name 1',
          thread_status_id: 2,
          thread_updated_at: '2023-10-01T09:20:00Z',
          msg_content: 'Content 1',
          msg_updated_at: '2023-10-01T09:30:00Z',
        },
        {
          thread_id: '2',
          store_name: 'Store Name 2',
          thread_status_id: 3,
          thread_updated_at: '2023-10-01T09:25:00Z',
          msg_content: 'Content 2',
          msg_updated_at: '2023-10-01T09:35:00Z',
        },
      ];

      const openSearchMockResult = {
        hits: {
          total: { value: 2 },
          hits: [
            {
              _source: {
                thread_id: '1',
                store_name: 'Store Name 1',
                thread_status_id: 2,
                thread_updated_at: '2023-10-01T09:20:00Z',
                msg_content: 'Content 1',
                msg_updated_at: '2023-10-01T09:30:00Z',
              },
            },
            {
              _source: {
                thread_id: '2',
                store_name: 'Store Name 2',
                thread_status_id: 3,
                thread_updated_at: '2023-10-01T09:25:00Z',
                msg_content: 'Content 2',
                msg_updated_at: '2023-10-01T09:35:00Z',
              },
            },
          ],
        },
      };

      jest
        .spyOn(inquiryThreadService['searchService'], 'getThreadsByConditions')
        .mockResolvedValue(openSearchMockResult as any);

      mockInquiryThreadRepo.createQueryBuilder = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue(mockThreads),
        getSql: jest.fn().mockReturnValue('SQL_QUERY'),
        withDeleted: jest.fn().mockReturnThis(),
        getParameters: jest.fn().mockReturnValue([]),
      }));

      const result = await inquiryThreadService.getAll(inquiryThreadQueryRequest);

      const expectedThreads = [
        {
          thread_id: '1',
          store_name: 'Store Name 1'.escape().urlize(),
          thread_status_id: 2,
          thread_updated_at: '2023-10-01T09:20:00Z',
          msg_content: 'Content 1',
          msg_updated_at: '2023-10-01T09:30:00Z',
          thread_status_name: '確認中',
        },
        {
          thread_id: '2',
          store_name: 'Store Name 2'.escape().urlize(),
          thread_status_id: 3,
          thread_updated_at: '2023-10-01T09:25:00Z',
          msg_content: 'Content 2',
          msg_updated_at: '2023-10-01T09:35:00Z',
          thread_status_name: '既読',
        },
      ];

      expect(result.threads).toEqual(expectedThreads);
      expect(result.pagination).toEqual({
        perPage: PAGINATOR.SHOP,
        pageNumber: 1,
        total: 0,
      });
    });
  });

  describe('getByCondition()', () => {
    it('should return the inquiry thread', async () => {
      jest.spyOn(inquiryThreadEntity, 'findOne').mockResolvedValue({} as any);
      const result = await inquiryThreadService.getByCondition({}, ['assignmentAdmin']);
      expect(result).toBeTruthy();
    });
  });

  describe('getLatestMessageForEachType()', () => {
    it('success', async () => {
      mockInquiryThreadRepo.createQueryBuilder = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          {
            thread_type: 1,
            is_read: false,
            latest_message: 'dummy message',
            latest_time: '2023-10-01T09:20:00Z',
            deleted_at: null,
          },
          {
            thread_type: 2,
            is_read: true,
            latest_message: 'another message',
            latest_time: '2023-10-01T09:25:00Z',
            deleted_at: null,
          },
        ]),
        getSql: jest.fn().mockReturnValue('SQL_QUERY'),
        withDeleted: jest.fn().mockReturnThis(),
        getParameters: jest.fn().mockReturnValue([]),
      }));
      jest.spyOn(replicaDataSource, 'query').mockResolvedValue([] as any);
      const result = await inquiryThreadService.getLatestMessageForEachType('d60fe544-065b-4352-8e24-40f791617485');
      expect(result).toBeTruthy();
    });
  });

  describe('saveThread()', () => {
    it('success', async () => {
      jest.spyOn(inquiryThreadEntity, 'save').mockResolvedValue({} as any);
      const result = await inquiryThreadService.saveThread({ id: '1' } as any);
      expect(result).toBeTruthy();
    });
  });

  describe('updateThreadById()', () => {
    it('success', async () => {
      jest.spyOn(inquiryThreadEntity, 'update').mockResolvedValue({} as any);
      const result = await inquiryThreadService.updateThreadById('1', {} as any);
      expect(result).toBeTruthy();
    });
  });

  describe('getOrCreateThread()', () => {
    it('created thread', async () => {
      jest.spyOn(inquiryThreadEntity, 'findOne').mockResolvedValue({} as any);
      const result = await inquiryThreadService.getOrCreateThread('1', 'name', 1);
      expect(result).toBeTruthy();
    });
    it('new thread', async () => {
      jest.spyOn(inquiryThreadEntity, 'findOne').mockResolvedValue(null);
      jest.spyOn(inquiryThreadEntity, 'create').mockReturnValue({} as any);
      jest.spyOn(inquiryThreadEntity, 'save').mockResolvedValue({} as any);
      const result = await inquiryThreadService.getOrCreateThread('1', 'name', 1);
      expect(result).toBeTruthy();
    });
  });

  describe('getThreadByType()', () => {
    it('should return the inquiry thread', async () => {
      const inquiryThread = <InquiryThreadEntity>(<unknown>{
        id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
        isRead: false,
        latestMessageTime: '2023-09-02T23:07:00',
        title: 'thread_title1',
        assignmentAdmin: { id: '123-abc-4567-dcdf-3245345', name: 'admin name 1' },
        updatedAt: '2023-06-02T23:07:00',
        threadType: ThreadType.CuramaThread,
      });
      jest.spyOn(mockInquiryThreadRepo, 'findOne').mockResolvedValue(inquiryThread);
      const result = await inquiryThreadService.getThreadByType(inquiryThread.id, ThreadType.CuramaThread);
      expect(result).toEqual(inquiryThread);
    });
  });

  describe('getThreadType()', () => {
    it('get QualityThread', async () => {
      const threadType = inquiryThreadService.getThreadTypeObject(ThreadType.QualityThread);
      expect(threadType).toHaveProperty('name', '品質管理チーム');
      expect(threadType).toHaveProperty('id', 2);
    });

    it('get CuramaThread', async () => {
      const threadType = inquiryThreadService.getThreadTypeObject(ThreadType.CuramaThread);
      expect(threadType).toHaveProperty('name', 'くらしのマーケット');
      expect(threadType).toHaveProperty('id', 1);
    });

    it('not found thread type', async () => {
      expect(inquiryThreadService.getThreadTypeObject(3)).toEqual(null);
    });
  });

  describe('isShowHelpFeelModal()', () => {
    it('thread is QualityThread must return false', async () => {
      const isShowHelpFeelModal = inquiryThreadService.isShowHelpFeelModal(ThreadType.QualityThread, []);
      expect(isShowHelpFeelModal).toEqual(false);
    });

    it('thread is CuramaThread but list message empty must return true', async () => {
      const isShowHelpFeelModal = inquiryThreadService.isShowHelpFeelModal(ThreadType.CuramaThread, []);
      expect(isShowHelpFeelModal).toEqual(true);
    });

    it('thread is CuramaThread, list message not empty, last message created_at was 3 days ago return true', async () => {
      const listMessage = [
        {
          id: '0da19d22-4ccd-45e3-9437-3bbeeda038c9',
          createdAt: new Date('2023-09-21T09:42:45.584Z'),
          threadId: '83de3fc1-913d-429f-b8f2-85f6f520df6a',
          content: 'Demo message 2',
          type: 1,
          attachments: [],
        },
      ];
      // @ts-ignore
      const isShowHelpFeelModal = inquiryThreadService.isShowHelpFeelModal(ThreadType.CuramaThread, listMessage);
      expect(isShowHelpFeelModal).toEqual(true);
    });

    it('thread is CuramaThread, list message not empty, last message created_at was 2 days ago return false', async () => {
      const listMessage = [
        {
          id: '0da19d22-4ccd-45e3-9437-3bbeeda038c9',
          createdAt: dayjs().add(-2).toDate(),
          threadId: '83de3fc1-913d-429f-b8f2-85f6f520df6a',
          content: 'Demo message 2',
          type: 1,
          attachments: [],
        },
      ];
      // @ts-ignore
      const isShowHelpFeelModal = inquiryThreadService.isShowHelpFeelModal(ThreadType.CuramaThread, listMessage);
      expect(isShowHelpFeelModal).toEqual(false);
    });
  });

  describe('getThreadsByStoreIdsAndType()', () => {
    it('should return a map of threads by store ID and thread type', async () => {
      const storeIds = ['store-1', 'store-2'];
      const threadType = ThreadType.CuramaThread;

      const mockThreads = [
        { storeId: 'store-1', id: 'thread-1' },
        { storeId: 'store-2', id: 'thread-2' },
      ] as InquiryThreadEntity[];
      mockInquiryThreadRepo.find.mockResolvedValue(mockThreads);

      const result = await inquiryThreadService.getThreadsByStoreIdsAndType(storeIds, threadType);

      expect(mockInquiryThreadRepo.find).toHaveBeenCalledWith({
        where: {
          storeId: In(storeIds),
          threadType,
        },
      });

      expect(result).toBeInstanceOf(Array);
      expect(result.length).toBe(2);
    });

    it('should return an empty map if no threads are found', async () => {
      const storeIds = ['store-1', 'store-2'];
      const threadType = ThreadType.CuramaThread;

      mockInquiryThreadRepo.find.mockResolvedValue([]);

      const result = await inquiryThreadService.getThreadsByStoreIdsAndType(storeIds, threadType);

      expect(mockInquiryThreadRepo.find).toHaveBeenCalledWith({
        where: {
          storeId: In(storeIds),
          threadType,
        },
      });

      expect(result).toBeInstanceOf(Array);
      expect(result.length).toBe(0);
    });
  });
});
