import { Test, TestingModule } from '@nestjs/testing';
import {
  AdminsStoresEntity,
  AttachmentEntity,
  CanaryReleaseStoreEntity,
  InquiryThreadEntity,
  MessageEntity,
  TeamsStoresEntity,
} from '@src/entities';
import { getDataSourceToken, getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { InquiryThreadController } from '@src/modules/shop/controllers/inquiry-thread.controller';
import { ConfigService } from '@nestjs/config';
import { ShopRequest } from '@modules/shop/interfaces/shop-request.interface';
import { ThreadType, ThreadTypeKey } from '@src/common/enums/thread-type';
import { AdminStoreService } from '@modules/shared/sync/services/admin-store.service';
import { TeamStoreService } from '@modules/shared/sync/services/team-store.service';
import { InquiryThreadService } from '@src/modules/shared/inquiry-thread/inquiry-thread.service';
import { MessageService } from '@src/modules/shared/message/message.service';
import { PromotionService, Py3Service } from '@vietnam/curama-py3-api';
import { MessageDto } from '@modules/shop/dtos/message.dto';
import { TeamService } from '@src/modules/shared/team/team.service';
import { ApiClientService } from '@vietnam/cnga-http-request';
import { SearchService } from '@modules/shared/search/services/search.service';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { DocumentService } from '@modules/shared/search/services/document.service';
import { getRedisToken } from '@liaoliaots/nestjs-redis';
import { AttachmentService } from '@src/modules/shared/attachment/attachment.service';

const mockedAmqpConnection = {
  publish: jest.fn(),
  request: jest.fn(),
  createChannel: jest.fn(),
} as unknown as AmqpConnection;

jest.mock('@src/main', () => ({
  FileTypeModule: {
    fileTypeFromBuffer: jest.fn(),
  },
}));
const transactionalEntityManager = {
  save: jest.fn().mockImplementation((entity) => jest.fn()),
};

const dataSource = {
  manager: {
    transaction: jest.fn().mockImplementation((callback) => {
      callback(transactionalEntityManager);
    }),
  },
  query: jest.fn(),
};

describe('InquiryThreadController', () => {
  let controller: InquiryThreadController;
  let inquiryThreadService: InquiryThreadService;
  let messageService: MessageService;
  let inquiryThreadRepo: Repository<InquiryThreadEntity>;
  let messageRepo: Repository<MessageEntity>;
  let documentService: DocumentService;
  const mockRedis = { set: jest.fn(), get: jest.fn(), del: jest.fn() };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [],
      providers: [
        InquiryThreadService,
        {
          provide: getRepositoryToken(InquiryThreadEntity),
          useClass: Repository,
        },
        {
          provide: getDataSourceToken('replicaConnection'),
          useValue: dataSource,
        },
        {
          provide: getRepositoryToken(AttachmentEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(MessageEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(AdminsStoresEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(TeamsStoresEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(CanaryReleaseStoreEntity),
          useClass: Repository,
        },
        {
          provide: DataSource,
          useValue: dataSource,
        },
        {
          provide: Py3Service,
          useValue: {},
        },
        {
          provide: ApiClientService,
          useValue: {},
        },
        {
          provide: PromotionService,
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {},
        },
        {
          provide: TeamService,
          useValue: {},
        },
        {
          provide: SearchService,
          useValue: {
            getServices: jest.fn(),
          },
        },
        { provide: getRedisToken('default'), useValue: mockRedis },
        {
          provide: AmqpConnection,
          useValue: mockedAmqpConnection,
        },
        {
          provide: AttachmentService,
          useValue: {
            uploadFile: jest.fn(),
          },
        },
        AdminStoreService,
        TeamStoreService,
      ],
    }).compile();

    messageRepo = module.get<Repository<MessageEntity>>(getRepositoryToken(MessageEntity));
    inquiryThreadRepo = module.get<Repository<InquiryThreadEntity>>(getRepositoryToken(InquiryThreadEntity));
    inquiryThreadService = module.get<InquiryThreadService>(InquiryThreadService);
    messageService = new MessageService(messageRepo, inquiryThreadRepo, documentService);
    controller = new InquiryThreadController(inquiryThreadService, messageService);
  });
  describe('ok', () => {
    it('ok', () => {
      expect('ok').toBeTruthy();
    });
  });

  describe('detail', () => {
    it('should return a detail of thread on mobile', async () => {
      const thread = <InquiryThreadEntity>(<unknown>{
        id: '4a89-b4d2-d0a1eca7-94a8-e8731a7cdcab',
        latestMessageTime: '2022-09-12T23:07:00',
        title: 'thread_title2',
        assignmentAdmin: { id: 'abc-4567-123-dcdf-3245345', name: 'admin name 2' },
        updatedAt: '2023-07-02T23:07:00',
      });
      const messages = {
        list: <any[]>[
          {
            id: '9f02edcb-e4bd-4ad8-8762-4fd549a59995',
            createdAt: new Date('2023-09-08T03:13:38.101Z'),
            updatedAt: new Date('2023-09-08T03:13:38.101Z'),
            threadId: '83de3fc1-913d-429f-b8f2-85f6f520df6a',
            content: 'demo message',
            type: 1,
            attachments: [],
          },
        ],
        pagination: { pageNumber: 1, total: 4, perPage: 2 },
      };

      jest.spyOn(inquiryThreadService, 'getById').mockResolvedValue(thread);
      jest.spyOn(inquiryThreadService, 'getThreadByType').mockResolvedValue(thread);
      jest.spyOn(messageService, 'getByInquiryThread').mockResolvedValue(messages);

      const request = {
        storeInfo: {
          store: {
            id: '307cbfe5-ec06-41bf-82cd-39c331fa78ba',
          },
        },
      } as ShopRequest;

      const result = await controller.detailMobile(ThreadTypeKey.CuramaThread, request);

      expect(inquiryThreadService.getThreadByType).toHaveBeenCalledWith(
        request.storeInfo.store.id,
        ThreadType.CuramaThread,
      );
      expect(messageService.getByInquiryThread).toHaveBeenCalledWith(thread.id);
      expect(result).toHaveProperty('thread', thread);
      expect(result).toHaveProperty('messages', messages);
    });

    it('should return a detail of thread on web', async () => {
      const thread = <InquiryThreadEntity>(<unknown>{
        id: '4a89-b4d2-d0a1eca7-94a8-e8731a7cdcab',
        latestMessageTime: '2022-09-12T23:07:00',
        title: 'thread_title2',
        assignmentAdmin: { id: 'abc-4567-123-dcdf-3245345', name: 'admin name 2' },
        updatedAt: '2023-07-02T23:07:00',
      });
      const messages = {
        list: <any[]>[
          {
            id: '9f02edcb-e4bd-4ad8-8762-4fd549a59995',
            createdAt: new Date('2023-09-08T03:13:38.101Z'),
            updatedAt: new Date('2023-09-08T03:13:38.101Z'),
            threadId: '83de3fc1-913d-429f-b8f2-85f6f520df6a',
            content: 'demo mesage',
            type: 1,
            attachments: [],
          },
        ],
        pagination: { pageNumber: 1, total: 4, perPage: 2 },
      };

      jest.spyOn(inquiryThreadService, 'getById').mockResolvedValue(thread);
      jest.spyOn(inquiryThreadService, 'getThreadByType').mockResolvedValue(thread);
      jest.spyOn(messageService, 'getByInquiryThread').mockResolvedValue(messages);

      const request = {
        storeInfo: {
          store: {
            id: '307cbfe5-ec06-41bf-82cd-39c331fa78ba',
          },
        },
      } as ShopRequest;

      const result = await controller.detailWeb(ThreadTypeKey.CuramaThread, request);

      expect(inquiryThreadService.getThreadByType).toHaveBeenCalledWith(
        request.storeInfo.store.id,
        ThreadType.CuramaThread,
      );
      expect(messageService.getByInquiryThread).toHaveBeenCalledWith(thread.id);
      expect(result).toHaveProperty('thread', thread);
      expect(result).toHaveProperty('messages', messages);
    });

    it('should return a detail of thread on web and empty message list', async () => {
      const thread = <InquiryThreadEntity>(<unknown>{
        id: '4a89-b4d2-d0a1eca7-94a8-e8731a7cdcab',
        latestMessageTime: '2022-09-12T23:07:00',
        title: 'thread_title2',
        assignmentAdmin: { id: 'abc-4567-123-dcdf-3245345', name: 'admin name 2' },
        updatedAt: '2023-07-02T23:07:00',
      });
      const messages = {
        list: <any[]>[],
        pagination: { pageNumber: 0, total: 0, perPage: 0 },
      };

      jest.spyOn(inquiryThreadService, 'getById').mockResolvedValue(thread);
      jest.spyOn(inquiryThreadService, 'getThreadByType').mockResolvedValue(thread);
      jest.spyOn(messageService, 'getByInquiryThread').mockResolvedValue(messages);

      const request = {
        storeInfo: {
          store: {
            id: '307cbfe5-ec06-41bf-82cd-39c331fa78ba',
          },
        },
      } as ShopRequest;

      const result = await controller.detailWeb(ThreadTypeKey.QualityThread, request);

      expect(inquiryThreadService.getThreadByType).toHaveBeenCalledWith(
        request.storeInfo.store.id,
        ThreadType.QualityThread,
      );
      expect(messageService.getByInquiryThread).toHaveBeenCalledWith(thread.id);
      expect(result).toHaveProperty('thread', thread);
      expect(result).toHaveProperty('messages', messages);
      expect(result).toHaveProperty('isShowMessageInput', false);
    });

    it('should return a detail of thread on mobile and empty message list', async () => {
      const thread = <InquiryThreadEntity>(<unknown>{
        id: '4a89-b4d2-d0a1eca7-94a8-e8731a7cdcab',
        latestMessageTime: '2022-09-12T23:07:00',
        title: 'thread_title2',
        assignmentAdmin: { id: 'abc-4567-123-dcdf-3245345', name: 'admin name 2' },
        updatedAt: '2023-07-02T23:07:00',
      });
      const messages = {
        list: <any[]>[],
        pagination: { pageNumber: 0, total: 0, perPage: 0 },
      };

      jest.spyOn(inquiryThreadService, 'getById').mockResolvedValue(thread);
      jest.spyOn(inquiryThreadService, 'getThreadByType').mockResolvedValue(thread);
      jest.spyOn(messageService, 'getByInquiryThread').mockResolvedValue(messages);

      const request = {
        storeInfo: {
          store: {
            id: '307cbfe5-ec06-41bf-82cd-39c331fa78ba',
          },
        },
      } as ShopRequest;

      const result = await controller.detailMobile(ThreadTypeKey.QualityThread, request);

      expect(inquiryThreadService.getThreadByType).toHaveBeenCalledWith(
        request.storeInfo.store.id,
        ThreadType.QualityThread,
      );
      expect(messageService.getByInquiryThread).toHaveBeenCalledWith(thread.id);
      expect(result).toHaveProperty('thread', thread);
      expect(result).toHaveProperty('messages', messages);
      expect(result).toHaveProperty('isShowMessageInput', false);
    });
  });

  describe('store()', () => {
    it('Create message and thread success', async () => {
      const request = <ShopRequest>{
        storeInfo: { store: { id: '08d14eaf-53fa-48dd-98f0-13d5e86c2003' } },
        path: 'current-path',
      };

      const messageRequest = <MessageDto>{
        threadId: undefined,
        threadTypeId: 1,
        message: 'demo message',
        attachments: [],
      };

      const resultExpect = {
        status: 200,
        success: true,
        message: 'success',
        data: {
          thread: {
            id: 'ef16ea98-5374-4936-9139-4fda025a77d4',
            latestMessageTime: '2023-09-02T23:07:00',
            title: 'thread_title1',
            assignmentAdmin: { id: '123-abc-4567-dcdf-3245345', name: 'admin name 1' },
            updatedAt: '2023-06-02T23:07:00',
          },
          message: [
            {
              id: '9f02edcb-e4bd-4ad8-8762-4fd549a59995',
              createdAt: new Date('2023-09-08T03:13:38.101Z'),
              updatedAt: new Date('2023-09-08T03:13:38.101Z'),
              threadId: '83de3fc1-913d-429f-b8f2-85f6f520df6a',
              content: 'demo message',
              type: 1,
              attachments: [],
            },
          ],
        },
        meta: {},
      };

      // @ts-ignore
      jest.spyOn(inquiryThreadService, 'getThreadByType').mockResolvedValue(undefined);

      jest.spyOn(inquiryThreadService, 'createMessageByStore').mockResolvedValue(resultExpect);

      const result = await controller.createMessageByStore(ThreadTypeKey.CuramaThread, messageRequest, [], request);

      expect(result).toEqual(resultExpect);
    });

    it('Create message and thread fail when thread type is quality', async () => {
      const request = <ShopRequest>{
        storeInfo: { store: { id: '08d14eaf-53fa-48dd-98f0-13d5e86c2003' } },
        path: 'current-path',
      };

      const messageRequest = <MessageDto>{
        threadId: undefined,
        threadTypeId: 2,
        message: 'demo message',
        attachments: [],
      };

      // @ts-ignore
      jest.spyOn(inquiryThreadService, 'getThreadByType').mockResolvedValue(undefined);

      const result = await controller.createMessageByStore(ThreadTypeKey.QualityThread, messageRequest, [], request);

      expect(result).toHaveProperty('status', 403);
    });
  });
});
