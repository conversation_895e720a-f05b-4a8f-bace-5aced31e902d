import { Test, TestingModule } from '@nestjs/testing';
import {
  AdminsStoresEntity,
  AttachmentEntity,
  CanaryReleaseStoreEntity,
  InquiryThreadEntity,
  MessageEntity,
  TeamsStoresEntity,
} from '@src/entities';
import { getDataSourceToken, getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { MessageController } from '@src/modules/shop/controllers/message.controller';
import { ConfigService } from '@nestjs/config';
import { MessageService } from '@src/modules/shared/message/message.service';
import { PromotionService, Py3Service } from '@vietnam/curama-py3-api';
import { CanaryReleaseStoreService } from '@src/modules/shared/canary-release-store/canary-release-store.service';
import { AdminStoreService } from '@src/modules/shared/sync/services/admin-store.service';
import { TeamStoreService } from '@src/modules/shared/sync/services/team-store.service';
import { InquiryThreadService } from '@src/modules/shared/inquiry-thread/inquiry-thread.service';
import { ThreadTypeKey } from '@src/common/enums/thread-type';
import { TeamService } from '@src/modules/shared/team/team.service';
import { ApiClientService } from '@vietnam/cnga-http-request';
import { SearchService } from '@modules/shared/search/services/search.service';
import { DocumentService } from '@modules/shared/search/services/document.service';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { getRedisToken } from '@liaoliaots/nestjs-redis';
import { AttachmentService } from '@src/modules/shared/attachment/attachment.service';

const mockedAmqpConnection = {
  publish: jest.fn(),
  request: jest.fn(),
  createChannel: jest.fn(),
} as unknown as AmqpConnection;

describe('MessageController', () => {
  let controller: MessageController;
  let messageService: MessageService;
  let messageRepo;
  let inquiryThreadRepo;
  let documentService: DocumentService;
  const mockRedis = { set: jest.fn(), get: jest.fn(), del: jest.fn() };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [],
      providers: [
        InquiryThreadService,
        {
          provide: getRepositoryToken(MessageEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(InquiryThreadEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(InquiryThreadEntity, 'replicaConnection'),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(TeamsStoresEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(AttachmentEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(AdminsStoresEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(CanaryReleaseStoreEntity),
          useClass: Repository,
        },
        {
          provide: DataSource,
          useValue: {},
        },
        {
          provide: getDataSourceToken('replicaConnection'),
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {},
        },
        {
          provide: Py3Service,
          useValue: {},
        },
        {
          provide: ApiClientService,
          useValue: {},
        },
        {
          provide: PromotionService,
          useValue: {},
        },
        {
          provide: TeamService,
          useValue: {},
        },
        {
          provide: CanaryReleaseStoreService,
          useClass: CanaryReleaseStoreService,
        },
        {
          provide: SearchService,
          useValue: {
            getServices: jest.fn(),
          },
        },
        { provide: getRedisToken('default'), useValue: mockRedis },
        {
          provide: AmqpConnection,
          useValue: mockedAmqpConnection,
        },
        {
          provide: AttachmentService,
          useValue: {
            uploadFile: jest.fn(),
          },
        },
        AdminStoreService,
        TeamStoreService,
      ],
    }).compile();

    messageRepo = module.get<Repository<MessageEntity>>(getRepositoryToken(MessageEntity));
    inquiryThreadRepo = module.get<Repository<InquiryThreadEntity>>(getRepositoryToken(InquiryThreadEntity));
    messageService = new MessageService(messageRepo, inquiryThreadRepo, documentService);
    const inquiryThreadService = module.get<InquiryThreadService>(InquiryThreadService);
    controller = new MessageController(inquiryThreadService, messageService);
  });

  describe('ok', () => {
    it('ok', () => {
      expect('ok').toBeTruthy();
    });
  });

  describe('getMessage()', () => {
    const listMessage = {
      list: [
        {
          id: 'a8e5003b-2b47-438e-a33f-d03943f0d563',
          createdAt: '2023-09-21T07:39:36.652Z',
          updatedAt: '2023-09-21T07:39:36.652Z',
          displayUpdatedAt: '2023年9月21日 16:39',
          threadId: '83de3fc1-913d-429f-b8f2-85f6f520df6a',
          content: 'Test message',
          fromAdminId: '3e9bc12c-0cab-495f-8836-99379a13d467',
          type: 1,
          deletedBy: null,
          deletedAt: null,
          attachments: [
            {
              id: '1e3f87a5-05f6-461c-bc1a-2a67a92f57d3',
              createdAt: '2023-09-21T07:39:32.471Z',
              updatedAt: '2023-09-21T07:39:32.471Z',
              displayUpdatedAt: '2023年9月21日 16:39',
              path: 'message/83de3fc1-913d-429f-b8f2-85f6f520df6a/1e3f87a5-05f6-461c-bc1a-2a67a92f57d3.png',
              attachmentType: 1,
              relationId: 'a8e5003b-2b47-438e-a33f-d03943f0d563',
              url: 'https://msqz6o892k.execute-api.ap-northeast-1.amazonaws.com/Prod/ip/message/83de3fc1-913d-429f-b8f2-85f6f520df6a/1e3f87a5-05f6-461c-bc1a-2a67a92f57d3.png?size=0x0&s=5fa49f265afad7400fe1c96b4a5409a3acfb5d3c',
            },
          ],
        },
        {
          id: '0da19d22-4ccd-45e3-9437-3bbeeda038c9',
          createdAt: '2023-09-21T09:42:45.584Z',
          updatedAt: '2023-09-21T09:42:45.584Z',
          displayUpdatedAt: '2023年9月21日 18:42',
          threadId: '83de3fc1-913d-429f-b8f2-85f6f520df6a',
          content: 'Demo message 2',
          fromAdminId: null,
          type: 1,
          deletedBy: null,
          deletedAt: null,
          attachments: [],
        },
      ],
      pagination: {
        perPage: 20,
        pageNumber: 1,
        total: 2,
      },
    };

    const mockRequest = {
      storeInfo: {
        store: {
          id: 'mockStoreId',
        },
      },
    };

    it('Get message success', async () => {
      // @ts-ignore
      jest.spyOn(messageService, 'getByInquiryThread').mockResolvedValue(listMessage);
      jest.spyOn(inquiryThreadRepo, 'findOne').mockResolvedValue({
        id: '83de3fc1-913d-429f-b8f2-85f6f520df6a',
      });

      const responseData = await controller.getMessages(ThreadTypeKey.CuramaThread, mockRequest as any);

      expect(responseData).toHaveProperty('data', listMessage.list);
      expect(responseData).toHaveProperty('meta', {
        pageNumber: 1,
        total: 2,
        perPage: 20,
      });
    });

    it('Get message with null thread id', async () => {
      // @ts-ignore
      jest.spyOn(messageService, 'getByInquiryThread').mockResolvedValue(listMessage);
      jest.spyOn(inquiryThreadRepo, 'findOne').mockResolvedValue(null);
      const responseData = await controller.getMessages(ThreadTypeKey.QualityThread, mockRequest as any);

      expect(responseData).toHaveProperty('data', []);
      expect(responseData).toHaveProperty('meta', {
        pageNumber: 0,
        total: 0,
        perPage: 0,
      });
    });
  });
});
