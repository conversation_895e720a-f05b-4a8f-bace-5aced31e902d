import { Test, TestingModule } from '@nestjs/testing';
import { InquiryThreadService } from '@src/modules/shared/inquiry-thread/inquiry-thread.service';
import { CanaryReleaseStoreService } from '@src/modules/shared/canary-release-store/canary-release-store.service';
import { InternalApiController } from '@src/modules/shop/controllers/internal-api.controller';

const mockCanaryReleaseStoreService = {
  checkRelease: jest.fn(),
};

const mockInquiryThreadService = {
  getLatestMessageForEachType: jest.fn(),
};

describe('InternalApiController', () => {
  let controller: InternalApiController;
  let inquiryThreadService: InquiryThreadService;
  let canaryReleaseStoreService: CanaryReleaseStoreService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [InternalApiController],
      providers: [
        {
          provide: InquiryThreadService,
          useValue: mockInquiryThreadService,
        },
        {
          provide: CanaryReleaseStoreService,
          useValue: mockCanaryReleaseStoreService,
        },
      ],
    }).compile();
    controller = module.get<InternalApiController>(InternalApiController);
    canaryReleaseStoreService = module.get<CanaryReleaseStoreService>(CanaryReleaseStoreService);
    inquiryThreadService = module.get<InquiryThreadService>(InquiryThreadService);
  });

  describe('getLatestMessage()', () => {
    it('should return a detail of thread and latest message', async () => {
      const storeId = '4a89-b4d2-d0a1eca7-94a8-e8731a7cdcab';
      const messages = [
        {
          thread_type: 1,
          latest_message: 'テストテスト',
          latest_time: '2023-08-01 10:20:01',
          is_read: true,
        },
        {
          thread_type: 2,
          latest_message: 'テストテスト',
          latest_time: '2023-08-01 10:20:01',
          is_read: true,
        },
      ];

      jest.spyOn(canaryReleaseStoreService, 'checkRelease').mockResolvedValue(true);
      jest.spyOn(inquiryThreadService, 'getLatestMessageForEachType').mockResolvedValue(messages);

      const result = await controller.getLatestMessages(storeId);

      expect(canaryReleaseStoreService.checkRelease).toHaveBeenCalledWith(storeId);
      expect(inquiryThreadService.getLatestMessageForEachType).toHaveBeenCalledWith(storeId);
      expect(typeof result.threads).toEqual('object');
    });

    it('should include default response when no messages are returned', async () => {
      const storeId = 'example-store-id';
      jest.spyOn(canaryReleaseStoreService, 'checkRelease').mockResolvedValue(true);
      jest.spyOn(inquiryThreadService, 'getLatestMessageForEachType').mockResolvedValue([]);
      const result = await controller.getLatestMessages(storeId);
      expect(result).toBeTruthy();
    });

    it('should prepend default response when no thread_type 1 is present', async () => {
      const storeId = 'example-store-id';
      const messages = [
        {
          thread_type: 2,
          latest_message: 'Message content',
          latest_time: '2023-08-01 10:20:01',
          is_read: true,
        },
      ];
      jest.spyOn(canaryReleaseStoreService, 'checkRelease').mockResolvedValue(true);
      jest.spyOn(inquiryThreadService, 'getLatestMessageForEachType').mockResolvedValue(messages);
      const result = await controller.getLatestMessages(storeId);
      expect(result).toBeTruthy();
    });
  });

  it('should return empty array when store is not released', async () => {
    const storeId = '4a89-b4d2-d0a1eca7-94a8-e8731a7cdcab';

    jest.spyOn(canaryReleaseStoreService, 'checkRelease').mockResolvedValue(false);

    const result = await controller.getLatestMessages(storeId);

    expect(canaryReleaseStoreService.checkRelease).toHaveBeenCalledWith(storeId);
    expect(result.threads).toEqual([]);
  });
});
