import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { Test, TestingModule } from '@nestjs/testing';
import { NotificationService } from '@src/modules/shared/notification/notification.service';
import { randomUUID } from 'crypto';
import { ACCOUNT_TYPES } from '@vietnam/cnga-middleware';
import { Request } from 'express';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';

describe('NotificationService', () => {
  let notificationService;
  const mockedAmqpConnection = {
    publish: jest.fn(),
  };

  const mockedHttpService = {
    post: jest.fn(),
  };

  const mockedRequest = {
    headers: {
      'x-request-id': randomUUID(),
    },
    terrySession: {
      id: randomUUID(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationService,
        {
          provide: AmqpConnection,
          useValue: mockedAmqpConnection,
        },
        {
          provide: HttpService,
          useValue: mockedHttpService,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue({ isEnable: true }),
          },
        },
      ],
    }).compile();

    notificationService = module.get<NotificationService>(NotificationService);
  });

  describe('pushNotiNewMessageToStore()', () => {
    it('success', async () => {
      mockedAmqpConnection.publish.mockResolvedValue({});
      const result = await notificationService.pushNotiNewMessageToStore(
        { threadType: 1, storeId: 'id' },
        mockedRequest as any,
      );
      expect(result).toBeTruthy();
    });
    it('fail', async () => {
      mockedAmqpConnection.publish.mockRejectedValue({});
      const result = await notificationService.pushNotiNewMessageToStore(
        { threadType: 2, storeId: 'id' },
        mockedRequest as any,
      );
      expect(result).toBeFalsy();
    });
  });

  describe('getNotificationHeaders()', () => {
    it('should return headers from Request', () => {
      const request = {
        headers: {
          'x-request-id': 'test-request-id',
        },
        terrySession: {
          adminId: 'test-admin-id',
        },
      } as unknown as Request;

      const result = notificationService.getNotificationHeaders(request);

      expect(result).toEqual({
        'x-request-id': 'test-request-id',
        'x-client-id': 'test-admin-id',
        'x-client-type': ACCOUNT_TYPES.Admin,
      });
    });

    it('should handle missing terrySession in Request', () => {
      const request = {
        headers: {
          'x-request-id': 'test-request-id',
        },
      } as unknown as Request;

      const result = notificationService.getNotificationHeaders(request);

      expect(result).toEqual({
        'x-request-id': 'test-request-id',
        'x-client-id': undefined,
        'x-client-type': ACCOUNT_TYPES.Admin,
      });
    });
  });
});
