import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { of, throwError } from 'rxjs';
import { AxiosResponse } from 'axios';
import { SlackService } from '@src/modules/shared/notification/slack.service';

describe('SlackService', () => {
  let service: SlackService;
  let configService: jest.Mocked<ConfigService>;
  let httpService: jest.Mocked<HttpService>;

  beforeEach(async () => {
    configService = {
      get: jest.fn(),
    } as unknown as jest.Mocked<ConfigService>;

    httpService = {
      post: jest.fn(),
    } as unknown as jest.Mocked<HttpService>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SlackService,
        { provide: ConfigService, useValue: configService },
        { provide: HttpService, useValue: httpService },
      ],
    }).compile();

    service = module.get<SlackService>(SlackService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendSlackNotification', () => {
    it('should send a notification successfully', async () => {
      const mockConfig = { slackWebhookUrl: 'https://hooks.slack.com/services/test' };
      const mockMessage = 'Test notification';
      const mockResponse = { data: 'ok' } as AxiosResponse;

      configService.get.mockReturnValue(mockConfig);
      httpService.post.mockReturnValue(of(mockResponse));

      await service.sendSlackNotification(mockMessage);

      expect(configService.get).toHaveBeenCalledWith('notification');
      expect(httpService.post).toHaveBeenCalledWith(mockConfig.slackWebhookUrl, {
        channel: '#general',
        text: mockMessage,
      });
    });

    it('should log an error if the HTTP request fails', async () => {
      const mockConfig = { slackWebhookUrl: 'https://hooks.slack.com/services/test' };
      const mockMessage = 'Test notification';
      const mockError = new Error('Request failed');

      configService.get.mockReturnValue(mockConfig);
      httpService.post.mockReturnValue(throwError(() => mockError));

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      await service.sendSlackNotification(mockMessage);

      expect(configService.get).toHaveBeenCalledWith('notification');
      expect(httpService.post).toHaveBeenCalledWith(mockConfig.slackWebhookUrl, {
        channel: '#general',
        text: mockMessage,
      });
      expect(loggerSpy).toHaveBeenCalledWith(mockError.message, expect.any(String));
    });
  });
});
