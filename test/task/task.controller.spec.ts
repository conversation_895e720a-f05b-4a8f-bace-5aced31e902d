import { CURAMA_AUTHORIZATION_SERVICE, CURAMA_AUTH_SERVICE } from '@curama-auth/constants';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { AdminService } from '@src/modules/shared/admin/admin.service';
import { Py3Service } from '@vietnam/curama-py3-api';
import { TaskService } from '@src/modules/shared/task/task.service';
import { TeamService } from '@src/modules/shared/team/team.service';
import { WorkTypeService } from '@src/modules/shared/work-type/work-type.service';
import { TaskController } from '@src/modules/terry/controllers/task.controller';
import { I18nContext } from 'nestjs-i18n';

describe('TaskController', () => {
  let controller: TaskController;
  let mockI18nContext: jest.Mocked<I18nContext>;
  const mockedTaskService = {
    getTaskWithPaginate: jest.fn(),
    getListOfTask: jest.fn(),
    getDataForCreate: jest.fn(),
    getDataForUpdate: jest.fn(),
    upsertTask: jest.fn(),
    getPagingTasks: jest.fn(),
  };
  const mockedAuthService = {
    getOauth2Url: jest.fn(),
    signOut: jest.fn(),
    authenticate: jest.fn(),
  };
  const mockWorkTypeService = {
    find: jest.fn(),
  };

  const mockAdminService = {
    find: jest.fn(),
  };

  const mockTeamService = {
    find: jest.fn(),
  };

  const configService = {
    get: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TaskController],
      providers: [
        TaskService,
        {
          provide: Py3Service,
          useValue: {},
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: mockedAuthService,
        },
        {
          provide: ConfigService,
          useValue: configService,
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
        {
          provide: WorkTypeService,
          useValue: mockWorkTypeService,
        },
        {
          provide: AdminService,
          useValue: mockAdminService,
        },
        {
          provide: TeamService,
          useValue: mockTeamService,
        },
      ],
    })
      .overrideProvider(TaskService)
      .useValue(mockedTaskService)
      .compile();
    controller = module.get<TaskController>(TaskController);
    mockI18nContext = {
      t: jest.fn().mockReturnValue('Translated string'),
      lang: 'en',
      service: {} as any,
      translate: jest.fn(),
      validate: jest.fn(),
    } as any;
  });

  describe('getListOfTask', () => {
    it('success', async () => {
      const mockRequest = {
        query: {},
      } as any;
      const mockUser = {
        id: 'admin-id',
        teams: [{ id: 'team-id' }],
      } as any;
      const tasks = { tasks: [], request: mockRequest };
      mockedTaskService.getListOfTask.mockResolvedValue(tasks);
      mockI18nContext.validate.mockResolvedValue([]);
      const result = await controller.getListOfTask(mockRequest, mockI18nContext, mockUser);
      expect(mockedTaskService.getListOfTask).toHaveBeenCalledWith(expect.any(Object), mockRequest);
      expect(result).toBeTruthy();
    });

    it('invalid UUID', async () => {
      const query = { storeId: 'invalidUUID' };
      const mockRequest = {
        query,
      } as any;
      const mockUser = {
        id: 'admin-id',
        teams: [{ id: 'team-id' }],
      } as any;
      mockedTaskService.getListOfTask.mockResolvedValue({});
      mockI18nContext.validate.mockResolvedValue([
        {
          property: 'storeId',
          constraints: { isUUID: 'storeId must be an UUID' },
        },
      ]);
      const result = await controller.getListOfTask(mockRequest, mockI18nContext, mockUser);
      expect(result.errors).toBeTruthy();
    });
  });

  describe('getDataForCreateTask', () => {
    it('success', async () => {
      const mockUser = {
        id: 'admin-id',
        teams: [{ id: 'team-id' }],
      } as any;
      mockedTaskService.getDataForCreate.mockResolvedValue({});
      const result = await controller.getDataForCreateTask(mockUser);
      expect(result).toBeTruthy();
      expect(mockedTaskService.getDataForCreate).toHaveBeenCalledWith(mockUser);
    });
  });

  describe('getDataForUpdate', () => {
    it('success', async () => {
      mockedTaskService.getDataForUpdate.mockResolvedValue({});
      const result = await controller.getDataForUpdate('id');
      expect(result).toBeTruthy();
    });
  });

  describe('upsertTask', () => {
    it('success', async () => {
      mockedTaskService.upsertTask.mockResolvedValue({} as any);
      await controller.upsertTask({} as any, {} as any);
    });
  });
  describe('getPagingTasks', () => {
    it('success', async () => {
      mockedTaskService.getPagingTasks.mockResolvedValue([]);
      const result = await controller.getPagingTasks({} as any);
      expect(result).toBeTruthy();
    });
  });
});
