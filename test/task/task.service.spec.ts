import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { TaskEntity } from '@src/entities/task.entity';
import { AdminService } from '@src/modules/shared/admin/admin.service';
import { InquiryThreadService } from '@src/modules/shared/inquiry-thread/inquiry-thread.service';
import { TasksQueryRequest } from '@src/modules/shared/task/dtos/tasks-query-request.do';
import { TaskService } from '@src/modules/shared/task/task.service';
import { TeamService } from '@src/modules/shared/team/team.service';
import { WorkTypeService } from '@src/modules/shared/work-type/work-type.service';
import { DataSource, Repository } from 'typeorm';
import { Request } from 'express';
import { HttpResponseFormatter } from '@common/response/response';

describe('TaskService', () => {
  let service: TaskService;
  let taskRepository: Repository<TaskEntity>;

  const mockDataSource = {
    transaction: jest.fn(),
  };

  const mockTeamService = { getTeams: jest.fn(), find: jest.fn() };
  const mockAdminService = { all: jest.fn(), find: jest.fn() };
  const mockWorkTypeService = {
    all: jest.fn(),
    getById: jest.fn(),
    find: jest.fn(),
  };
  const mockInquiryThreadService = {
    getOrCreateThread: jest.fn(),
    saveThread: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TaskService,
        { provide: getRepositoryToken(TaskEntity), useClass: Repository },
        { provide: TeamService, useValue: mockTeamService },
        { provide: AdminService, useValue: mockAdminService },
        { provide: WorkTypeService, useValue: mockWorkTypeService },
        { provide: InquiryThreadService, useValue: mockInquiryThreadService },
        { provide: DataSource, useValue: mockDataSource },
      ],
    }).compile();

    service = module.get<TaskService>(TaskService);
    taskRepository = module.get<Repository<TaskEntity>>(getRepositoryToken(TaskEntity));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getTaskWithPaginate', () => {
    it('should return list of task', async () => {
      const searchQueryMockData = {
        pageNumber: 1,
        perPage: 10,
        updatedAt: '2023-09-02T23:07:00',
        offset: 0,
        limit: 10,
        sortByNextConfirmationDate: 'ASC',
        storeId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
        assignmentTeamId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
        assignmentAdminId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
        workTypeId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
        status: 1,
        nextConfirmationDate: '2023-09-02',
        orderNextConfirmationDate: 1,
      };
      const pageNumber = 1;
      const perPage = 10;
      const list = [
        {
          id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a777',
          name: 'name',
          description: 'description',
          workTypeId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          memo: 'memo',
          assignmentAdminId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          assignmentTeamId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          nextConfirmationDate: '2023-09-02T23:07:00',
          taskNo: 'taskNo',
          status: 1,
          updatedAt: '2023-09-02T23:07:00',
          createdAt: '2023-09-02T23:07:00',
          displayUpdatedAt: '2023年7月24日',
        },
        {
          id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          name: 'name',
          description: 'description',
          workTypeId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          memo: 'memo',
          assignmentAdminId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          assignmentTeamId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          nextConfirmationDate: '2023-09-02T23:07:00',
          taskNo: 'taskNo',
          status: 1,
          updatedAt: '2023-09-02T23:07:00',
          createdAt: '2023-09-02T23:07:00',
          displayUpdatedAt: '2023年7月24日',
        },
      ];
      const total = 2;

      const createQueryBuilder: any = {
        select: () => createQueryBuilder,
        where: () => createQueryBuilder,
        andWhere: () => createQueryBuilder,
        orderBy: () => createQueryBuilder,
        leftJoinAndSelect: () => createQueryBuilder,
        getManyAndCount: () => [list, total],
        getCount: () => total,
        skip: () => createQueryBuilder,
        take: () => createQueryBuilder,
        getMany: () => list,
      };

      jest.spyOn(taskRepository, 'createQueryBuilder').mockReturnValue(createQueryBuilder);

      const result = await service.getTaskWithPaginate(searchQueryMockData);

      expect(result.tasks).toEqual(list);
      expect(result.pagination).toEqual({
        pageNumber,
        total,
        perPage,
      });
      expect(taskRepository.createQueryBuilder).toHaveBeenCalled();
    });
  });

  describe('findAndCount', () => {
    it('success', async () => {
      jest.spyOn(taskRepository, 'findAndCount').mockResolvedValue({} as any);
      const result = await service.findAndCount({});
      expect(result).toBeTruthy();
    });
  });
  describe('countTotal', () => {
    it('success', async () => {
      jest.spyOn(taskRepository, 'countBy').mockResolvedValue(8);
      const result = await service.countTotal('id');
      expect(result).toBeTruthy();
    });
  });
  describe('generateTaskNo', () => {
    it('should generate a unique task number for a given thread ID', async () => {
      const threadId = 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778';
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ maxTaskNo: 0 }),
      };
      jest.spyOn(taskRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);
      const result = await service.generateTaskNo(threadId);
      expect(result).toBe(1);
    });

    it('should increment the task number if there are existing tasks for the given thread ID', async () => {
      const threadId = 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778';
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ maxTaskNo: 1 }),
      };
      jest.spyOn(taskRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);
      const result = await service.generateTaskNo(threadId);
      expect(result).toBe(2);
    });
  });

  describe('getDataForCreate', () => {
    it('should return the correct data for creating a task', async () => {
      const user = {
        id: 'admin-id',
        teams: [{ id: 'team-id' }],
      };

      const staticData = {
        status: ['InProgress', 'Completed'],
        teams: [
          {
            id: 'team-id',
            name: 'test team 1',
          },
          {
            id: 'team-id-2',
            name: 'test team 2',
          },
        ],
        workTypes: [
          {
            id: 'work-type-id-1',
            name: 'test work type 1',
          },
          {
            id: 'work-type-id-2',
            name: 'test work type 2',
          },
        ],
        admins: [
          {
            id: 'admin-id-1',
            name: 'test admin 1',
            teams: [
              {
                id: 'team-id',
                name: 'test team 1',
              },
            ],
          },
          {
            id: 'admin-id-2',
            name: 'test admin 2',
            teams: [
              {
                id: 'team-id-2',
                name: 'test team 2',
              },
            ],
          },
        ],
      } as any;

      jest.spyOn(service, 'getStaticDataForUpsert').mockResolvedValue(staticData);

      const result = await service.getDataForCreate(user as any);

      expect(result).toEqual(
        HttpResponseFormatter.formatSuccessResponse({
          data: {
            status: staticData.status,
            teams: staticData.teams,
            workTypes: staticData.workTypes,
            admins: [staticData.admins[0]],
            teamId: 'team-id',
            adminId: 'admin-id',
          },
        }),
      );
    });

    it('should handle errors', async () => {
      const user = {
        id: 'admin-id',
        teams: [{ id: 'team-id' }],
      };
      jest.spyOn(service, 'getStaticDataForUpsert').mockRejectedValue(new Error('Some error'));
      const result = await service.getDataForCreate(user as any);
      expect(result).toEqual(HttpResponseFormatter.responseInternalError({ errors: 'Some error' }));
    });
  });

  describe('getDataForUpdate', () => {
    it('success', async () => {
      const taskId = 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778';
      const task = {
        id: taskId,
        taskNo: 1,
        memo: 'test memo',
        nextConfirmationDate: '2023-09-02',
        assignmentTeamId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
        workType: {
          id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          name: 'test work type',
        },
      };
      const staticData = {
        status: ['InProgress', 'Completed'],
        teams: [
          {
            id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
            name: 'test team 1',
          },
          {
            id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a779',
            name: 'test team 2',
          },
        ],
        workTypes: [
          {
            id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
            name: 'test work type 1',
          },
          {
            id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a779',
            name: 'test work type 2',
          },
        ],
        admins: [
          {
            id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
            name: 'test admin 1',
            teams: [
              {
                id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
                name: 'test team 1',
              },
            ],
          },
          {
            id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a779',
            name: 'test admin 2',
            teams: [
              {
                id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a779',
                name: 'test team 2',
              },
            ],
          },
        ],
      };

      jest.spyOn(taskRepository, 'findOne').mockResolvedValue(task as any);
      jest.spyOn(service, 'getStaticDataForUpsert').mockResolvedValue(staticData as any);

      const result: any = await service.getDataForUpdate(taskId);

      expect(result.task).toEqual(task);
      expect(result.status).toEqual(staticData.status);
      expect(result.teams).toEqual(staticData.teams);
      expect(result.workTypes).toEqual(staticData.workTypes);
      expect(result.admins).toEqual([
        {
          id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          name: 'test admin 1',
          teams: [
            {
              id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
              name: 'test team 1',
            },
          ],
        },
      ]);
    });

    it('fail', async () => {
      const taskId = 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778';
      jest.spyOn(taskRepository, 'findOne').mockRejectedValueOnce('error');
      const result = await service.getDataForUpdate(taskId);
      expect(result).toBeTruthy();
    });
  });

  describe('getPagingTasks()', () => {
    it('success', async () => {
      jest.spyOn(service, 'findAndCount').mockResolvedValueOnce([[{} as any], 1]);
      const result = await service.getPagingTasks({ withCompleted: 'true' } as any);
      expect(result).toBeTruthy();
    });

    it('fail', async () => {
      jest.spyOn(service, 'findAndCount').mockRejectedValueOnce('error');
      const result = await service.getPagingTasks({ withCompleted: 'false' } as any);
      expect(result).toBeTruthy();
    });
  });

  describe('getListOfTask', () => {
    const searchQueryMockData = <TasksQueryRequest>{
      storeId: '123',
      assignmentTeamId: '',
      assignmentAdminId: '',
      status: 1,
      updatedAt: '',
      nextConfirmationDate: '',
      workTypeId: '',
      orderNextConfirmationDate: 1,
      offset: 0,
      pageNumber: 1,
      perPage: 10,
    };
    it('success', async () => {
      jest.spyOn(service, 'getTaskWithPaginate').mockResolvedValueOnce({} as any);

      const result = await service.getListOfTask(searchQueryMockData, {
        path: 'current-path',
        query: {},
      } as Request);
      expect(result).toBeTruthy();
      expect(service.getTaskWithPaginate).toHaveBeenCalled();
    });
    it('fail', async () => {
      jest.spyOn(service, 'getTaskWithPaginate').mockRejectedValueOnce('error');
      const result = await service.getListOfTask(searchQueryMockData, {
        path: 'current-path',
        query: {},
      } as Request);
      expect(result).toBeTruthy();
    });
  });

  describe('upsertTask', () => {
    const mockEntityManager = {
      create: jest.fn().mockResolvedValueOnce({}),
      update: jest.fn().mockResolvedValueOnce({}),
      delete: jest.fn().mockResolvedValueOnce({}),
      save: jest.fn().mockResolvedValue({ id: 'id', workType: { name: 'string' }, memo: 'string' }),
      findOne: jest
        .fn()
        .mockResolvedValueOnce({ id: 'id', workType: { name: 'string' }, memo: 'string' })
        .mockResolvedValue({ id: 'id', nextConfirmationDate: '2023-09-02' }),
      findOneBy: jest.fn().mockResolvedValue({ id: 'id', taskNo: 'string', workTypeId: 'id' }),
      createQueryBuilder: jest.fn().mockResolvedValueOnce({}),
    };
    it('taskNo', async () => {
      jest.spyOn(service, 'generateTaskNo').mockResolvedValueOnce(1);
      const result = await service.generateTaskNo('id');
      expect(result).toBeTruthy();
    });

    it('success-create', async () => {
      mockInquiryThreadService.getOrCreateThread.mockResolvedValueOnce({ id: '1' });
      mockInquiryThreadService.saveThread.mockResolvedValueOnce({ id: '1' });
      mockDataSource.transaction.mockImplementationOnce(async (callback) => {
        return await callback(mockEntityManager);
      });
      service.generateTaskNo = jest.fn().mockResolvedValueOnce(1);
      mockWorkTypeService.getById.mockResolvedValueOnce({ id: '1', name: 'AIメッセージパトロール' });
      const result = await service.upsertTask(
        {
          workTypeId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          status: 1,
          assignmentTeamId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          assignmentAdminId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          nextConfirmationDate: '2023-09-02',
          memo: 'memo',
          storeId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          storeName: 'storeName',
          threadType: 1,
        } as any,
        'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
      );

      expect(result).toBeTruthy();
    });

    it('success-create', async () => {
      mockInquiryThreadService.getOrCreateThread.mockResolvedValueOnce({ id: '1' });
      mockInquiryThreadService.saveThread.mockResolvedValueOnce({ id: '1' });
      mockDataSource.transaction.mockImplementationOnce(async (callback) => {
        return await callback(mockEntityManager);
      });
      service.generateTaskNo = jest.fn().mockResolvedValueOnce(1);
      mockWorkTypeService.getById.mockResolvedValueOnce({ id: '1', name: 'AIメッセージパトロール' });
      const result = await service.upsertTask(
        {
          workTypeId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          status: 1,
          assignmentTeamId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          storeId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          storeName: 'storeName',
          threadType: 1,
        } as any,
        'id',
      );
      expect(result).toBeTruthy();
    });

    it('success-update', async () => {
      mockInquiryThreadService.getOrCreateThread.mockResolvedValueOnce({ id: '1' });
      mockInquiryThreadService.saveThread.mockResolvedValueOnce({ id: '1' });
      mockDataSource.transaction.mockImplementationOnce(async (callback) => {
        return await callback(mockEntityManager);
      });
      service.generateTaskNo = jest.fn().mockResolvedValueOnce(1);

      const result = await service.upsertTask(
        {
          id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          workTypeId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          status: 1,
          assignmentTeamId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          assignmentAdminId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          nextConfirmationDate: '2023-09-02',
          memo: 'memo',
          storeId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          storeName: 'storeName',
          threadType: 1,
        } as any,
        'id',
      );

      expect(result).toBeTruthy();
    });

    it('success-update', async () => {
      mockInquiryThreadService.getOrCreateThread.mockResolvedValueOnce({ id: '1' });
      mockInquiryThreadService.saveThread.mockResolvedValueOnce({ id: '1' });
      mockDataSource.transaction.mockImplementationOnce(async (callback) => {
        return await callback(mockEntityManager);
      });
      service.generateTaskNo = jest.fn().mockResolvedValueOnce(1);

      const result = await service.upsertTask(
        {
          id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          workTypeId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          status: 1,
          assignmentTeamId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          storeId: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          storeName: 'storeName',
          threadType: 1,
        } as any,
        'id',
      );
      expect(result).toBeTruthy();
    });

    it('fail', async () => {
      mockInquiryThreadService.getOrCreateThread.mockResolvedValueOnce({ id: '1' });
      mockDataSource.transaction.mockRejectedValueOnce('error');
      service.generateTaskNo = jest.fn().mockResolvedValueOnce(1);
      const result = await service.upsertTask({} as any, 'id');
      expect(result).toBeTruthy();
    });
  });
});
