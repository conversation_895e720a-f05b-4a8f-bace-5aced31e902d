import { Test, TestingModule } from '@nestjs/testing';
import { NgWordEntity } from '@src/entities';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Request } from 'express';
import { NgWordController } from '@src/modules/terry/controllers/ng-word.controller';
import { NgWordService } from '@src/modules/shared/ng-word/ng-word.service';
import { Py3Service } from '@vietnam/curama-py3-api';
import { NgWordDto } from '@src/modules/shared/ng-word/dtos/ng-word.dto';
import { CURAMA_AUTHORIZATION_SERVICE, CURAMA_AUTH_SERVICE } from '@curama-auth/constants';
import { ConfigService } from '@nestjs/config';

describe('NgWordController', () => {
  let controller: NgWordController;
  let ngWordService: NgWordService;
  const mockedAuthService = {
    getOauth2Url: jest.fn(),
    signOut: jest.fn(),
    authenticate: jest.fn(),
  };

  const configService = {
    get: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NgWordController],
      providers: [
        NgWordService,
        {
          provide: getRepositoryToken(NgWordEntity),
          useClass: Repository,
        },
        {
          provide: Py3Service,
          useValue: {},
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: mockedAuthService,
        },
        {
          provide: ConfigService,
          useValue: configService,
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<NgWordController>(NgWordController);
    ngWordService = module.get<NgWordService>(NgWordService);
  });

  describe('getListNgWord', () => {
    it('should return a list of ng word', async () => {
      const request = <Request>{};
      const page = 1;
      const listNgWord = <NgWordEntity[]>(<unknown>[
        {
          id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
          content: 'thread_title1',
          updatedAt: '2023-06-02T23:07:00',
        },
        {
          id: '4a89-b4d2-d0a1eca7-94a8-e8731a7cdcab',
          content: 'thread_title2',
          updatedAt: '2023-07-02T23:07:00',
        },
      ]);

      jest.spyOn(ngWordService, 'getListNgWord').mockResolvedValue(listNgWord);

      const result = await controller.listNgWord(request, page);

      expect(ngWordService.getListNgWord).toHaveBeenCalledWith(request, page);
      expect(result).toEqual({
        listNgWord,
      });
    });
  });

  describe('createNgWord', () => {
    it('should create new ng word', async () => {
      const resultExpect = {
        _csrf: '9qtFZ9OM-nFAIMxqYDT6L502T9ZtFJgA6-SY',
        status: 200,
        success: true,
        data: {
          id: 'ef16ea98-5374-4936-9139-4fda025a77d4',
          content: 'thread_title1',
          updatedAt: '2023-06-02T23:07:00',
        },
      };
      const ngWordRequest = <NgWordDto>{
        content: 'thread_title1',
      };

      // @ts-ignore
      jest.spyOn(ngWordService, 'save').mockResolvedValue(resultExpect);

      const result = await controller.store(ngWordRequest);

      expect(ngWordService.save).toHaveBeenCalledWith(ngWordRequest);
      expect(result).toEqual(resultExpect);
    });
  });

  describe('deleteNgWord', () => {
    it('should delete ng word', async () => {
      const result = await controller.delete('id');
      expect(result).toBeTruthy();
    });
  });
});
