import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { NgWordEntity } from '@src/entities';
import { Request } from 'express';
import { Repository } from 'typeorm';
import { PAGINATOR } from '@src/common/constants/paginator';
import { NgWordService } from '@src/modules/shared/ng-word/ng-word.service';
import { NgWordDto } from '@src/modules/shared/ng-word/dtos/ng-word.dto';

describe('NgWordService', () => {
  let ngWordService: NgWordService;
  let ngWordEntity: Repository<NgWordEntity>;
  let ngWordRepo: Repository<NgWordEntity>;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NgWordService,
        {
          provide: getRepositoryToken(NgWordEntity),
          useClass: Repository,
        },
      ],
    }).compile();

    ngWordService = module.get<NgWordService>(NgWordService);
    ngWordRepo = module.get<Repository<NgWordEntity>>(getRepositoryToken(NgWordEntity));
    ngWordEntity = module.get<Repository<NgWordEntity>>(getRepositoryToken(NgWordEntity));
  });

  describe('getListNgWord', () => {
    it('should get the list of ng word with page > 0', async () => {
      const request = <Request>{ path: 'current-path' };
      const page = 1;
      const perPage = PAGINATOR.NGWORD;
      const pageNumber = 1;
      const list = <NgWordEntity[]>(<unknown>[
        {
          id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
          content: 'Test',
          updatedAt: '2023-06-02T23:07:00',
        },
        {
          id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
          content: 'Load',
          updatedAt: '2023-06-02T23:07:00',
        },
      ]);
      const total = 2;

      // Mock the findAndCount method of the ngWordEntity
      jest.spyOn(ngWordEntity, 'findAndCount').mockResolvedValue([list, total]);

      const result = await ngWordService.getListNgWord(request, page);

      // @ts-ignore
      expect(result.list).toEqual(list);
      // @ts-ignore
      expect(result.pagination).toEqual({
        pageNumber,
        total,
        perPage,
        request,
      });
      expect(ngWordEntity.findAndCount).toHaveBeenCalledWith({
        take: perPage,
        skip: 0,
        order: {
          createdAt: 'DESC',
        },
      });
    });
    it('should get the list of ng word with page = 0', async () => {
      const request = <Request>{ path: 'current-path' };
      const page = 0;
      const perPage = PAGINATOR.NGWORD;
      const pageNumber = 1;
      const list = <NgWordEntity[]>(<unknown>[
        {
          id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
          content: 'Test',
          updatedAt: '2023-06-02T23:07:00',
        },
        {
          id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
          content: 'Load',
          updatedAt: '2023-06-02T23:07:00',
        },
      ]);
      const total = 2;

      // Mock the findAndCount method of the inquiryThreadEntity
      jest.spyOn(ngWordEntity, 'findAndCount').mockResolvedValue([list, total]);

      const result = await ngWordService.getListNgWord(request, page);

      // @ts-ignore
      expect(result.list).toEqual(list);
      // @ts-ignore
      expect(result.pagination).toEqual({
        pageNumber,
        total,
        perPage,
        request,
      });
      expect(ngWordEntity.findAndCount).toHaveBeenCalledWith({
        take: perPage,
        skip: 0,
        order: {
          createdAt: 'DESC',
        },
      });
    });
  });
  describe('createNgWord', () => {
    it('should return error if ng word already exists', async () => {
      const ngWordRequest = <NgWordDto>{
        content: 'test',
      };
      const ngWord = <NgWordEntity>(<unknown>{
        id: 'd0a1eca7-94a8-4a89-b4d2-e8731a7cdcab',
        content: 'test',
        updatedAt: '2023-06-02T23:07:00',
      });
      jest.spyOn(ngWordRepo, 'findOne').mockResolvedValue(ngWord);
      const result = await ngWordService.save(ngWordRequest);
      expect(result).toEqual({
        status: 400,
        message: 'NGワードは既に存在しています。',
        errors: undefined,
        success: false,
      });
    });

    it('should save ng word successfully', async () => {
      const ngWordRequest = <NgWordDto>{ content: 'thread_title1' };

      jest.spyOn(ngWordRepo, 'findOne').mockResolvedValue(null);
      jest.spyOn(ngWordRepo, 'save').mockResolvedValue(null);
      await ngWordService.save(ngWordRequest);

      expect(ngWordRepo.findOne).toHaveBeenCalled();
      expect(ngWordRepo.save).toHaveBeenCalled();
    });
  });

  describe('deleteNgWord', () => {
    it('success', async () => {
      jest.spyOn(ngWordRepo, 'delete').mockResolvedValue({} as any);
      const result = await ngWordService.delete('id');

      expect(result).toBeTruthy();
    });

    it('fail', async () => {
      jest.spyOn(ngWordRepo, 'delete').mockRejectedValue('error');
      const result = await ngWordService.delete('id');
      expect(result).toBeTruthy();
    });
  });
  describe('checkNGWord', () => {
    it('should return array of ng words', async () => {
      const createQueryBuilder: any = {
        select: () => createQueryBuilder,
        where: () => createQueryBuilder,
        getRawMany: () => [{ content: 'a' }, { content: 'b' }],
      };
      jest.spyOn(ngWordRepo, 'createQueryBuilder').mockImplementation(() => createQueryBuilder);

      const result = await ngWordService.checkNGWord('abc');
      expect(result).toBeTruthy();
    });
  });
});
