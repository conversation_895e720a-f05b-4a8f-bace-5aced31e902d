import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getRedisToken } from '@liaoliaots/nestjs-redis';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { HttpService } from '@nestjs/axios';
import { Readable } from 'stream';
import { Between, DataSource, EntityManager, In, Repository } from 'typeorm';
import { BroadcastMessageEntity } from '@src/entities/broadcast-message.entity';
import { AttachmentEntity } from '@src/entities/attachment.entity';
import { StoreEntity } from '@src/entities/store.entity';
import {
  BroadcastMessageStatus,
  BULLMQ_MESSAGE_BROADCAST_QUEUE,
  BULLMQ_JOB_SEND_MESSAGE_BATCH,
} from '@common/constants';
import { BroadcastMessageService } from '@src/modules/shared/broadcast-message/broadcast-message.service';
import { AttachmentService } from '@src/modules/shared/attachment/attachment.service';
import { SlackService } from '@src/modules/shared/notification/slack.service';
import { v4 as uuidv4 } from 'uuid';
import { AxiosResponse } from 'axios';
import { of } from 'rxjs';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { SaveTemplateDto } from '@src/modules/shared/broadcast-message/dtos/save-template.dto';
import { getQueueToken } from '@nestjs/bullmq';

declare global {
  interface String {
    escape(): string;
    urlize(): string;
    nl2br(): string;
  }
}
String.prototype.escape = jest.fn().mockReturnValue('');
String.prototype.urlize = jest.fn().mockReturnValue('');
String.prototype.nl2br = jest.fn().mockReturnValue('');

jest.mock('@src/common/utils/image-handler');
describe('BroadcastMessageService - Full Coverage', () => {
  let service: BroadcastMessageService;
  let storeRepository: Repository<StoreEntity>;
  let attachmentRepository: Repository<AttachmentEntity>;
  let broadcastMessageRepository: Repository<BroadcastMessageEntity>;
  let entityManager: jest.Mocked<EntityManager>;
  let httpService: HttpService;
  let redis: any;

  const mockAmqp = { publish: jest.fn() };
  const mockDataSource = { transaction: jest.fn() };
  const mockAttachmentService = { uploadFile: jest.fn() };
  const mockRedis = { set: jest.fn(), get: jest.fn(), del: jest.fn(), zadd: jest.fn() };
  const mockSlackService = {
    sendSlackNotification: jest.fn(),
  };
  const mockBullMQQueue = {
    add: jest.fn(),
    process: jest.fn(),
    on: jest.fn(),
  };

  beforeEach(async () => {
    entityManager = {
      insert: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      create: jest.fn(),
      findOne: jest.fn(),
      findOneOrFail: jest.fn(),
      createQueryBuilder: jest.fn(),
      leftJoinAndSelect: jest.fn(),
      where: jest.fn(),
      getOne: jest.fn(),
      count: jest.fn(),
    } as unknown as jest.Mocked<EntityManager>;
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BroadcastMessageService,
        { provide: getRepositoryToken(StoreEntity), useClass: Repository },
        { provide: getRepositoryToken(AttachmentEntity), useClass: Repository },
        { provide: getRepositoryToken(BroadcastMessageEntity), useClass: Repository },
        { provide: AmqpConnection, useValue: mockAmqp },
        { provide: HttpService, useValue: { get: jest.fn() } },
        { provide: getRedisToken('default'), useValue: mockRedis },
        {
          provide: SlackService,
          useValue: mockSlackService,
        },
        { provide: AttachmentService, useValue: mockAttachmentService },
        { provide: DataSource, useValue: mockDataSource },
        { provide: getQueueToken(BULLMQ_MESSAGE_BROADCAST_QUEUE), useValue: mockBullMQQueue },
      ],
    }).compile();

    service = module.get<BroadcastMessageService>(BroadcastMessageService);
    storeRepository = module.get(getRepositoryToken(StoreEntity));
    attachmentRepository = module.get(getRepositoryToken(AttachmentEntity));
    broadcastMessageRepository = module.get(getRepositoryToken(BroadcastMessageEntity));
    httpService = module.get<HttpService>(HttpService);
    redis = module.get(getRedisToken('default'));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getCountStoreByStatus', () => {
    it('should retrieve stores by their statuses', async () => {
      const statuses = [1, 2];
      const stores = 2;
      jest.spyOn(storeRepository, 'count').mockResolvedValue(2);

      const result = await service.getCountStoreByStatus(statuses);

      expect(result).toEqual(stores);
      expect(storeRepository.count).toHaveBeenCalledWith({ where: { status: In(statuses) } });
    });
  });
  describe('getStoreLengthByStatus', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    it('should return 0 if storeIds array is empty', async () => {
      jest.spyOn(storeRepository, 'createQueryBuilder').mockReturnValue({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
        getCount: jest.fn().mockReturnValue(0),
      } as any);
      const result = await service.getStoreLengthByStatus([]);

      expect(result).toBe(0);
      expect(storeRepository.createQueryBuilder).not.toHaveBeenCalled();
    });

    it('should return the count of stores with matching IDs and statuses', async () => {
      const storeIds = ['id1', 'id2'];
      const expectedCount = 5;
      jest.spyOn(storeRepository, 'createQueryBuilder').mockReturnValue({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
        getCount: jest.fn().mockReturnValue(5),
      } as any);
      const result = await service.getStoreLengthByStatus(storeIds);

      expect(result).toBe(expectedCount);
    });
  });
  describe('processCSVData', () => {
    const CONCURRENT_MESSAGE_SIZE_BATCH = 100;

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should process store preview and return the first 30 valid stores', async () => {
      const storeUuid = uuidv4();
      const stream = Readable.from([`id\n`, `${storeUuid}\n`]);

      jest.spyOn(service, 'getStoreStatusByIds').mockResolvedValue([{ id: storeUuid, name: 'Store A' }]);

      const result = await service.processCSVData(stream);

      expect(result.publicAndSuspendedStores).toEqual([{ id: storeUuid, name: 'Store A' }]);
    });

    it('should handle invalid UUIDs gracefully and ignore them', async () => {
      const invalidUuid = '123-invalid-uuid';
      const stream = Readable.from([`id\n`, `${invalidUuid}\n`, `${uuidv4()}\n`]);

      jest.spyOn(service, 'getStoreStatusByIds').mockResolvedValue([]);

      const result = await service.processCSVData(stream);

      expect(result.publicAndSuspendedStores).toEqual([]);
    });

    it('should batch process IDs in chunks and handle multiple batches', async () => {
      const validUuids = Array.from({ length: CONCURRENT_MESSAGE_SIZE_BATCH + 10 }, () => uuidv4());
      const stream = Readable.from([`id\n`, ...validUuids.map((id) => `${id}\n`)]);

      jest
        .spyOn(service, 'getStoreStatusByIds')
        .mockImplementation(async (ids) => ids.map((id) => ({ id, name: `Store ${id}` })));

      const result = await service.processCSVData(stream);

      expect(result.publicAndSuspendedStores.length).toBe(110);
    });

    it('should stop processing once 30 valid stores are found', async () => {
      const validUuids = Array.from({ length: 50 }, () => uuidv4());
      const stream = Readable.from([`id\n`, ...validUuids.map((id) => `${id}\n`)]);

      jest
        .spyOn(service, 'getStoreStatusByIds')
        .mockImplementation(async (ids) => ids.map((id) => ({ id, name: `Store ${id}` })));

      const result = await service.processCSVData(stream);

      expect(result.publicAndSuspendedStores.length).toBe(50);
      expect(service.getStoreStatusByIds).toHaveBeenCalledTimes(1);
    });

    it('should process remaining IDs in the final batch after the stream ends', async () => {
      const validUuids = Array.from({ length: 20 }, () => uuidv4());
      const stream = Readable.from([`id\n`, ...validUuids.map((id) => `${id}\n`)]);

      jest
        .spyOn(service, 'getStoreStatusByIds')
        .mockImplementation(async (ids) => ids.map((id) => ({ id, name: `Store ${id}` })));

      const result = await service.processCSVData(stream);

      expect(result.publicAndSuspendedStores.length).toBe(20);
    });
  });
  describe('processPreviewStore', () => {
    const CONCURRENT_MESSAGE_SIZE_BATCH = 100;

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should process store preview and return the first 30 valid stores', async () => {
      const storeUuid = uuidv4();
      const stream = Readable.from([`id\n`, `${storeUuid}\n`]);

      jest.spyOn(service, 'getStoreStatusByIds').mockResolvedValue([{ id: storeUuid, name: 'Store A' }]);

      const result = await service.processPreviewStore(stream);

      expect(result.publicAndSuspendedStores).toEqual([{ id: storeUuid, name: 'Store A' }]);
    });

    it('should handle invalid UUIDs gracefully and ignore them', async () => {
      const invalidUuid = '123-invalid-uuid';
      const stream = Readable.from([`id\n`, `${invalidUuid}\n`, `${uuidv4()}\n`]);

      jest.spyOn(service, 'getStoreStatusByIds').mockResolvedValue([]);

      const result = await service.processPreviewStore(stream);

      expect(result.publicAndSuspendedStores).toEqual([]);
    });

    it('should batch process IDs in chunks and handle multiple batches', async () => {
      const validUuids = Array.from({ length: CONCURRENT_MESSAGE_SIZE_BATCH + 10 }, () => uuidv4());
      const stream = Readable.from([`id\n`, ...validUuids.map((id) => `${id}\n`)]);

      jest
        .spyOn(service, 'getStoreStatusByIds')
        .mockImplementation(async (ids) => ids.map((id) => ({ id, name: `Store ${id}` })));

      const result = await service.processPreviewStore(stream);

      expect(result.publicAndSuspendedStores.length).toBe(30);
    });

    it('should stop processing once 30 valid stores are found', async () => {
      const validUuids = Array.from({ length: 50 }, () => uuidv4());
      const stream = Readable.from([`id\n`, ...validUuids.map((id) => `${id}\n`)]);

      jest
        .spyOn(service, 'getStoreStatusByIds')
        .mockImplementation(async (ids) => ids.map((id) => ({ id, name: `Store ${id}` })));

      const result = await service.processPreviewStore(stream);

      expect(result.publicAndSuspendedStores.length).toBe(30);
      expect(service.getStoreStatusByIds).toHaveBeenCalledTimes(1);
    });

    it('should process remaining IDs in the final batch after the stream ends', async () => {
      const validUuids = Array.from({ length: 20 }, () => uuidv4());
      const stream = Readable.from([`id\n`, ...validUuids.map((id) => `${id}\n`)]);

      jest
        .spyOn(service, 'getStoreStatusByIds')
        .mockImplementation(async (ids) => ids.map((id) => ({ id, name: `Store ${id}` })));

      const result = await service.processPreviewStore(stream);

      expect(result.publicAndSuspendedStores.length).toBe(20);
    });
  });

  describe('publishBatchMessage', () => {
    it('should call addDelayedJobToBullMQ with correct batches', async () => {
      const stores = Array(105)
        .fill(null)
        .map((_, i) => ({ id: `${i + 1}`, name: `Store ${i + 1}` }));
      const broadcastMessage = { id: '123', sendingBy: 'admin' };
      const headers = {};
      const broadcastData = { content: 'Test message', attachments: [] };

      const addDelayedJobToBullMQSpy = jest.spyOn(service as any, 'addDelayedJobToBullMQ').mockResolvedValue(undefined);

      await (service as any).publishBatchMessage(stores, broadcastMessage, headers, broadcastData);

      // With 105 stores and batch size of 500, only 1 batch is needed
      expect(addDelayedJobToBullMQSpy).toHaveBeenCalledTimes(1);
      expect(addDelayedJobToBullMQSpy).toHaveBeenCalledWith('123', 0, stores, headers, broadcastData);
    });
  });

  describe('getAttachmentFilesByRelationId', () => {
    it('should retrieve attachment files for a given relation ID', async () => {
      const attachments = [{ id: '1', path: '/file1.csv', attachmentType: 'csv' }];
      jest.spyOn(attachmentRepository, 'find').mockResolvedValue(attachments as any);

      const result = await service.getAttachmentFilesByRelationId('relation-123');

      expect(result).toEqual(attachments);
      expect(attachmentRepository.find).toHaveBeenCalledWith({
        where: { relationId: 'relation-123' },
        select: ['id', 'path', 'attachmentType'],
      });
    });
  });

  describe('getStoreStatusByIds', () => {
    it('should retrieve valid store statuses by IDs', async () => {
      const storeIds = ['1', '2'];
      const stores = [{ id: '1', name: 'Store A' }];
      jest.spyOn(storeRepository, 'createQueryBuilder').mockReturnValue({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(stores),
      } as any);

      const result = await service.getStoreStatusByIds(storeIds);

      expect(result).toEqual(stores);
    });
    it('should not retrieve valid store statuses by IDs', async () => {
      const storeIds = [];
      const stores = [];
      jest.spyOn(storeRepository, 'createQueryBuilder').mockReturnValue({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(stores),
      } as any);

      const result = await service.getStoreStatusByIds(storeIds);

      expect(result).toEqual(stores);
    });
  });

  describe('updateSentCountsAndStatus', () => {
    it('should update success and failure counts', async () => {
      const broadcastMessage = { id: '123', storeSentFailCount: 2 };
      jest.spyOn(broadcastMessageRepository, 'findOne').mockResolvedValue(broadcastMessage as any);
      jest.spyOn(broadcastMessageRepository, 'update').mockResolvedValue(undefined);

      await service.updateSentCountsAndStatus('123', 5, 3);

      expect(broadcastMessageRepository.update).toHaveBeenCalledWith('123', {
        storeSentSuccessCount: 5,
        storeSentFailCount: 5,
        status: BroadcastMessageStatus.Sent,
      });
    });
  });

  describe('getAll', () => {
    it('should retrieve broadcast messages with pagination', async () => {
      jest.spyOn(broadcastMessageRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[{ id: '1', title: 'Test' }], 1]),
      } as any);

      const result = await service.getAll(
        {
          search: 'Test',
          pageNumber: 1,
          perPage: 10,
          page: 0,
          offset: 0,
        },
        {} as any,
      );

      expect(result.listBroadcastMessages).toHaveLength(1);
      expect(result.pagination.total).toBe(1);
    });
  });

  describe('getById', () => {
    it('should retrieve a broadcast message by ID', async () => {
      const storeUuid = uuidv4();
      const broadcastMessage = {
        id: '123',
        content: 'abc',
        csvFileName: 'file.csv',
        importFilePath: 'path/to/file',
        attachments: [],
      };

      const mockCsvData = Readable.from([
        'id\n', // Column header
        `${storeUuid}\n`, // First UUID entry
        `${uuidv4()}`, // Second UUID entry
      ]);
      const mockStream = Readable.from(['mock stream data']);

      jest.spyOn(service, 'findOneById').mockResolvedValue(broadcastMessage as any);
      jest.spyOn(service, 'processPreviewStore').mockResolvedValue({
        publicAndSuspendedStores: [{ id: storeUuid, name: 'Store A' }],
      });

      // Mocking `httpService.get` to return observable data for lastValueFrom
      const mockResponse: AxiosResponse = {
        data: mockCsvData,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {
          headers: undefined,
        },
      };
      jest.spyOn(httpService, 'get').mockReturnValue(of(mockResponse));
      jest.spyOn(service, 'bufferToStream').mockReturnValue(mockStream);

      const result = await service.getById('123');

      expect(result.id).toBe('123');
      expect(result.storeList).toEqual([{ id: storeUuid, name: 'Store A' }]);
      expect(service.findOneById).toHaveBeenCalledWith('123', ['attachments']);
      expect(httpService.get).toHaveBeenCalled();
      expect(service.bufferToStream).toHaveBeenCalledWith(mockCsvData);
    });
    it('should retrieve a broadcast message by ID with importFilePath null', async () => {
      const storeUuid = uuidv4();

      // Mock BroadcastMessageEntity with importFilePath = null
      const broadcastMessage = {
        id: '123',
        content: 'abc',
        csvFileName: null,
        importFilePath: null,
        attachments: [],
      };

      const mockStoreStatus = [{ id: storeUuid, name: 'Store A' }];
      const mockStores = [{ id: storeUuid, name: 'Store A' }];

      jest.spyOn(service, 'findOneById').mockResolvedValue(broadcastMessage as any);

      // Mock TypeORM query to return store list
      jest.spyOn(storeRepository, 'createQueryBuilder').mockReturnValue({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockStores),
      } as any);

      const result = await service.getById('123');

      // Assertions
      expect(result.id).toBe('123');
      expect(result.storeList).toEqual(mockStoreStatus);
      expect(service.findOneById).toHaveBeenCalledWith('123', ['attachments']);
      expect(storeRepository.createQueryBuilder).toHaveBeenCalled();
    });

    it('should throw NotFoundException when broadcast message is not found', async () => {
      jest.spyOn(service, 'findOneById').mockResolvedValue(null);

      await expect(service.getById('non-existent-id')).rejects.toThrow(NotFoundException);
      expect(service.findOneById).toHaveBeenCalledWith('non-existent-id', ['attachments']);
    });
  });

  describe('handleScheduleSendingMessage', () => {
    beforeAll(() => {
      // Mock Date.now to return a consistent timestamp
      jest.spyOn(Date, 'now').mockImplementation(() => new Date('2024-01-01T00:00:00Z').getTime());
    });

    afterAll(() => {
      // Restore the original implementation
      jest.restoreAllMocks();
    });
    it('should handle fail case not found of a broadcast message', async () => {
      const broadcastMessage = {};
      jest.spyOn(service, 'findOneById').mockResolvedValue(broadcastMessage as any);
      try {
        await service.handleScheduleSendingMessage(
          '123',
          {
            sendNow: true,
            dateTime: undefined,
          },
          { id: 'admin' } as any,
        );
      } catch (error) {
        expect(error.name).toBe('BadRequestException'); // Check the name of the error instead
      }
    });
    it('should handle immediate sending of a broadcast message', async () => {
      const broadcastMessage = { id: '123', status: BroadcastMessageStatus.Approved };
      jest.spyOn(service, 'findOneById').mockResolvedValue(broadcastMessage as any);
      jest.spyOn(broadcastMessageRepository, 'update').mockResolvedValue(undefined);
      jest.spyOn(service, 'checkTime').mockResolvedValue(false);
      const result = await service.handleScheduleSendingMessage(
        '123',
        {
          sendNow: true,
          dateTime: undefined,
        },
        { id: 'admin' } as any,
      );

      expect(result.success).toBe(true);
    });
    it('case sendNow with checkTime true of a broadcast message', async () => {
      const broadcastMessage = { id: '123', status: BroadcastMessageStatus.Approved };
      jest.spyOn(service, 'findOneById').mockResolvedValue(broadcastMessage as any);
      jest.spyOn(broadcastMessageRepository, 'update').mockResolvedValue(undefined);
      jest.spyOn(service, 'checkTime').mockResolvedValue(true);
      const result = await service.handleScheduleSendingMessage(
        '123',
        {
          sendNow: true,
          dateTime: undefined,
        },
        { id: 'admin' } as any,
      );

      expect(result.success).toBe(false);
    });
    it('case send later with checkTime true of a broadcast message', async () => {
      const now = new Date('2023-10-20T12:00:00Z');
      const broadcastMessage = { id: '123', status: BroadcastMessageStatus.Approved };
      jest.spyOn(service, 'findOneById').mockResolvedValue(broadcastMessage as any);
      jest.spyOn(broadcastMessageRepository, 'update').mockResolvedValue(undefined);
      jest.spyOn(service, 'checkTime').mockResolvedValue(true);
      const result = await service.handleScheduleSendingMessage(
        '123',
        {
          sendNow: false,
          dateTime: now,
        },
        { id: 'admin' } as any,
      );

      expect(result.success).toBe(false);
    });
    it('case send later with checkTime true of a broadcast message', async () => {
      const now = new Date('2023-10-20T12:00:00Z');
      const broadcastMessage = { id: '123', status: BroadcastMessageStatus.Approved };
      jest.spyOn(service, 'findOneById').mockResolvedValue(broadcastMessage as any);
      jest.spyOn(broadcastMessageRepository, 'update').mockResolvedValue(undefined);
      jest.spyOn(service, 'checkTime').mockResolvedValue(false);
      const result = await service.handleScheduleSendingMessage(
        '123',
        {
          sendNow: false,
          dateTime: now,
        },
        { id: 'admin' } as any,
      );

      expect(result.success).toBe(false);
    });
    it('case send later with checkTime true of a broadcast message', async () => {
      const now = new Date('2025-10-20T12:00:00Z');
      const broadcastMessage = { id: '123', status: BroadcastMessageStatus.Approved };
      jest.spyOn(service, 'findOneById').mockResolvedValue(broadcastMessage as any);
      jest.spyOn(broadcastMessageRepository, 'update').mockResolvedValue(undefined);
      jest.spyOn(service, 'checkTime').mockResolvedValue(false);
      const result = await service.handleScheduleSendingMessage(
        '123',
        {
          sendNow: false,
          dateTime: now,
        },
        { id: 'admin' } as any,
      );

      expect(result.success).toBe(true);
    });
  });

  describe('findOneWithSelectedField', () => {
    it('should retrieve a broadcast message with selected fields', async () => {
      const broadcastMessage = { id: '123', content: 'Test' };
      jest.spyOn(broadcastMessageRepository, 'findOne').mockResolvedValue(broadcastMessage as any);

      const result = await service.findOneWithSelectedField('123');

      expect(result).toEqual(broadcastMessage);
    });
  });

  describe('sendSlackNotification', () => {
    it('should send a Slack notification', async () => {
      const broadcastMessage = { id: '123', title: 'Test', scheduledSendTime: new Date() };

      jest.spyOn(mockSlackService, 'sendSlackNotification').mockResolvedValue({});

      await service.sendSlackNotification(broadcastMessage as any);
      expect(mockSlackService.sendSlackNotification).toHaveBeenCalled();
    });
  });
  describe('checkTime', () => {
    it('should return true when messages exist within the time range', async () => {
      const now = new Date('2023-10-20T12:00:00Z');
      const message = { id: 1, scheduledSendTime: new Date('2023-10-20T11:30:00Z') };

      jest.spyOn(broadcastMessageRepository, 'find').mockResolvedValue([message] as any);

      // Mocking the repository response

      const result = await service.checkTime(now);

      expect(result).toBe(true);
      expect(broadcastMessageRepository.find).toHaveBeenCalledWith({
        where: {
          scheduledSendTime: expect.any(Object), // Ensure Between is used
        },
      });
    });

    it('should return false when no messages exist within the time range', async () => {
      const now = new Date('2023-10-20T12:00:00Z');
      const message = {};
      // Mocking the repository response
      jest.spyOn(broadcastMessageRepository, 'find').mockResolvedValue(message as any);

      const result = await service.checkTime(now);

      expect(result).toBe(false);
      expect(broadcastMessageRepository.find).toHaveBeenCalledWith({
        where: {
          scheduledSendTime: expect.any(Object), // Ensure Between is used
        },
      });
    });

    it('should correctly calculate start and end hours based on now', async () => {
      const now = new Date('2023-10-20T12:59:00Z');
      const message = {};
      jest.spyOn(broadcastMessageRepository, 'find').mockResolvedValue(message as any);

      const startTime = new Date(now);
      startTime.setMinutes(now.getMinutes() - 59);

      const endTime = new Date(now);
      endTime.setMinutes(now.getMinutes() + 59);

      await service.checkTime(now);

      // Verify that the correct date range is being used in the repository query
      expect(broadcastMessageRepository.find).toHaveBeenCalledWith({
        where: {
          scheduledSendTime: Between(startTime, endTime),
        },
      });
    });
  });
  describe('bufferToStream', () => {
    it('should convert a buffer to a readable stream', () => {
      const buffer = Buffer.from('test data');
      const stream = service.bufferToStream(buffer);

      expect(stream).toBeInstanceOf(Readable);
      const chunks: string[] = [];

      return new Promise<void>((resolve) => {
        stream.on('data', (chunk) => {
          chunks.push(chunk.toString());
        });
        stream.on('end', () => {
          expect(chunks.join('')).toBe('test data');
          resolve();
        });
      });
    });

    it('should log and rethrow errors when conversion fails', () => {
      const buffer = null as unknown as Buffer; // Intentionally incorrect type to trigger error

      try {
        service.bufferToStream(buffer);
      } catch (error) {
        expect(error.name).toBe('TypeError'); // Check the name of the error instead
      }
    });
  });
  describe('updateStatus Method', () => {
    it('should throw BadRequestException if broadcast message is not found', async () => {
      jest.spyOn(broadcastMessageRepository, 'findOne').mockResolvedValue(null);

      await expect(service.updateStatus('test-id', BroadcastMessageStatus.Approved)).rejects.toThrow(
        BadRequestException,
      );

      expect(broadcastMessageRepository.findOne).toHaveBeenCalledWith({
        where: {
          id: 'test-id',
          status: expect.anything(),
        },
      });
    });

    it('should throw BadRequestException for invalid newStatus', async () => {
      jest.spyOn(broadcastMessageRepository, 'findOne').mockResolvedValue({} as any);
      await expect(
        service.updateStatus('test-id', 99), // Assume 99 is invalid
      ).rejects.toThrow(BadRequestException);
    });

    it('should update approvedBy and approvedAt for unapproved to approved transition', async () => {
      const mockUser: any = { id: 'admin-user' };
      const mockBroadcastMessage = {
        status: BroadcastMessageStatus.Unapproved,
        approvedBy: null,
        approvedAt: null,
      };
      jest.spyOn(broadcastMessageRepository, 'findOne').mockResolvedValue(mockBroadcastMessage as any);
      jest.spyOn(broadcastMessageRepository, 'update').mockResolvedValue(true as any);

      await service.updateStatus('test-id', BroadcastMessageStatus.Approved, mockUser);

      expect(broadcastMessageRepository.update).toHaveBeenCalledWith('test-id', {
        status: BroadcastMessageStatus.Approved,
        approvedAt: expect.any(Date),
        approvedBy: mockUser.id,
        scheduledSendTime: undefined,
      });
    });

    it('should clear scheduledSendTime for SendingScheduled to Approved transition', async () => {
      const mockBroadcastMessage = {
        status: BroadcastMessageStatus.SendingScheduled,
        scheduledSendTime: new Date(),
      };
      jest.spyOn(broadcastMessageRepository, 'findOne').mockResolvedValue(mockBroadcastMessage as any);
      jest.spyOn(broadcastMessageRepository, 'update').mockResolvedValue(true as any);

      await service.updateStatus('test-id', BroadcastMessageStatus.Approved);

      expect(broadcastMessageRepository.update).toHaveBeenCalledWith('test-id', {
        status: BroadcastMessageStatus.Approved,
        approvedBy: undefined, // Assuming no user was passed in
        scheduledSendTime: null,
      });
    });

    it('should log error and rethrow the exception', async () => {
      const errorMessage = 'Database connection error';
      jest.spyOn(broadcastMessageRepository, 'findOne').mockRejectedValue(new Error(errorMessage));

      await expect(service.updateStatus('test-id', BroadcastMessageStatus.Approved)).rejects.toThrow(Error);
    });
  });
  describe('processAndSendMessages', () => {
    const broadcastMessage = {
      id: 'test-broadcast-id',
      importFilePath: 'path/to/csv/file.csv',
      content: 'Test broadcast message content',
      canSendStoreCount: 5,
    } as BroadcastMessageEntity;
    beforeEach(() => {
      jest.spyOn(service, 'getAttachmentFilesByRelationId').mockResolvedValue([]);
      jest.spyOn(service, 'getValidStoreIdsFromCsv').mockResolvedValue([]);
      jest.spyOn(service, 'getCountStoreByStatus').mockResolvedValue(0);
      jest.spyOn(service, 'processFullStore').mockResolvedValue([]);
      jest.spyOn(service as any, 'publishBatchMessage').mockResolvedValue(undefined);
      jest.spyOn(broadcastMessageRepository, 'update').mockResolvedValue(undefined);
      jest.spyOn(redis, 'set').mockResolvedValue('OK');
      jest.spyOn(redis, 'del').mockResolvedValue(1);
    });

    it('should process message and publish batches when stores are available', async () => {
      const validStores = [
        { id: 'store1', name: 'Store 1' },
        { id: 'store2', name: 'Store 2' },
      ];
      jest.spyOn(service, 'getValidStoreIdsFromCsv').mockResolvedValue(validStores);
      jest.spyOn(service as any, 'publishBatchMessage').mockResolvedValue(undefined);

      const result = await service.processAndSendMessages(broadcastMessage);

      expect(service.getValidStoreIdsFromCsv).toHaveBeenCalledWith(broadcastMessage.importFilePath);
      expect(service['publishBatchMessage']).toHaveBeenCalled();
      expect(result).toEqual({ message: 'Bulk message sending was successful!' });
    });

    it('should handle case when no stores are available', async () => {
      jest.spyOn(service, 'getCountStoreByStatus').mockResolvedValue(0);

      const result = await service.processAndSendMessages(broadcastMessage);

      expect(broadcastMessageRepository.update).toHaveBeenCalledWith(broadcastMessage.id, {
        storeSentSuccessCount: 0,
        storeSentFailCount: broadcastMessage.canSendStoreCount,
        status: BroadcastMessageStatus.Sent,
      });
      expect(result).toEqual({ message: 'Bulk message sending was successful!' });
      expect(service['publishBatchMessage']).not.toHaveBeenCalled();
    });

    it('should handle an error during processing', async () => {
      const error = new Error('Test error');
      // Since broadcastMessage has importFilePath, getValidStoreIdsFromCsv will be called instead of getCountStoreByStatus
      jest.spyOn(service, 'getValidStoreIdsFromCsv').mockRejectedValue(error);

      await expect(service.processAndSendMessages(broadcastMessage)).rejects.toThrow(error);
    });
  });
  describe('checkCSVFile', () => {
    const bufferToStreamMock = jest.fn();

    beforeEach(() => {
      bufferToStreamMock.mockClear();
    });

    it('should throw an error if the file is not provided', async () => {
      await expect(service.checkCSVFile(undefined)).rejects.toThrow(BadRequestException);
      await expect(service.checkCSVFile(undefined)).rejects.toThrow('The CSV file has not been uploaded');
    });

    it('should throw an error if the file mimetype is not csv', async () => {
      const fileMock = {
        mimetype: 'text/plain',
        buffer: Buffer.from(''),
      } as Express.Multer.File;

      await expect(service.checkCSVFile(fileMock)).rejects.toThrow(BadRequestException);
      await expect(service.checkCSVFile(fileMock)).rejects.toThrow('Please upload the CSV file');
    });
  });
  describe('getValidStoreIdsFromCsv', () => {
    const mockedResult = {
      publicAndSuspendedStores: [
        { id: 1, name: 'Store1' },
        { id: 2, name: 'Store2' },
      ],
    };
    it('should return valid store IDs when CSV is processed successfully', async () => {
      const csvFilePath = 'path/to/valid-csv.csv';

      jest.spyOn(service, 'getStreamFromS3').mockResolvedValue('fake stream' as any);
      jest.spyOn(service, 'processCSVData').mockResolvedValue(mockedResult as any);

      const result = await service.getValidStoreIdsFromCsv(csvFilePath);

      expect(service.getStreamFromS3).toHaveBeenCalledWith(csvFilePath);
      expect(service.processCSVData).toHaveBeenCalledWith('fake stream');
      expect(result).toEqual(mockedResult.publicAndSuspendedStores);
    });
  });
  describe('findOneById', () => {
    it('should return valid store IDs when CSV is processed successfully', async () => {
      const broadcastMessage = { id: '123', sendingBy: 'admin' };

      jest.spyOn(broadcastMessageRepository, 'findOne').mockResolvedValue(broadcastMessage as any);

      const result = await service.findOneById('123');
      expect(result).toEqual(broadcastMessage);
    });
  });

  describe('BroadcastMessageService - save, update, delete', () => {
    let broadcastMessage: BroadcastMessageEntity;

    beforeEach(() => {
      broadcastMessage = {
        id: 'test-broadcast-id',
        title: 'abc',
        content: 'content',
        description: 'description',
        status: 1,
        createdBy: 'admin',
      } as BroadcastMessageEntity;

      jest.spyOn(broadcastMessageRepository, 'findOne').mockResolvedValue(broadcastMessage);
      jest.spyOn(mockDataSource, 'transaction').mockImplementation((cb) => cb(entityManager));
    });

    // 1. Unit Test for deleteBroadcastMessageAndAttachments
    describe('deleteBroadcastMessageAndAttachments', () => {
      it('should delete a broadcast message and its attachments', async () => {
        jest.spyOn(service, 'findOneById').mockResolvedValue(broadcastMessage);
        jest.spyOn(entityManager, 'delete').mockResolvedValue(undefined);

        const result = await service.deleteBroadcastMessageAndAttachments('test-broadcast-id');

        expect(service.findOneById).toHaveBeenCalledWith('test-broadcast-id');
        expect(entityManager.delete).toHaveBeenCalledWith(AttachmentEntity, { relationId: 'test-broadcast-id' });
        expect(entityManager.delete).toHaveBeenCalledWith(BroadcastMessageEntity, { id: 'test-broadcast-id' });
        expect(result).toEqual({ message: 'Broadcast message and related attachments deleted successfully' });
      });

      it('should throw a BadRequestException if message is already sent', async () => {
        broadcastMessage.status = 5; // Sent status
        jest.spyOn(service, 'findOneById').mockResolvedValue(broadcastMessage);

        await expect(service.deleteBroadcastMessageAndAttachments('test-broadcast-id')).rejects.toThrow(
          BadRequestException,
        );
      });

      it('should throw a NotFoundException if broadcast message does not exist', async () => {
        jest.spyOn(service, 'findOneById').mockResolvedValue(null);

        await expect(service.deleteBroadcastMessageAndAttachments('test-broadcast-id')).rejects.toThrow(
          BadRequestException,
        );
      });
    });

    describe('save', () => {
      let mockQueryBuilder: any;

      beforeEach(() => {
        // Mock the queryBuilder methods
        mockQueryBuilder = {
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getOne: jest.fn().mockResolvedValue({
            id: 'broadcast-id',
            title: 'New Broadcast',
            attachments: [],
          }),
        };

        jest.spyOn(mockDataSource, 'transaction').mockImplementation(async (callback) => {
          return await callback({
            create: jest.fn().mockImplementation((_, obj) => ({ ...obj, id: 'broadcast-id' })),
            insert: jest.fn().mockResolvedValue(undefined),
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
          });
        });
      });

      it('should save a new broadcast message with valid data', async () => {
        const dto: SaveTemplateDto = {
          title: 'New Broadcast',
          content: 'Test content',
          description: 'Test description',
          selectAllStores: true,
        };

        const stores = [{ id: '1', name: 'Store A' }];

        jest.spyOn(storeRepository, 'createQueryBuilder').mockReturnValue({
          select: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue(stores),
          getCount: jest.fn().mockReturnValue(1),
        } as any);
        // Mock getStoresByStatus
        jest.spyOn(service, 'getCountStoreByStatus').mockResolvedValue(1);
        // Mock uploadFile

        const result = await service.save(dto, 'admin-id', undefined, []);

        expect(mockDataSource.transaction).toHaveBeenCalled();
        expect(result.success).toBe(true);
        expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalled();
        expect(mockQueryBuilder.where).toHaveBeenCalled();
        expect(mockQueryBuilder.getOne).toHaveBeenCalled();
      });

      it('should handle save without CSV file', async () => {
        const dto: SaveTemplateDto = {
          title: 'New Broadcast',
          content: 'Test content',
          description: 'Test description',
          selectAllStores: false,
        };

        const result = await service.save(dto, 'admin-id', null, []);

        expect(mockDataSource.transaction).toHaveBeenCalled();
        expect(result.success).toBe(true);
        expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalled();
        expect(mockQueryBuilder.where).toHaveBeenCalled();
        expect(mockQueryBuilder.getOne).toHaveBeenCalled();
      });
    });

    describe('update', () => {
      let mockQueryBuilder: any;

      beforeEach(() => {
        // Mock the queryBuilder methods
        mockQueryBuilder = {
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getOne: jest.fn().mockResolvedValue({
            id: 'test-broadcast-id',
            title: 'Updated Title',
            attachments: [],
          }),
        };

        jest.spyOn(mockDataSource, 'transaction').mockImplementation(async (callback) => {
          return await callback({
            findOneOrFail: jest.fn().mockResolvedValue({
              id: 'test-broadcast-id',
              title: 'Original Title',
              content: 'Original Content',
              description: 'Original Description',
              status: 1,
              createdBy: 'admin',
            }),
            update: jest.fn().mockResolvedValue(undefined),
            delete: jest.fn().mockResolvedValue(undefined),
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
          });
        });
      });

      it('should update an existing broadcast message', async () => {
        const dto: SaveTemplateDto = {
          title: 'Updated Title',
          content: 'Updated Content',
          description: 'Updated Description',
          selectAllStores: false,
          removedFiles: ['file1'],
        };
        const csvFile = { originalname: 'file.csv', buffer: Buffer.from('test') } as any;

        jest.spyOn(mockAttachmentService, 'uploadFile').mockResolvedValue({ path: 'path/to/csv' });

        const result = await service.update(dto, 'test-broadcast-id', 'admin', csvFile, []);

        expect(mockDataSource.transaction).toHaveBeenCalled();
        expect(mockAttachmentService.uploadFile).toHaveBeenCalledWith(csvFile, 'test-broadcast-id', expect.any(String));
        expect(result.success).toBe(true);
        expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalled();
        expect(mockQueryBuilder.where).toHaveBeenCalled();
        expect(mockQueryBuilder.getOne).toHaveBeenCalled();
      });
      it('should update an existing broadcast message', async () => {
        const dto: SaveTemplateDto = {
          title: 'Updated Title',
          content: 'Updated Content',
          description: 'Updated Description',
          selectAllStores: true,
          removedFiles: ['file1'],
        };

        jest.spyOn(mockAttachmentService, 'uploadFile').mockResolvedValue({ path: 'path/to/csv' });
        const stores = [{ id: '1', name: 'Store A' }];

        jest.spyOn(storeRepository, 'createQueryBuilder').mockReturnValue({
          select: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue(stores),
          getCount: jest.fn().mockReturnValue(1),
        } as any);
        const result = await service.update(dto, 'test-broadcast-id', 'admin');

        expect(mockDataSource.transaction).toHaveBeenCalled();
        expect(result.success).toBe(true);
        expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalled();
        expect(mockQueryBuilder.where).toHaveBeenCalled();
        expect(mockQueryBuilder.getOne).toHaveBeenCalled();
      });

      it('should throw BadRequestException if no ID is provided', async () => {
        const dto: SaveTemplateDto = {
          title: 'Title',
          content: '',
          description: '',
          selectAllStores: false,
        };

        await expect(service.update(dto, '', 'admin')).rejects.toThrow(BadRequestException);
      });
    });
  });

  describe('addDelayedJobToBullMQ', () => {
    it('should add a delayed job to BullMQ with correct parameters', async () => {
      const broadcastMessageId = 'bm123';
      const batchIndex = 2;
      const batch = [{ id: 'store1' }];
      const headers = { 'x-request-id': 'abc123' };
      const broadcastData = { content: 'Test message', attachments: [] };

      const loggerSpy = jest.spyOn(service['logger'], 'log');

      await (service as any).addDelayedJobToBullMQ(broadcastMessageId, batchIndex, batch, headers, broadcastData);

      expect(mockBullMQQueue.add).toHaveBeenCalledWith(
        BULLMQ_JOB_SEND_MESSAGE_BATCH,
        {
          batch,
          broadcastMessageId,
          headers,
          batchIndex,
          broadcastData,
        },
        expect.objectContaining({
          delay: expect.any(Number),
          jobId: `${broadcastMessageId}-batch-${batchIndex}`,
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        }),
      );
      expect(loggerSpy).toHaveBeenCalledWith(
        expect.stringContaining(`[BullMQ] Batch ${batchIndex} added to queue for broadcast ${broadcastMessageId}`),
      );
    });
  });
});
