import { Test, TestingModule } from '@nestjs/testing';
import { BroadcastMessageService } from '@modules/shared/broadcast-message/broadcast-message.service';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { BroadcastMessageController } from '@modules/terry/controllers/broadcast-message.controller';
import { CURAMA_AUTH_SERVICE, CURAMA_AUTHORIZATION_SERVICE } from '@curama-auth/constants';
import { SaveTemplateDto } from '@modules/shared/broadcast-message/dtos/save-template.dto';
import { SaveTimeTemplateDto } from '@modules/shared/broadcast-message/dtos/save-time-template.dto';
import { MessageLabel } from '@common/enums/message-label';
import { Py3Service } from '@vietnam/curama-py3-api';

declare global {
  interface String {
    escape(): string;
    urlize(): string;
    nl2br(): string;
  }
}
String.prototype.escape = jest.fn().mockReturnValue('');
String.prototype.urlize = jest.fn().mockReturnValue('');
String.prototype.nl2br = jest.fn().mockReturnValue('');

jest.mock('@src/common/decorators/multi-input-files.decorator', () => ({
  MultiInputFiles: jest.fn().mockImplementation(() => (target: any, key: any, descriptor: any) => {}),
}));
jest.mock('@src/common/utils/image-handler', () => ({
  getPrivateFile: jest.fn().mockImplementation((imgPath) => 'some/path'),
}));
describe('BroadcastMessageController', () => {
  let controller: BroadcastMessageController;

  const mockBroadcastMessageService = {
    findOneById: jest.fn(),
    getAll: jest.fn(),
    getById: jest.fn(),
    deleteBroadcastMessageAndAttachments: jest.fn(),
    handleScheduleSendingMessage: jest.fn(),
    updateStatus: jest.fn(),
    checkCSVFile: jest.fn(),
    processPreviewStore: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockRequest = {
    query: {},
  } as Request;

  const mockBody: SaveTemplateDto = {
    title: 'Test Message',
    content: 'This is the content',
    description: 'Message description',
    selectAllStores: false,
  };

  const mockFiles = {
    files: [
      {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        buffer: Buffer.from('csv content'),
        size: 1234,
        stream: null,
        destination: undefined,
        filename: undefined,
        path: undefined,
      },
    ],
    attachments: [],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BroadcastMessageController],
      providers: [
        {
          provide: BroadcastMessageService,
          useValue: mockBroadcastMessageService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: {},
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
        {
          provide: Py3Service,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<BroadcastMessageController>(BroadcastMessageController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('displayMessageSendHistory', () => {
    it('should return broadcast message details', async () => {
      const mockBroadcastMessage = {
        content: '',
        createdAdmin: { name: 'Admin1' },
        approvedAdmin: { name: 'Admin2' },
        createdAt: new Date(),
        updatedAt: new Date(),
        storeSentSuccessCount: 10,
        storeSentFailCount: 10,
        attachments: [],
      };
      const mockLabel = 'このメッセージは出店者の皆様にお送りしています'; // Actual label value

      mockBroadcastMessageService.findOneById.mockResolvedValue(mockBroadcastMessage);

      const result = await controller.displayMessageSendHistory('broadcastMessageId');
      expect(mockBroadcastMessageService.findOneById).toHaveBeenCalledWith('broadcastMessageId', [
        'createdAdmin',
        'approvedAdmin',
        'attachments',
      ]);
      expect(result).toEqual({
        label: mockLabel,
        message: mockBroadcastMessage.content,
        createdBy: mockBroadcastMessage.createdAdmin.name,
        createdAt: expect.any(String),
        approvedBy: mockBroadcastMessage.approvedAdmin.name,
        approvedAt: expect.any(String),
        linkDownload: expect.any(String),
        storeSuccessCount: mockBroadcastMessage.storeSentSuccessCount,
        storeUnsuccessCount: mockBroadcastMessage.storeSentFailCount,
        attachmentFiles: mockBroadcastMessage.attachments,
      });
    });
  });

  describe('getAll', () => {
    it('should return a list of broadcast messages', async () => {
      const mockListBroadcastMessage = [{ id: '1', content: 'Message1' }];
      mockBroadcastMessageService.getAll.mockResolvedValue(mockListBroadcastMessage);

      const result = await controller.getAll(mockRequest);
      expect(mockBroadcastMessageService.getAll).toHaveBeenCalledWith(expect.any(Object), mockRequest);
      expect(result).toEqual({ listBroadcastMessage: mockListBroadcastMessage });
    });
  });
  describe('createList', () => {
    it('should return a empty object', async () => {
      const mockListBroadcastMessage = {};

      const result = await controller.createList();
      expect(result).toEqual(mockListBroadcastMessage);
    });
  });

  describe('getById', () => {
    it('should return broadcast message details with allowEdit true', async () => {
      const mockUser = { id: 'userId' } as any;
      const mockBroadcastMessage = { id: '1', createdBy: 'userId' };
      mockBroadcastMessageService.getById.mockResolvedValue(mockBroadcastMessage);

      const result = await controller.getById('1', mockUser);
      expect(mockBroadcastMessageService.getById).toHaveBeenCalledWith('1');
      expect(result).toEqual({
        allowEdit: true,
        broadcastMessage: mockBroadcastMessage,
      });
    });

    it('should return broadcast message details with allowEdit false', async () => {
      const mockUser = { id: 'userId' } as any;
      const mockBroadcastMessage = { id: '1', createdBy: 'otherUserId' };
      mockBroadcastMessageService.getById.mockResolvedValue(mockBroadcastMessage);

      const result = await controller.getById('1', mockUser);
      expect(result).toEqual({
        allowEdit: false,
        broadcastMessage: mockBroadcastMessage,
      });
    });
  });

  describe('preview', () => {
    it('should return preview details with delete permission', async () => {
      const mockUser = { id: 'userId', can: jest.fn().mockResolvedValue(true) } as any;
      const mockBroadcastMessage = { id: '1', content: 'Test Message' };
      mockBroadcastMessageService.getById.mockResolvedValue(mockBroadcastMessage);

      const result = await controller.preview('1', mockUser);
      expect(mockBroadcastMessageService.getById).toHaveBeenCalledWith('1');
      expect(result).toEqual({
        label: MessageLabel.System,
        broadcastMessage: mockBroadcastMessage,
        hasApprovePermission: true,
        adminId: mockUser.id,
      });
    });

    it('should return preview details without delete permission', async () => {
      const mockUser = { id: 'userId', can: jest.fn().mockResolvedValue(false) } as any;
      const mockBroadcastMessage = { id: '1', content: 'Test Message' };
      mockBroadcastMessageService.getById.mockResolvedValue(mockBroadcastMessage);

      const result = await controller.preview('1', mockUser);
      expect(mockBroadcastMessageService.getById).toHaveBeenCalledWith('1');
      expect(result).toEqual({
        label: MessageLabel.System,
        broadcastMessage: mockBroadcastMessage,
        hasApprovePermission: false,
        adminId: mockUser.id,
      });
    });
  });

  describe('deleteById', () => {
    it('should delete a broadcast message', async () => {
      mockBroadcastMessageService.deleteBroadcastMessageAndAttachments.mockResolvedValue('Message deleted');

      const result = await controller.deleteById('1');
      expect(mockBroadcastMessageService.deleteBroadcastMessageAndAttachments).toHaveBeenCalledWith('1');
      expect(result).toBe('Message deleted');
    });
  });

  describe('updateStatusById', () => {
    it('should update the status of a broadcast message', async () => {
      const mockUser = { id: 'userId' } as any;
      mockBroadcastMessageService.updateStatus.mockResolvedValue('Status updated');

      const result = await controller.updateStatusById('1', 2, mockUser);
      expect(mockBroadcastMessageService.updateStatus).toHaveBeenCalledWith('1', 2, mockUser);
      expect(result).toBe('Status updated');
    });
  });

  describe('countCSVFile', () => {
    it('should process a CSV file and return a list of stores', async () => {
      const mockFiles = [{ buffer: Buffer.from('file content') }] as any;
      const mockStream = 'CSVStream';
      const mockListStore = [{ id: 'storeId', status: true }];

      mockBroadcastMessageService.checkCSVFile.mockResolvedValue(mockStream);
      mockBroadcastMessageService.processPreviewStore.mockResolvedValue(mockListStore);

      const result = await controller.countCSVFile(mockFiles);
      expect(mockBroadcastMessageService.checkCSVFile).toHaveBeenCalledWith(mockFiles[0]);
      expect(mockBroadcastMessageService.processPreviewStore).toHaveBeenCalledWith(mockStream);
      expect(result).toEqual({ listStore: mockListStore });
    });
  });

  describe('handleScheduleSendingMessage', () => {
    it('should handle schedule sending message', async () => {
      const mockUser = { id: 'userId' } as any;
      const mockDto: SaveTimeTemplateDto = {
        dateTime: new Date('2024-10-15T10:00:00Z'),
        sendNow: false,
      };
      mockBroadcastMessageService.handleScheduleSendingMessage.mockResolvedValue('Message scheduled');

      const result = await controller.handleScheduleSendingMessage('1', mockDto, mockUser);
      expect(mockBroadcastMessageService.handleScheduleSendingMessage).toHaveBeenCalledWith('1', mockDto, mockUser);
      expect(result).toBe('Message scheduled');
    });
  });

  describe('store', () => {
    it('should save a broadcast message with attachments', async () => {
      const mockUser = { id: 'userId' } as any;
      mockBroadcastMessageService.save.mockResolvedValue('Message saved');

      const result = await controller.store(mockFiles, mockUser, mockBody);
      expect(mockBroadcastMessageService.save).toHaveBeenCalledWith(
        mockBody,
        mockUser.id,
        mockFiles.files[0],
        mockFiles.attachments,
      );
      expect(result).toBe('Message saved');
    });
  });

  describe('update', () => {
    it('should update a broadcast message with attachments', async () => {
      const mockUser = { id: 'userId' } as any;
      mockBroadcastMessageService.update.mockResolvedValue('Message updated');

      const result = await controller.update('1', mockFiles, mockUser, mockBody);
      expect(mockBroadcastMessageService.update).toHaveBeenCalledWith(
        mockBody,
        '1',
        mockUser.id,
        mockFiles.files[0],
        mockFiles.attachments,
      );
      expect(result).toBe('Message updated');
    });
  });
});
