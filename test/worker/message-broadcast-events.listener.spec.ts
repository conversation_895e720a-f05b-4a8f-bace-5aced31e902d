import { Test, TestingModule } from '@nestjs/testing';
import { Redis } from 'ioredis';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { getRedisToken } from '@liaoliaots/nestjs-redis';
import { Queue } from 'bullmq';
import { getQueueToken } from '@nestjs/bullmq';
import { MessageBroadcastEventsListener } from '@src/modules/shared/worker/listeners/message-broadcast-events.listener';
import { CONCURRENT_MESSAGE_MQ_NAME, BULLMQ_MESSAGE_BROADCAST_QUEUE } from '@src/common/constants';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';

describe('MessageBroadcastEventsListener', () => {
  let listener: MessageBroadcastEventsListener;
  let mockRedis: jest.Mocked<Redis>;
  let mockAmqpConnection: jest.Mocked<AmqpConnection>;
  let mockQueue: jest.Mocked<Queue>;

  const mockHeaders = {
    'x-client-id': 'admin-123',
    'x-client-type': 'admin',
    'x-request-id': 'req-123',
  };

  beforeEach(async () => {
    mockRedis = {
      get: jest.fn(),
    } as unknown as jest.Mocked<Redis>;

    mockAmqpConnection = {
      publish: jest.fn(),
    } as unknown as jest.Mocked<AmqpConnection>;

    mockQueue = {
      getJob: jest.fn(),
      getJobs: jest.fn(),
    } as unknown as jest.Mocked<Queue>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MessageBroadcastEventsListener,
        { provide: getRedisToken('default'), useValue: mockRedis },
        { provide: AmqpConnection, useValue: mockAmqpConnection },
        { provide: getQueueToken(BULLMQ_MESSAGE_BROADCAST_QUEUE), useValue: mockQueue },
      ],
    }).compile();

    listener = module.get<MessageBroadcastEventsListener>(MessageBroadcastEventsListener);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('onJobCompleted', () => {
    it('should handle job completion and trigger report creation when all jobs are done', async () => {
      const jobId = 'broadcast-123-batch-0';
      const mockJob = {
        data: {
          broadcastMessageId: 'broadcast-123',
          headers: mockHeaders,
        },
      };

      // Mock job retrieval
      mockQueue.getJob.mockResolvedValue(mockJob as any);

      // Mock job counts - all jobs completed
      const completedJobs = [{ id: 'broadcast-123-batch-0' }, { id: 'broadcast-123-batch-1' }];
      const failedJobs = [];
      const activeJobs = [];
      const waitingJobs = [];
      const delayedJobs = [];

      mockQueue.getJobs
        .mockResolvedValueOnce(completedJobs) // completed
        .mockResolvedValueOnce(failedJobs) // failed
        .mockResolvedValueOnce(activeJobs) // active
        .mockResolvedValueOnce(waitingJobs) // waiting
        .mockResolvedValueOnce(delayedJobs); // delayed

      const loggerSpy = jest.spyOn(listener['logger'], 'log');

      await listener.onJobCompleted({
        jobId,
        returnvalue: 'success',
      });

      expect(mockQueue.getJob).toHaveBeenCalledWith(jobId);
      expect(loggerSpy).toHaveBeenCalledWith(`Job completed: ${jobId}`);
      expect(loggerSpy).toHaveBeenCalledWith('Broadcast broadcast-123: 2/2 jobs processed (2 completed, 0 failed)');
      expect(loggerSpy).toHaveBeenCalledWith(
        'All jobs processed for broadcast broadcast-123. Triggering report creation.',
      );
      expect(mockAmqpConnection.publish).toHaveBeenCalledWith(
        'taskManager',
        CONCURRENT_MESSAGE_MQ_NAME,
        {
          event: RabbitMQEventType.CreateReportBroadcastMessage,
          content: { broadcastMessageId: 'broadcast-123' },
        },
        { headers: mockHeaders },
      );
    });

    it('should not trigger report creation when jobs are still pending', async () => {
      const jobId = 'broadcast-123-batch-0';
      const mockJob = {
        data: {
          broadcastMessageId: 'broadcast-123',
          headers: mockHeaders,
        },
      };

      mockQueue.getJob.mockResolvedValue(mockJob as any);

      // Mock job counts - some jobs still pending
      const completedJobs = [{ id: 'broadcast-123-batch-0' }];
      const failedJobs = [];
      const activeJobs = [];
      const waitingJobs = [{ id: 'broadcast-123-batch-1' }]; // Still waiting
      const delayedJobs = [];

      mockQueue.getJobs
        .mockResolvedValueOnce(completedJobs)
        .mockResolvedValueOnce(failedJobs)
        .mockResolvedValueOnce(activeJobs)
        .mockResolvedValueOnce(waitingJobs)
        .mockResolvedValueOnce(delayedJobs);

      const loggerSpy = jest.spyOn(listener['logger'], 'log');

      await listener.onJobCompleted({
        jobId,
        returnvalue: 'success',
      });

      expect(loggerSpy).toHaveBeenCalledWith('Broadcast broadcast-123: 1/2 jobs processed (1 completed, 0 failed)');
      expect(mockAmqpConnection.publish).not.toHaveBeenCalled();
    });

    it('should handle invalid job ID format', async () => {
      const jobId = 'invalid-job-id';
      const loggerWarnSpy = jest.spyOn(listener['logger'], 'warn');

      await listener.onJobCompleted({
        jobId,
        returnvalue: 'success',
      });

      expect(loggerWarnSpy).toHaveBeenCalledWith(`Could not extract broadcast message ID from job ID: ${jobId}`);
      expect(mockQueue.getJob).not.toHaveBeenCalled();
    });
  });

  describe('onJobFailed', () => {
    it('should handle job failure and check completion status', async () => {
      const jobId = 'broadcast-123-batch-0';
      const mockJob = {
        data: {
          broadcastMessageId: 'broadcast-123',
          headers: mockHeaders,
        },
      };

      mockQueue.getJob.mockResolvedValue(mockJob as any);

      // Mock job counts
      const completedJobs = [];
      const failedJobs = [{ id: 'broadcast-123-batch-0' }];
      const activeJobs = [];
      const waitingJobs = [];
      const delayedJobs = [];

      mockQueue.getJobs
        .mockResolvedValueOnce(completedJobs)
        .mockResolvedValueOnce(failedJobs)
        .mockResolvedValueOnce(activeJobs)
        .mockResolvedValueOnce(waitingJobs)
        .mockResolvedValueOnce(delayedJobs);

      const loggerErrorSpy = jest.spyOn(listener['logger'], 'error');
      const loggerLogSpy = jest.spyOn(listener['logger'], 'log');

      await listener.onJobFailed({
        jobId,
        failedReason: 'Processing error',
      });

      expect(loggerErrorSpy).toHaveBeenCalledWith(`Job failed: ${jobId}, reason: Processing error`);
      expect(loggerLogSpy).toHaveBeenCalledWith('Broadcast broadcast-123: 1/1 jobs processed (0 completed, 1 failed)');
    });
  });

  describe('extractBroadcastMessageId', () => {
    it('should extract broadcast message ID from valid job ID', () => {
      const result = listener['extractBroadcastMessageId']('broadcast-123-batch-0');
      expect(result).toBe('broadcast-123');
    });

    it('should return null for invalid job ID format', () => {
      const result = listener['extractBroadcastMessageId']('invalid-job-id');
      expect(result).toBeNull();
    });
  });

  describe('error handling', () => {
    it('should handle errors in onJobCompleted gracefully', async () => {
      const jobId = 'broadcast-123-batch-0';
      const error = new Error('Test error');

      mockQueue.getJob.mockRejectedValue(error);

      const loggerErrorSpy = jest.spyOn(listener['logger'], 'error');

      await listener.onJobCompleted({
        jobId,
        returnvalue: 'success',
      });

      expect(loggerErrorSpy).toHaveBeenCalledWith(`Error handling job completion for job ${jobId}:`, error);
    });

    it('should handle errors in getBroadcastJobCounts', async () => {
      const error = new Error('Queue error');
      mockQueue.getJobs.mockRejectedValue(error);

      const loggerErrorSpy = jest.spyOn(listener['logger'], 'error');

      const result = await listener['getBroadcastJobCounts']('broadcast-123');

      expect(loggerErrorSpy).toHaveBeenCalledWith('Error getting job counts for broadcast broadcast-123:', error);
      expect(result).toEqual({
        completedJobs: 0,
        failedJobs: 0,
        totalJobs: 0,
      });
    });
  });
});
