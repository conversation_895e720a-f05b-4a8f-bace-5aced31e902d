import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { TeamEntity } from '@src/entities';
import { TeamUpserted } from '@src/modules/shared/worker/workers/tasks/team-upserted';
import { TeamMessage } from '@src/modules/shared/worker/types/team-message';

const message = {
  team_id: '856625e3-c4f6-412a-8828-fe0cb85f6ac7',
  name: 'Example team',
  department_id: 'a56625e3-c4f6-412a-8828-fe0cb85f6acb',
};

const transactionalEntityManager = {
  delete: jest.fn(),
};

const mockDataSource = {
  transaction: jest.fn().mockImplementation((callback) => {
    callback(transactionalEntityManager);
  }),
  getRepository: jest.fn(),
};

let teamRepo: Repository<TeamEntity>;
describe('Worker', () => {
  let handler: TeamUpserted;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TeamUpserted,
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: getRepositoryToken(TeamEntity),
          useClass: Repository,
        },
      ],
    }).compile();

    handler = module.get<TeamUpserted>(TeamUpserted);
    teamRepo = module.get<Repository<TeamEntity>>(getRepositoryToken(TeamEntity));
  });

  describe('handler', () => {
    it('should success', async () => {
      jest.spyOn(mockDataSource, 'getRepository').mockReturnValue(teamRepo);
      teamRepo.upsert = jest.fn();
      await handler.handle(<TeamMessage>message);
      expect(teamRepo.upsert).toHaveBeenCalled();
    });

    it('invalid data', async () => {
      const loggerLogSpy = jest.spyOn(handler['logger'], 'error');
      const result = await handler.handle(<TeamMessage>null);
      expect(loggerLogSpy).toHaveBeenCalled();
      expect(result).toBe(undefined);
    });
  });

  describe('validate', () => {
    it('validate true', async () => {
      const validated = handler.validateTask(<TeamMessage>message);
      expect(validated).toBe(true);
    });

    it('throw exception', async () => {
      expect(() => handler.validateTask(<TeamMessage>{ name: 'John' })).toThrowError();
    });
  });
});
