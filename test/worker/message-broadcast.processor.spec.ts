import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, EntityManager } from 'typeorm';
import { Redis } from 'ioredis';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { getRedisToken } from '@liaoliaots/nestjs-redis';
import { Job, Queue } from 'bullmq';
import { getQueueToken } from '@nestjs/bullmq';
import { MessageBroadcastProcessor } from '@src/modules/shared/worker/processors/message-broadcast.processor';
import { NotificationService } from '@src/modules/shared/notification/notification.service';
import { BullMQMessageBroadcastJobData } from '@src/modules/shared/worker/types/bullmq-message-broadcast';
import { StoresInfo } from '@modules/terry/types/csv-record';
import { InquiryThreadEntity, MessageEntity } from '@src/entities';
import { ThreadType } from '@common/enums/thread-type';
import { InquiryThreadStatus } from '@common/enums/inquiry-thread-status';
import { MessageType } from '@common/enums/message-type';
import { MessageLabel } from '@common/enums/message-label';
import { BULLMQ_MESSAGE_BROADCAST_QUEUE } from '@src/common/constants';
import { AttachmentFileUploadType } from '@common/types/attachment-file-upload.type';
import { AttachmentType } from '@common/enums/attachment-type';

describe('MessageBroadcastProcessor', () => {
  let processor: MessageBroadcastProcessor;
  let mockRedis: jest.Mocked<Redis>;
  let mockDataSource: jest.Mocked<DataSource>;
  let mockAmqpConnection: jest.Mocked<AmqpConnection>;
  let mockNotificationService: jest.Mocked<NotificationService>;
  let mockEntityManager: jest.Mocked<EntityManager>;
  let mockQueue: jest.Mocked<Queue>;

  const mockStores: StoresInfo[] = [
    { id: 'store-1', name: 'Store 1' },
    { id: 'store-2', name: 'Store 2' },
  ];

  const mockHeaders = {
    'x-client-id': 'admin-123',
    'x-client-type': 'admin',
    'x-request-id': 'req-123',
  };

  const mockBroadcastData = {
    content: 'Test broadcast message',
    attachments: [
      {
        id: 'attachment-1',
        path: '/path/to/file.jpg',
        attachmentType: AttachmentType.Image,
        relationId: 'message-1',
      } as AttachmentFileUploadType,
    ],
  };

  const mockJobData: BullMQMessageBroadcastJobData = {
    batch: mockStores,
    broadcastMessageId: 'broadcast-123',
    headers: mockHeaders,
    batchIndex: 0,
    broadcastData: mockBroadcastData,
  };

  const mockJob: Job<BullMQMessageBroadcastJobData> = {
    data: mockJobData,
  } as Job<BullMQMessageBroadcastJobData>;

  beforeEach(async () => {
    mockRedis = {
      get: jest.fn(),
      sadd: jest.fn(),
      scard: jest.fn(),
    } as unknown as jest.Mocked<Redis>;

    const mockQueryBuilder = {
      insert: jest.fn().mockReturnThis(),
      into: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      orUpdate: jest.fn().mockReturnThis(),
      execute: jest.fn().mockResolvedValue({ identifiers: [{ id: 'thread-1' }, { id: 'thread-2' }] }),
    };

    mockEntityManager = {
      createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
    } as unknown as jest.Mocked<EntityManager>;

    mockDataSource = {
      transaction: jest.fn().mockImplementation((callback) => callback(mockEntityManager)),
    } as unknown as jest.Mocked<DataSource>;

    mockAmqpConnection = {
      publish: jest.fn(),
    } as unknown as jest.Mocked<AmqpConnection>;

    mockNotificationService = {
      pushNotiNewMessageToStore: jest.fn(),
    } as unknown as jest.Mocked<NotificationService>;

    mockQueue = {
      getJobs: jest.fn(),
    } as unknown as jest.Mocked<Queue>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MessageBroadcastProcessor,
        { provide: getRedisToken('default'), useValue: mockRedis },
        { provide: DataSource, useValue: mockDataSource },
        { provide: AmqpConnection, useValue: mockAmqpConnection },
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: getQueueToken(BULLMQ_MESSAGE_BROADCAST_QUEUE), useValue: mockQueue },
      ],
    }).compile();

    processor = module.get<MessageBroadcastProcessor>(MessageBroadcastProcessor);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('process', () => {
    beforeEach(() => {
      mockQueue.getJobs.mockResolvedValue([]);
    });

    it('should successfully process a batch of stores', async () => {
      const loggerSpy = jest.spyOn(processor['logger'], 'log');
      const processStoresSpy = jest.spyOn(processor, 'processStores');
      const performBulkOperationsSpy = jest.spyOn(processor, 'performBulkOperations');

      await processor.process(mockJob);

      expect(loggerSpy).toHaveBeenCalledWith(
        'Processing message broadcast batch 0 for broadcast broadcast-123 with 2 stores',
      );
      expect(processStoresSpy).toHaveBeenCalledWith(mockStores, mockBroadcastData, 'admin-123');
      expect(performBulkOperationsSpy).toHaveBeenCalled();

      expect(mockNotificationService.pushNotiNewMessageToStore).toHaveBeenCalled();
      expect(loggerSpy).toHaveBeenCalledWith('Successfully processed batch 0 for broadcast broadcast-123');
    });

    it('should handle errors during processing and throw error for BullMQ to handle', async () => {
      const error = new Error('Processing failed');
      const loggerErrorSpy = jest.spyOn(processor['logger'], 'error');

      jest.spyOn(processor, 'processStores').mockImplementation(() => {
        throw error;
      });

      await expect(processor.process(mockJob)).rejects.toThrow(error);

      expect(loggerErrorSpy).toHaveBeenCalledWith('Batch processing error for batch 0:', error);
    });
  });

  describe('processStores', () => {
    it('should process stores and return prepared data for bulk operations', () => {
      const result = processor.processStores(mockStores, mockBroadcastData, 'admin-123');

      expect(result.threadsToSave).toHaveLength(2);
      expect(result.messagesToInsert).toHaveLength(2);
      expect(result.attachmentsToInsert).toHaveLength(2);

      // Check thread properties
      const thread = result.threadsToSave[0];
      expect(thread.storeId).toBe('store-1');
      expect(thread.storeName).toBe('Store 1');
      expect(thread.threadType).toBe(ThreadType.CuramaThread);
      expect(thread.status).toBe(InquiryThreadStatus.Unread);
      expect(thread.isRead).toBe(false);
      expect(thread.updatedBy).toBe('admin-123');

      // Check message properties
      const message = result.messagesToInsert[0];
      expect(message.content).toBe('Test broadcast message');
      expect(message.type).toBe(MessageType.Message);
      expect(message.label).toBe(MessageLabel.System);
      expect(message.fromAdminId).toBe('admin-123');

      // Check attachment properties
      const attachment = result.attachmentsToInsert[0];
      expect(attachment.path).toBe('/path/to/file.jpg');
      expect(attachment.attachmentType).toBe(AttachmentType.Image);
    });

    it('should handle stores without attachments', () => {
      const dataWithoutAttachments = { ...mockBroadcastData, attachments: [] };

      const result = processor.processStores(mockStores, dataWithoutAttachments, 'admin-123');

      expect(result.threadsToSave).toHaveLength(2);
      expect(result.messagesToInsert).toHaveLength(2);
      expect(result.attachmentsToInsert).toHaveLength(0);
    });
  });

  describe('performBulkOperations', () => {
    it('should perform bulk operations in transaction', async () => {
      const mockThreads = [new InquiryThreadEntity()];
      const mockMessages = [new MessageEntity()];
      const mockAttachments = [{ id: 'att-1' } as AttachmentFileUploadType];

      await processor.performBulkOperations(mockThreads, mockMessages, mockAttachments);

      expect(mockDataSource.transaction).toHaveBeenCalled();
      expect(mockEntityManager.createQueryBuilder).toHaveBeenCalledTimes(3);
    });

    it('should handle errors in bulk operations', async () => {
      const error = new Error('Database error');
      const loggerErrorSpy = jest.spyOn(processor['logger'], 'error');

      mockDataSource.transaction.mockRejectedValue(error);

      await expect(processor.performBulkOperations([], [], [])).rejects.toThrow(error);

      expect(loggerErrorSpy).toHaveBeenCalledWith('Error in bulk operations:', error);
    });
  });

  describe('createThread (private method)', () => {
    it('should create a thread with correct properties', () => {
      const store: StoresInfo = { id: 'store-123', name: 'Test Store' };
      const adminId = 'admin-456';

      // Access private method through bracket notation
      const thread = processor['createThread'](store, adminId);

      expect(thread).toBeInstanceOf(InquiryThreadEntity);
      expect(thread.id).toBeDefined();
      expect(thread.storeId).toBe('store-123');
      expect(thread.storeName).toBe('Test Store');
      expect(thread.threadType).toBe(ThreadType.CuramaThread);
      expect(thread.status).toBe(InquiryThreadStatus.Unread);
      expect(thread.isRead).toBe(false);
      expect(thread.updatedBy).toBe('admin-456');
      expect(thread.createdAt).toBeInstanceOf(Date);
      expect(thread.updatedAt).toBeInstanceOf(Date);
    });
  });

  describe('createMessage (private method)', () => {
    it('should create a message with correct properties', () => {
      const content = 'Test message content';
      const adminId = 'admin-789';

      // Access private method through bracket notation
      const message = processor['createMessage'](content, adminId);

      expect(message).toBeInstanceOf(MessageEntity);
      expect(message.id).toBeDefined();
      expect(message.content).toBe('Test message content');
      expect(message.type).toBe(MessageType.Message);
      expect(message.label).toBe(MessageLabel.System);
      expect(message.fromAdminId).toBe('admin-789');
      expect(message.createdAt).toBeInstanceOf(Date);
      expect(message.updatedAt).toBeInstanceOf(Date);
    });
  });

  describe('createRequest (private method)', () => {
    it('should create a request object with correct structure', () => {
      const headers = {
        'x-client-id': 'client-123',
        authorization: 'Bearer token',
      };

      // Access private method through bracket notation
      const request = processor['createRequest'](headers);

      expect(request.headers).toBe(headers);
      expect(request.terrySession).toEqual({
        adminId: 'client-123',
      });
    });
  });

  describe('upsertData (private method)', () => {
    it('should execute upsert query with correct parameters', async () => {
      const mockValues = [{ id: '1', name: 'test' }];
      const conflictPaths = ['id'];
      const upsertPaths = ['name'];

      // Access private method through bracket notation
      const result = await processor['upsertData'](
        mockEntityManager,
        InquiryThreadEntity,
        mockValues,
        conflictPaths,
        upsertPaths,
      );

      expect(mockEntityManager.createQueryBuilder).toHaveBeenCalled();
      expect(result).toEqual({ identifiers: [{ id: 'thread-1' }, { id: 'thread-2' }] });
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle empty batch', async () => {
      const emptyJobData = { ...mockJobData, batch: [] };
      const emptyJob = { data: emptyJobData } as Job<BullMQMessageBroadcastJobData>;

      mockRedis.get.mockResolvedValue(JSON.stringify(mockBroadcastData));

      await processor.process(emptyJob);

      expect(mockNotificationService.pushNotiNewMessageToStore).toHaveBeenCalledWith(
        {
          storeId: [],
          threadType: ThreadType.CuramaThread,
        },
        expect.any(Object),
      );
    });

    it('should handle missing broadcast data in Redis', async () => {
      mockRedis.get.mockResolvedValue(null);

      const result = processor.processStores(mockStores, { content: '', attachments: [] }, 'admin-123');

      expect(result.threadsToSave).toHaveLength(2);
      expect(result.messagesToInsert).toHaveLength(2);
      expect(result.attachmentsToInsert).toHaveLength(0);

      // Check that message content is empty when no broadcast data
      expect(result.messagesToInsert[0].content).toBe('');
    });

    it('should handle bulk operations with empty arrays', async () => {
      await processor.performBulkOperations([], [], []);

      expect(mockDataSource.transaction).toHaveBeenCalled();
      // Should not call any database operations when arrays are empty
    });

    it('should handle notification service errors gracefully', async () => {
      const notificationError = new Error('Notification failed');
      mockRedis.get.mockResolvedValue(JSON.stringify(mockBroadcastData));
      mockNotificationService.pushNotiNewMessageToStore.mockRejectedValue(notificationError);

      const loggerErrorSpy = jest.spyOn(processor['logger'], 'error');

      await expect(processor.process(mockJob)).rejects.toThrow(notificationError);

      expect(loggerErrorSpy).toHaveBeenCalledWith('Batch processing error for batch 0:', notificationError);
    });
  });
});
