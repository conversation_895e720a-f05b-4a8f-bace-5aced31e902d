import { BroadcastMessageService } from '@modules/shared/broadcast-message/broadcast-message.service';
import { BroadcastMessageEntity } from '@src/entities/broadcast-message.entity';
import { HandleCsvCheckStore } from '@src/modules/shared/worker/workers/tasks/handle-csv-upload-broadcast-message';
import { Readable } from 'stream';

jest.mock('@modules/shared/broadcast-message/broadcast-message.service');

describe('HandleCsvCheckStore', () => {
  let broadcastMessageServiceMock: jest.Mocked<BroadcastMessageService>;
  let handler: HandleCsvCheckStore;

  beforeEach(() => {
    broadcastMessageServiceMock = {
      findOneById: jest.fn(),
      getStreamFromS3: jest.fn(),
      processCSVData: jest.fn(),
      processStoreCount: jest.fn(),
    } as unknown as jest.Mocked<BroadcastMessageService>;

    handler = new HandleCsvCheckStore(broadcastMessageServiceMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateTask', () => {
    it('should throw an error if payload is null', () => {
      expect(() => handler.validateTask(null)).toThrowError('The payload is invalid.');
    });

    it('should return true for valid payload', () => {
      expect(handler.validateTask({ broadcastMessageId: '123' })).toBe(true);
    });
  });

  describe('handle', () => {
    it('should log message if no broadcastMessage is found', async () => {
      broadcastMessageServiceMock.findOneById.mockResolvedValue(null);
      const loggerSpy = jest.spyOn(handler['logger'], 'log');

      await handler.handle({ broadcastMessageId: '123' });

      expect(broadcastMessageServiceMock.findOneById).toHaveBeenCalledWith('123');
      expect(loggerSpy).toHaveBeenCalledWith('There is no message to send.');
    });

    it('should log message if importFilePath is missing', async () => {
      broadcastMessageServiceMock.findOneById.mockResolvedValue({
        importFilePath: null,
      } as BroadcastMessageEntity);

      const loggerSpy = jest.spyOn(handler['logger'], 'log');
      await handler.handle({ broadcastMessageId: '123' });

      expect(broadcastMessageServiceMock.findOneById).toHaveBeenCalledWith('123');
      expect(loggerSpy).toHaveBeenCalledWith('There is no csv to process.');
    });

    it('should handle CSV processing and update counts successfully', async () => {
      const saveMock = jest.fn();
      broadcastMessageServiceMock.findOneById.mockResolvedValue({
        importFilePath: 'path/to/file',
        save: saveMock,
      } as unknown as BroadcastMessageEntity);

      const s3StreamMock = Readable.from(['csv data']);
      broadcastMessageServiceMock.getStreamFromS3.mockResolvedValue(s3StreamMock as any);
      broadcastMessageServiceMock.processStoreCount.mockResolvedValue({
        totalRow: 10,
        totalStoresSuccess: 5,
      });

      const loggerSpy = jest.spyOn(handler['logger'], 'log');
      const result = await handler.handle({ broadcastMessageId: '123' });

      expect(broadcastMessageServiceMock.findOneById).toHaveBeenCalledWith('123');
      expect(broadcastMessageServiceMock.getStreamFromS3).toHaveBeenCalledWith('path/to/file');
      expect(broadcastMessageServiceMock.processStoreCount).toHaveBeenCalledWith(s3StreamMock);
      expect(saveMock).toHaveBeenCalled();
      expect(loggerSpy).toHaveBeenCalledWith('The message was successfully processed.');
      expect(result).toBeUndefined();
    });

    it('should log an error if broadcastMessageService.findOneById throws an exception', async () => {
      broadcastMessageServiceMock.findOneById.mockRejectedValue(new Error('DB Error'));
      const loggerSpy = jest.spyOn(handler['logger'], 'log');

      const result = await handler.handle({ broadcastMessageId: '123' });

      expect(broadcastMessageServiceMock.findOneById).toHaveBeenCalledWith('123');
      expect(loggerSpy).toHaveBeenCalledWith(
        'An error occurred while processing the scheduled message.',
        expect.any(Error),
      );
      expect(result).toBeUndefined();
    });
  });
});
