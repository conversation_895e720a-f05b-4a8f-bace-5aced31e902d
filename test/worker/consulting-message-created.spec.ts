import { SyncDataService } from '@modules/shared/search/services/sync-data.service';
import { MessageCreatedDocument } from '@modules/shared/worker/types/message-created-document';
import { ThreadType } from '@common/enums/thread-type';
import { ConsultingMessageCreated } from '@modules/shared/worker/workers/tasks/consulting-message-created';

describe('ConsultingMessageCreated', () => {
  let consultingMessageCreated: ConsultingMessageCreated;
  let syncDataService: SyncDataService;

  beforeEach(() => {
    syncDataService = {
      syncMessageCreation: jest.fn(),
    } as unknown as SyncDataService;

    consultingMessageCreated = new ConsultingMessageCreated(syncDataService);
  });

  describe('handle', () => {
    it('should call syncMessageCreation with correct payload', async () => {
      const payload: MessageCreatedDocument = {
        message_id: 'message-123',
        thread_id: 'thread-456',
        thread_type: 789,
        thread_status_id: 2,
        thread_updated_at: '2023-10-01T09:20:00Z',
      };

      await consultingMessageCreated.handle(payload);

      expect(syncDataService.syncMessageCreation).toHaveBeenCalledWith(
        payload.message_id,
        payload.thread_id,
        payload.thread_type,
        payload.thread_status_id,
        payload.thread_updated_at,
      );
    });
  });

  describe('validateTask', () => {
    it('should return true for valid payload', () => {
      const payload: MessageCreatedDocument = {
        message_id: 'message-123',
        thread_id: 'thread-456',
        thread_type: ThreadType.QualityThread,
        thread_status_id: 2,
        thread_updated_at: '2023-10-01T09:20:00Z',
      };

      const result = consultingMessageCreated.validateTask(payload);

      expect(result).toBe(true);
    });

    it('should throw an error for invalid payload (missing message_id)', () => {
      const payload: Partial<MessageCreatedDocument> = {
        thread_id: 'thread-456',
        thread_type: ThreadType.QualityThread,
      };

      expect(() => consultingMessageCreated.validateTask(payload as MessageCreatedDocument)).toThrow(
        'Invalid payload structure',
      );
    });

    it('should throw an error for invalid payload (missing thread_id)', () => {
      const payload: Partial<MessageCreatedDocument> = {
        message_id: 'message-123',
        thread_type: ThreadType.QualityThread,
      };

      expect(() => consultingMessageCreated.validateTask(payload as MessageCreatedDocument)).toThrow(
        'Invalid payload structure',
      );
    });

    it('should throw an error for invalid payload (missing thread_type)', () => {
      const payload: Partial<MessageCreatedDocument> = {
        message_id: 'message-123',
        thread_id: 'thread-456',
      };

      expect(() => consultingMessageCreated.validateTask(payload as MessageCreatedDocument)).toThrow(
        'Invalid payload structure',
      );
    });

    it('should throw an error for completely invalid payload', () => {
      const payload = null;

      expect(() => consultingMessageCreated.validateTask(payload as unknown as MessageCreatedDocument)).toThrow(
        'Invalid payload structure',
      );
    });
  });
});
