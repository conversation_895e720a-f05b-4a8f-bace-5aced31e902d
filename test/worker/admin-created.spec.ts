import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AdminCreated } from '@src/modules/shared/worker/workers/tasks/admin-created';
import { AdminEntity } from '@src/entities';
import { UpdateAdminMessage } from '@src/modules/shared/worker/types/update-admin-message';

const message = {
  admin_id: '856625e3-c4f6-412a-8828-fe0cb85f6ac5',
  email: '<EMAIL>',
  name: 'Example Admin',
  status: 1,
};

const transactionalEntityManager = {
  delete: jest.fn(),
};

const mockDataSource = {
  transaction: jest.fn().mockImplementation((callback) => {
    callback(transactionalEntityManager);
  }),
  getRepository: jest.fn(),
};

let adminRepo: Repository<AdminEntity>;
describe('Worker', () => {
  let handler: AdminCreated;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminCreated,
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: getRepositoryToken(AdminEntity),
          useClass: Repository,
        },
      ],
    }).compile();

    handler = module.get<AdminCreated>(AdminCreated);
    adminRepo = module.get<Repository<AdminEntity>>(getRepositoryToken(AdminEntity));
  });

  describe('handler', () => {
    it('should success', async () => {
      jest.spyOn(mockDataSource, 'getRepository').mockReturnValue(adminRepo);
      adminRepo.upsert = jest.fn();
      await handler.handle(<UpdateAdminMessage>message);
      expect(adminRepo.upsert).toHaveBeenCalled();
    });

    it('invalid data', async () => {
      const loggerLogSpy = jest.spyOn(handler['logger'], 'error');
      const result = await handler.handle(<UpdateAdminMessage>null);
      expect(loggerLogSpy).toHaveBeenCalled();
      expect(result).toBe(undefined);
    });
  });

  describe('validate', () => {
    it('validate true', async () => {
      const validated = handler.validateTask(<UpdateAdminMessage>message);
      expect(validated).toBe(true);
    });

    it('throw exception', async () => {
      expect(() => handler.validateTask(<UpdateAdminMessage>{ name: 'John' })).toThrowError();
    });
  });
});
