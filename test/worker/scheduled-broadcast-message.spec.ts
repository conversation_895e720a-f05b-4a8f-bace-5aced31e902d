import { Test, TestingModule } from '@nestjs/testing';
import { BroadcastMessageService } from '@modules/shared/broadcast-message/broadcast-message.service';
import { ScheduledBroadcastMessage } from '@modules/shared/worker/workers/tasks/scheduled-broadcast-message';

describe('ScheduledBroadcastMessage', () => {
  let handler: ScheduledBroadcastMessage;
  let mockBroadcastMessageService: jest.Mocked<BroadcastMessageService>;

  beforeEach(async () => {
    mockBroadcastMessageService = {
      findOneWithSelectedField: jest.fn(),
      processAndSendMessages: jest.fn(),
    } as unknown as jest.Mocked<BroadcastMessageService>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduledBroadcastMessage,
        { provide: BroadcastMessageService, useValue: mockBroadcastMessageService },
      ],
    }).compile();

    handler = module.get<ScheduledBroadcastMessage>(ScheduledBroadcastMessage);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handle', () => {
    it('should send broadcast message successfully when found', async () => {
      const mockBroadcastMessage = { id: '1', content: 'Message content' } as any;
      mockBroadcastMessageService.findOneWithSelectedField.mockResolvedValue(mockBroadcastMessage);

      const loggerSpy = jest.spyOn(handler['logger'], 'log');
      const result = await handler.handle({ broadcastMessageId: '1' });

      expect(mockBroadcastMessageService.findOneWithSelectedField).toHaveBeenCalledWith('1');
      expect(mockBroadcastMessageService.processAndSendMessages).toHaveBeenCalledWith(mockBroadcastMessage);
      expect(loggerSpy).toHaveBeenCalledWith('The message schedule was successfully sent.');
      expect(result).toBeUndefined();
    });

    it('should log and return when no message is found', async () => {
      const loggerSpy = jest.spyOn(handler['logger'], 'log');
      mockBroadcastMessageService.findOneWithSelectedField.mockResolvedValue(null);

      const result = await handler.handle({ broadcastMessageId: '1' });

      expect(mockBroadcastMessageService.findOneWithSelectedField).toHaveBeenCalledWith('1');
      expect(mockBroadcastMessageService.processAndSendMessages).not.toHaveBeenCalled();
      expect(loggerSpy).toHaveBeenCalledWith('There is no message to send.');
      expect(result).toBeUndefined();
    });

    it('should log an error if an exception occurs', async () => {
      const loggerSpy = jest.spyOn(handler['logger'], 'log');
      mockBroadcastMessageService.findOneWithSelectedField.mockRejectedValue(new Error('DB Error'));

      await handler.handle({ broadcastMessageId: '1' });

      expect(mockBroadcastMessageService.findOneWithSelectedField).toHaveBeenCalledWith('1');
      expect(mockBroadcastMessageService.processAndSendMessages).not.toHaveBeenCalled();
      expect(loggerSpy).toHaveBeenCalledWith(
        'An error occurred while processing the scheduled message.',
        expect.any(Error),
      );
    });
  });

  describe('validateTask', () => {
    it('should return true for a valid payload', () => {
      const result = handler.validateTask({ broadcastMessageId: '1' });
      expect(result).toBe(true);
    });

    it('should throw an error for an invalid payload', () => {
      expect(() => handler.validateTask(null)).toThrow('The payload is invalid.');
    });
  });
});
