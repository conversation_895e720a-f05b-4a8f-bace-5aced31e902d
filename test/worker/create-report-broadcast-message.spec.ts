import { Test, TestingModule } from '@nestjs/testing';
import { Queue } from 'bullmq';
import { BroadcastMessageService } from '@modules/shared/broadcast-message/broadcast-message.service';
import { AttachmentService } from '@modules/shared/attachment/attachment.service';
import { CONCURRENT_MESSAGE_FILENAME_SENT_SUCCESS, BULLMQ_MESSAGE_BROADCAST_QUEUE } from '@common/constants';
import { CreateReportBroadcastMessage } from '@modules/shared/worker/workers/tasks/create-report-broadcast-message';
import { getQueueToken } from '@nestjs/bullmq';

jest.mock('bullmq');

describe('CreateReportBroadcastMessage', () => {
  let handler: CreateReportBroadcastMessage;
  let mockQueue: jest.Mocked<Queue>;
  let mockBroadcastMessageService: jest.Mocked<BroadcastMessageService>;
  let mockAttachmentService: jest.Mocked<AttachmentService>;

  const mockHeaders = {
    'x-client-id': 'client-id',
    'x-client-type': 'client-type',
    'x-request-id': 'request-id',
  };

  beforeEach(async () => {
    mockQueue = {
      getJobs: jest.fn(),
    } as unknown as jest.Mocked<Queue>;

    mockBroadcastMessageService = {
      updateSentCountsAndStatus: jest.fn(),
    } as unknown as jest.Mocked<BroadcastMessageService>;

    mockAttachmentService = {
      uploadFile: jest.fn(),
    } as unknown as jest.Mocked<AttachmentService>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateReportBroadcastMessage,
        {
          provide: getQueueToken(BULLMQ_MESSAGE_BROADCAST_QUEUE),
          useValue: mockQueue,
        },
        { provide: BroadcastMessageService, useValue: mockBroadcastMessageService },
        { provide: AttachmentService, useValue: mockAttachmentService },
      ],
    }).compile();

    handler = module.get<CreateReportBroadcastMessage>(CreateReportBroadcastMessage);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handle', () => {
    it('should create a report and clean up BullMQ jobs when all stores are processed', async () => {
      const broadcastMessageId = '1';
      const mockPayload = { broadcastMessageId };

      // Mock completed jobs
      const mockCompletedJobs = [
        {
          id: `${broadcastMessageId}-batch-0`,
          data: { batch: [{ id: 'store-1' }, { id: 'store-2' }] },
          remove: jest.fn(),
        },
      ];

      // Mock failed jobs
      const mockFailedJobs = [
        {
          id: `${broadcastMessageId}-batch-1`,
          data: { batch: [{ id: 'store-3' }] },
          remove: jest.fn(),
        },
      ];

      // Mock empty arrays for other job states
      mockQueue.getJobs
        .mockResolvedValueOnce(mockCompletedJobs) // completed jobs
        .mockResolvedValueOnce(mockFailedJobs) // failed jobs
        .mockResolvedValueOnce([]) // active jobs
        .mockResolvedValueOnce([]) // waiting jobs
        .mockResolvedValueOnce([]) // delayed jobs
        .mockResolvedValueOnce(mockCompletedJobs) // completed jobs for cleanup
        .mockResolvedValueOnce(mockFailedJobs); // failed jobs for cleanup

      const loggerSpy = jest.spyOn(handler['logger'], 'log');
      const result = await handler.handle(mockPayload, mockHeaders);

      expect(mockQueue.getJobs).toHaveBeenCalledWith(['completed'], 0, -1);
      expect(mockQueue.getJobs).toHaveBeenCalledWith(['failed'], 0, -1);
      expect(mockAttachmentService.uploadFile).toHaveBeenCalledTimes(2); // Success and Fail CSV
      expect(mockBroadcastMessageService.updateSentCountsAndStatus).toHaveBeenCalledWith(broadcastMessageId, 2, 1);
      expect(mockCompletedJobs[0].remove).toHaveBeenCalled();
      expect(mockFailedJobs[0].remove).toHaveBeenCalled();
      expect(loggerSpy).toHaveBeenCalledWith('Created report successfully');
      expect(result).toBeUndefined();
    });

    it('should not create a report if stores are not fully processed', async () => {
      const broadcastMessageId = '1';
      const mockPayload = { broadcastMessageId };

      // Mock incomplete processing - 2 completed, 1 failed, 1 still active
      const mockCompletedJobs = [{ id: `${broadcastMessageId}-batch-0`, data: { batch: [{ id: 'store-1' }] } }];
      const mockFailedJobs = [{ id: `${broadcastMessageId}-batch-1`, data: { batch: [{ id: 'store-2' }] } }];
      const mockActiveJobs = [{ id: `${broadcastMessageId}-batch-2`, data: { batch: [{ id: 'store-3' }] } }];

      mockQueue.getJobs
        .mockResolvedValueOnce(mockCompletedJobs) // completed jobs
        .mockResolvedValueOnce(mockFailedJobs) // failed jobs
        .mockResolvedValueOnce(mockActiveJobs) // active jobs
        .mockResolvedValueOnce([]) // waiting jobs
        .mockResolvedValueOnce([]); // delayed jobs

      const loggerSpy = jest.spyOn(handler['logger'], 'log');
      const result = await handler.handle(mockPayload, mockHeaders);

      expect(mockBroadcastMessageService.updateSentCountsAndStatus).not.toHaveBeenCalled();
      expect(loggerSpy).toHaveBeenCalledWith('Create report fail, data for failing report', {
        totalJobs: 3,
        totalProcessedJobs: 2,
        broadcastMessageId: '1',
      });
      expect(result).toBeUndefined();
    });

    it('should log an error if an exception occurs', async () => {
      const loggerSpy = jest.spyOn(handler['logger'], 'log');
      const broadcastMessageId = '1';
      const mockPayload = { broadcastMessageId };

      mockQueue.getJobs.mockRejectedValue(new Error('BullMQ Error'));

      const result = await handler.handle(mockPayload, mockHeaders);

      // The error is caught in getBroadcastJobData and returns empty arrays,
      // so it logs the "Create report fail" message instead of the error
      expect(loggerSpy).toHaveBeenCalledWith('Create report fail, data for failing report', {
        totalJobs: 0,
        totalProcessedJobs: 0,
        broadcastMessageId: '1',
      });
      expect(result).toBeUndefined();
    });
  });

  describe('createReportCsvFile', () => {
    it('should create and upload a CSV file successfully', async () => {
      const storeIds = ['store-1', 'store-2'];
      const broadcastMessageId = '1';

      await handler.createReportCsvFile(storeIds, broadcastMessageId, CONCURRENT_MESSAGE_FILENAME_SENT_SUCCESS);

      expect(mockAttachmentService.uploadFile).toHaveBeenCalledWith(
        expect.objectContaining({ originalname: 'file.csv' }),
        null,
        expect.stringContaining(broadcastMessageId),
        CONCURRENT_MESSAGE_FILENAME_SENT_SUCCESS,
      );
    });

    it('should log an error if file upload fails', async () => {
      const storeIds = ['store-1', 'store-2'];
      const broadcastMessageId = '1';

      mockAttachmentService.uploadFile.mockRejectedValue(new Error('Upload Error'));

      await expect(
        handler.createReportCsvFile(storeIds, broadcastMessageId, CONCURRENT_MESSAGE_FILENAME_SENT_SUCCESS),
      ).rejects.toThrow(new Error('Upload Error'));
    });
  });

  describe('validateTask', () => {
    it('should return true for a valid payload', () => {
      const result = handler.validateTask({ broadcastMessageId: '1' });
      expect(result).toBe(true);
    });

    it('should throw an error for an invalid payload', () => {
      expect(() => handler.validateTask(null)).toThrow('The payload is invalid.');
    });
  });
});
