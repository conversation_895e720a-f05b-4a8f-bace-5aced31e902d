import { Test, TestingModule } from '@nestjs/testing';
import { RabbitMQEventType } from '@src/modules/shared/worker/enums/rabbitmq-event-type';
import { RabbitMQMessage } from '@src/modules/shared/worker/types/rabbitmq-payload';
import { DataSource } from 'typeorm';
import { UpsertAdminTeamStore } from '@modules/shared/worker/workers/tasks/upsert-admin-team-store';
import { AdminTeamStoreMessage } from '@modules/shared/worker/types/admin-team-store-message';
import { AdminInfoForNewStoreMessage } from '@src/modules/shared/worker/types/admin-info-for-new-store-message';
import { InquiryThreadService } from '@modules/shared/inquiry-thread/inquiry-thread.service';
import { SyncDataService } from '@modules/shared/search/services/sync-data.service';

// Mock transactionalEntityManager
const transactionalEntityManager = {
  delete: jest.fn().mockImplementation((entity) => jest.fn()),
  save: jest.fn().mockImplementation((entity) => jest.fn()),
};

// Mock dataSource.manager.transaction
const dataSource = {
  transaction: jest.fn().mockImplementation((callback) => {
    callback(transactionalEntityManager);
  }),
};

const inquiryThreadService = {
  refreshStoreAssignmentView: jest.fn(),
};

const syncDataService = {
  syncStoreAssignments: jest.fn(),
};

describe('Worker', () => {
  let handler: UpsertAdminTeamStore;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpsertAdminTeamStore,
        {
          provide: InquiryThreadService,
          useValue: inquiryThreadService,
        },
        {
          provide: SyncDataService,
          useValue: syncDataService,
        },
        {
          provide: DataSource,
          useValue: dataSource,
        },
      ],
    }).compile();

    handler = module.get<UpsertAdminTeamStore>(UpsertAdminTeamStore);
  });

  describe('handler', () => {
    it('should process admins_stores and teams_stores, quality_teams_Store upsert event', async () => {
      const message: RabbitMQMessage = {
        event: RabbitMQEventType.UpsertAdminTeamStores,
        content: {
          admin_assignments: [
            {
              store_id: '90f00439-4d95-49c0-b683-a42d3da97124',
              team_id: 'f9168e5b-604a-4c58-a934-d43ae509e027',
              admin_id: 'd6a5ac64-6efd-46a1-9fb1-d55d02759a9c',
              quality_admin_id: 'ed4d187e-872a-4b30-ac58-172c74aec262',
            },
            {
              store_id: '97e0ae51-f39f-4f03-8780-02b1bf684fb8',
              team_id: 'f9168e5b-604a-4c58-a934-d43ae509e027',
              admin_id: 'd6a5ac64-6efd-46a1-9fb1-d55d02759a9c',
              quality_admin_id: 'ed4d187e-872a-4b30-ac58-172c74aec262',
            },
            {
              store_id: 'ffeb49a1-34f7-489f-a91f-2edc0cd45928',
              quality_admin_id: 'ed4d187e-872a-4b30-ac58-172c74aec262',
            },
            {
              store_id: 'bea3bdaf-5622-41e3-b9a4-dd057326937e',
              team_id: 'f9168e5b-604a-4c58-a934-d43ae509e027',
              admin_id: 'd6a5ac64-6efd-46a1-9fb1-d55d02759a9c',
            },
          ],
        },
      };
      await handler.handle(<AdminTeamStoreMessage>message.content);

      expect(dataSource.transaction).toHaveBeenCalled();
    });

    it('invalid data', async () => {
      const message: RabbitMQMessage = {
        event: RabbitMQEventType.UpsertAdminTeamStores,
        content: null,
      };

      const loggerLogSpy = jest.spyOn(handler['logger'], 'error');
      await handler.handle(<AdminTeamStoreMessage>message.content);

      expect(loggerLogSpy).toHaveBeenCalled();
    });
  });

  describe('validate', () => {
    it('validate true', async () => {
      const message: RabbitMQMessage = {
        event: RabbitMQEventType.UpsertAdminTeamStores,
        content: {
          admin_assignments: [
            {
              store_id: '90f00439-4d95-49c0-b683-a42d3da97124',
              team_id: 'f9168e5b-604a-4c58-a934-d43ae509e027',
              admin_id: 'd6a5ac64-6efd-46a1-9fb1-d55d02759a9c',
              quality_admin_id: 'ed4d187e-872a-4b30-ac58-172c74aec262',
            },
            {
              store_id: '97e0ae51-f39f-4f03-8780-02b1bf684fb8',
              team_id: 'f9168e5b-604a-4c58-a934-d43ae509e027',
              admin_id: 'd6a5ac64-6efd-46a1-9fb1-d55d02759a9c',
              quality_admin_id: 'ed4d187e-872a-4b30-ac58-172c74aec262',
            },
            {
              store_id: 'bea3bdaf-5622-41e3-b9a4-dd057326937e',
              team_id: 'f9168e5b-604a-4c58-a934-d43ae509e027',
              admin_id: 'd6a5ac64-6efd-46a1-9fb1-d55d02759a9c',
              quality_admin_id: 'ed4d187e-872a-4b30-ac58-172c74aec262',
            },
          ],
        },
      };
      const valiated = handler.validateTask(<AdminTeamStoreMessage>message.content);

      expect(valiated).toBe(true);
    });

    it('throw exception', async () => {
      const message: RabbitMQMessage = {
        event: RabbitMQEventType.UpsertAdminTeamStores,
        content: {
          admin_assignments: [
            {
              team_id: 'f9168e5b-604a-4c58-a934-d43ae509e027',
              admin_id: 'd6a5ac64-6efd-46a1-9fb1-d55d02759a9c',
            } as AdminInfoForNewStoreMessage,
          ],
        },
      };

      expect(() => handler.validateTask(<AdminTeamStoreMessage>message.content)).toThrowError();
    });
  });
});
