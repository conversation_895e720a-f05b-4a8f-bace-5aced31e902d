import { DocumentService } from '@modules/shared/search/services/document.service';
import { AdminChangedDocument } from '@modules/shared/worker/types/admin-changed-document';
import { RemoveAdminFromStore } from '@modules/shared/worker/workers/tasks/remove-admin-from-store';

describe('RemoveAdminFromStore', () => {
  let removeAdminFromStore: RemoveAdminFromStore;
  let documentService: DocumentService;

  beforeEach(() => {
    documentService = {
      updateDocumentsByCondition: jest.fn(),
    } as unknown as DocumentService;

    removeAdminFromStore = new RemoveAdminFromStore(documentService);
  });

  describe('handle', () => {
    it('should call updateDocumentsByCondition with correct parameters', async () => {
      const payload: AdminChangedDocument = {
        admin_id: 'admin-123',
      };

      await removeAdminFromStore.handle(payload);

      expect(documentService.updateDocumentsByCondition).toHaveBeenCalledWith(
        { admin_id: payload.admin_id },
        { admin_id: '', team_id: '' },
      );
    });
  });

  describe('validateTask', () => {
    it('should return true for valid payload', () => {
      const payload: AdminChangedDocument = {
        admin_id: 'admin-123',
      };

      const result = removeAdminFromStore.validateTask(payload);

      expect(result).toBe(true);
    });

    it('should throw an error for invalid payload (missing admin_id)', () => {
      const payload: Partial<AdminChangedDocument> = {};

      expect(() => removeAdminFromStore.validateTask(payload as AdminChangedDocument)).toThrow(
        'Invalid payload structure',
      );
    });

    it('should throw an error for completely invalid payload', () => {
      const payload = null;

      expect(() => removeAdminFromStore.validateTask(payload as unknown as AdminChangedDocument)).toThrow(
        'Invalid payload structure',
      );
    });
  });
});
