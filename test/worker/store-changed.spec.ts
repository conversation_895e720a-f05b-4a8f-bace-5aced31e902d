import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { InquiryThreadEntity } from '@src/entities';
import { StoreChangedMessage } from '@src/modules/shared/worker/types/store-changed-message';
import { StoreChanged } from '@src/modules/shared/worker/workers/tasks/store-changed';
import { StoreEntity } from '@src/entities/store.entity';

const message: StoreChangedMessage = {
  store_id: '856625e3-c4f6-412a-8828-fe0cb85f6ac7',
  store_name: 'Example store',
  store_status_id: 3,
};

const mockTransactionalEntityManager = {
  findOne: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
};

const mockDataSource = {
  transaction: jest.fn().mockImplementation((fn) => fn(mockTransactionalEntityManager)),
  getRepository: jest.fn(),
};

describe('Worker', () => {
  let handler: StoreChanged;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StoreChanged,
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: getRepositoryToken(InquiryThreadEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(StoreEntity),
          useClass: Repository,
        },
      ],
    }).compile();

    handler = module.get<StoreChanged>(StoreChanged);
  });

  describe('handle', () => {
    it('should create a new store if it does not exist', async () => {
      mockTransactionalEntityManager.findOne.mockResolvedValueOnce(null);

      const updatedAtSpy = jest.fn();

      await handler.handle(message);

      expect(mockTransactionalEntityManager.save).toHaveBeenCalledWith(StoreEntity, {
        id: message.store_id,
        status: message.store_status_id,
        name: message.store_name,
      });

      expect(mockTransactionalEntityManager.update).toHaveBeenCalledWith(
        InquiryThreadEntity,
        { storeId: message.store_id },
        {
          storeName: message.store_name,
          updatedAt: expect.any(Function), // Ensure a function is passed
        },
      );

      // Ensure at least one call was made to `update`
      expect(mockTransactionalEntityManager.update).toHaveBeenCalledTimes(1);

      // Access the first update call
      const updateCall = mockTransactionalEntityManager.update.mock.calls[0][2]; // First update call arguments
      updateCall.updatedAt(updatedAtSpy); // Simulate calling the updatedAt function
    });
    it('should update an existing store', async () => {
      mockTransactionalEntityManager.findOne.mockResolvedValueOnce({ id: message.store_id });
      await handler.handle(message);

      expect(mockTransactionalEntityManager.update).toHaveBeenCalledWith(
        StoreEntity,
        { id: message.store_id },
        {
          name: message.store_name,
          status: message.store_status_id,
        },
      );
      expect(mockTransactionalEntityManager.update).toHaveBeenCalledWith(
        InquiryThreadEntity,
        { storeId: message.store_id },
        {
          storeName: message.store_name,
          updatedAt: expect.any(Function), // Ensure a function is passed
        },
      );
      expect(mockTransactionalEntityManager.update).toHaveBeenCalledTimes(3);
    });

    it('should return true after successful transaction', async () => {
      const result = await handler.handle(message);
      expect(result).toBe(true);
    });
  });

  describe('validateTask', () => {
    it('should validate successfully with correct message structure', () => {
      const validated = handler.validateTask(message);
      expect(validated).toBe(true);
    });

    it('should throw an exception for invalid message structure', () => {
      expect(() => handler.validateTask(<StoreChangedMessage>{ store_name: 'John' })).toThrowError();
    });
  });
});
