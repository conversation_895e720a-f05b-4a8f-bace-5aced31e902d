import { SyncDataService } from '@modules/shared/search/services/sync-data.service';
import { StoreChangedDocument } from '@modules/shared/worker/types/store-changed-document';
import { UpdateAdminTeamStore } from '@modules/shared/worker/workers/tasks/update-admin-team-store';

describe('UpdateAdminTeamStore', () => {
  let updateAdminTeamStore: UpdateAdminTeamStore;
  let syncDataService: SyncDataService;

  beforeEach(() => {
    syncDataService = {
      syncStoreAssignments: jest.fn(),
    } as unknown as SyncDataService;

    updateAdminTeamStore = new UpdateAdminTeamStore(syncDataService);
  });

  describe('handle', () => {
    it('should call syncStoreAssignments with correct parameters', async () => {
      const payload: StoreChangedDocument = {
        store_id: 'store-123',
      };

      await updateAdminTeamStore.handle(payload);

      expect(syncDataService.syncStoreAssignments).toHaveBeenCalledWith(payload.store_id);
    });
  });

  describe('validateTask', () => {
    it('should return true for valid payload', () => {
      const payload: StoreChangedDocument = {
        store_id: 'store-123',
      };

      const result = updateAdminTeamStore.validateTask(payload);

      expect(result).toBe(true);
    });

    it('should throw an error for invalid payload (missing store_id)', () => {
      const payload: Partial<StoreChangedDocument> = {};

      expect(() => updateAdminTeamStore.validateTask(payload as StoreChangedDocument)).toThrow(
        'Invalid payload structure',
      );
    });

    it('should throw an error for completely invalid payload', () => {
      const payload = null;

      expect(() => updateAdminTeamStore.validateTask(payload as unknown as StoreChangedDocument)).toThrow(
        'Invalid payload structure',
      );
    });
  });
});
