import { Test, TestingModule } from '@nestjs/testing';
import { DataSource } from 'typeorm';
import { CreateAdminInfoForNewStore } from '@modules/shared/worker/workers/tasks/create-admin-info-for-new-store';
import { AdminInfoForNewStoreMessage } from '@src/modules/shared/worker/types/admin-info-for-new-store-message';
import { InquiryThreadService } from '@modules/shared/inquiry-thread/inquiry-thread.service';
import { StoreEntity } from '@src/entities/store.entity';

// Mock transactionalEntityManager
const transactionalEntityManager = {
  delete: jest.fn(),
  save: jest.fn(),
  insert: jest.fn(),
  exists: jest.fn(),
};

const inquiryThreadService = {
  refreshStoreAssignmentView: jest.fn(),
};

// Mock DataSource
const mockDataSource = {
  transaction: jest.fn().mockImplementation((callback) => callback(transactionalEntityManager)),
};

describe('CreateAdminInfoForNewStore', () => {
  let handler: CreateAdminInfoForNewStore;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateAdminInfoForNewStore,
        {
          provide: InquiryThreadService,
          useValue: inquiryThreadService,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    handler = module.get<CreateAdminInfoForNewStore>(CreateAdminInfoForNewStore);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handle', () => {
    it('should process entities and call transaction', async () => {
      const payload: AdminInfoForNewStoreMessage = {
        store_id: '90f00439-4d95-49c0-b683-a42d3da97124',
        team_id: 'f9168e5b-604a-4c58-a934-d43ae509e027',
        admin_id: 'd6a5ac64-6efd-46a1-9fb1-d55d02759a9c',
        quality_admin_id: 'ed4d187e-872a-4b30-ac58-172c74aec262',
        store_name: 'Test Store',
      };

      transactionalEntityManager.exists.mockResolvedValue(false);

      await handler.handle(payload);

      expect(mockDataSource.transaction).toHaveBeenCalled();
      expect(transactionalEntityManager.delete).toHaveBeenCalledTimes(3);
      expect(transactionalEntityManager.save).toHaveBeenCalledTimes(4);
      expect(transactionalEntityManager.insert).toHaveBeenCalledWith(StoreEntity, {
        id: '90f00439-4d95-49c0-b683-a42d3da97124',
        name: 'Test Store',
        status: 1,
      });
    });

    it('should skip store insertion if store exists', async () => {
      const payload: AdminInfoForNewStoreMessage = {
        store_id: '90f00439-4d95-49c0-b683-a42d3da97124',
        team_id: 'f9168e5b-604a-4c58-a934-d43ae509e027',
      };

      transactionalEntityManager.exists.mockResolvedValue(true);

      await handler.handle(payload);

      expect(mockDataSource.transaction).toHaveBeenCalled();
      expect(transactionalEntityManager.exists).toHaveBeenCalledWith(StoreEntity, {
        where: { id: payload.store_id },
      });
      expect(transactionalEntityManager.insert).not.toHaveBeenCalled();
    });

    it('should log an error for invalid payload', async () => {
      const loggerSpy = jest.spyOn(handler['logger'], 'error');
      await handler.handle(null);

      expect(loggerSpy).toHaveBeenCalledWith('Process event team admin store: invalid content');
    });
  });

  describe('validateTask', () => {
    it('should return true for valid payload', () => {
      const payload: AdminInfoForNewStoreMessage = {
        store_id: '90f00439-4d95-49c0-b683-a42d3da97124',
      };

      const result = handler.validateTask(payload);
      expect(result).toBe(true);
    });

    it('should throw an error for invalid payload', () => {
      const payload: Partial<AdminInfoForNewStoreMessage> = {};

      expect(() => handler.validateTask(payload as AdminInfoForNewStoreMessage)).toThrowError('Invalid payload');
    });
  });
});
