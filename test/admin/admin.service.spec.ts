import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AdminEntity } from '@src/entities';
import { AdminService } from '@src/modules/shared/admin/admin.service';
import { HttpResponseFormatter } from '@common/response/response';
import { Logger } from '@nestjs/common';

describe('AdminService', () => {
  let adminService: AdminService;
  const mockedAdminRepo = {
    createQueryBuilder: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminService,
        {
          provide: getRepositoryToken(AdminEntity),
          useValue: mockedAdminRepo,
        },
      ],
    }).compile();

    adminService = module.get<AdminService>(AdminService);
  });

  describe('getAdmins()', () => {
    it('should return a list of admins for a given team ID', async () => {
      const teamId = 'some-team-id';
      const admins = [
        { id: 1, name: 'Admin 1', email: '<EMAIL>' },
        { id: 2, name: 'Admin 2', email: '<EMAIL>' },
      ];
      mockedAdminRepo.createQueryBuilder.mockReturnValue({
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(admins),
        addSelect: jest.fn().mockReturnThis(),
      });
      const result = await adminService.getAdmins(teamId);
      expect(result).toEqual(HttpResponseFormatter.responseOK({ data: admins }));
    });

    it('should handle errors and log them', async () => {
      const teamId = 'some-team-id';
      const error = new Error('Test Error');
      mockedAdminRepo.createQueryBuilder.mockReturnValue({
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockRejectedValue(error),
        addSelect: jest.fn().mockReturnThis(),
      });
      const loggerSpy = jest.spyOn(Logger.prototype, 'error');
      const result = await adminService.getAdmins(teamId);
      expect(loggerSpy).toHaveBeenCalledWith({ err: error.message });
      expect(result).toEqual(HttpResponseFormatter.responseInternalError({ errors: error.message }));
    });
  });

  describe('all()', () => {
    it('should return an array of all admin entities with their teams', async () => {
      const adminEntities = [
        { id: 1, name: 'Admin 1', email: '<EMAIL>', teams: [] },
        { id: 2, name: 'Admin 2', email: '<EMAIL>', teams: [] },
      ];
      mockedAdminRepo.find.mockResolvedValue(adminEntities);
      const result = await adminService.all();
      expect(mockedAdminRepo.find).toHaveBeenCalledWith({ relations: ['teams'] });
      expect(result).toEqual(adminEntities);
    });

    it('should return an empty array when an error occurs', async () => {
      mockedAdminRepo.find.mockRejectedValue(new Error('Error'));
      const result = await adminService.all();
      expect(mockedAdminRepo.find).toHaveBeenCalledWith({ relations: ['teams'] });
      expect(result).toEqual([]);
    });
  });

  describe('find()', () => {
    it('should return an array of admin entities', async () => {
      const adminEntities = [
        { id: 1, name: 'Admin 1', email: '<EMAIL>' },
        { id: 2, name: 'Admin 2', email: '<EMAIL>' },
      ];
      mockedAdminRepo.find.mockResolvedValue(adminEntities);
      const options = { where: { name: 'Admin' } };
      const result = await adminService.find(options);
      expect(mockedAdminRepo.find).toHaveBeenCalledWith(options);
      expect(result).toEqual(adminEntities);
    });

    it('should return an empty array when an error occurs', async () => {
      mockedAdminRepo.find.mockRejectedValue(new Error('Error'));
      const options = { where: { name: 'Admin' } };
      const result = await adminService.find(options);
      expect(mockedAdminRepo.find).toHaveBeenCalledWith(options);
      expect(result).toEqual([]);
    });
  });

  describe('findOneWithRelation()', () => {
    it('should return a single admin entity when found', async () => {
      const adminEntity = { id: 1, name: 'Admin 1', email: '<EMAIL>' };
      mockedAdminRepo.findOne = jest.fn().mockResolvedValue(adminEntity); // Mock implementation of findOne
      const conditions = { id: 1 };
      const relations = ['role', 'department'];
      const result = await adminService.findOneWithRelation(conditions, relations);
      expect(mockedAdminRepo.findOne).toHaveBeenCalledWith({
        where: conditions,
        relations: relations,
      });
      expect(result).toEqual(adminEntity);
    });

    it('should return undefined when no admin entity is found', async () => {
      mockedAdminRepo.findOne = jest.fn().mockResolvedValue(undefined);
      const conditions = { id: 999 };
      const relations = ['role', 'department'];
      const result = await adminService.findOneWithRelation(conditions, relations);
      expect(mockedAdminRepo.findOne).toHaveBeenCalledWith({
        where: conditions,
        relations: relations,
      });
      expect(result).toBeUndefined();
    });

    it('should call findOne with conditions and no relations', async () => {
      const conditions = { id: 1 };
      mockedAdminRepo.findOne.mockResolvedValueOnce({ id: 1, name: 'Admin 1' });
      const result = await adminService.findOneWithRelation(conditions);
      expect(mockedAdminRepo.findOne).toHaveBeenCalledWith({
        where: conditions,
        relations: [],
      });
      expect(result).toBeDefined();
    });
  });
});
