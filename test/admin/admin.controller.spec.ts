import { CURAMA_AUTHORIZATION_SERVICE, CURAMA_AUTH_SERVICE } from '@curama-auth/constants';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { AdminService } from '@src/modules/shared/admin/admin.service';
import { AdminController } from '@src/modules/terry/controllers/admin.controller';

describe('AdminController', () => {
  let controller: AdminController;
  const mockedAdminService = {
    getAdmins: jest.fn(),
    find: jest.fn(),
  };
  const mockedAuthService = {
    getOauth2Url: jest.fn(),
    signOut: jest.fn(),
    authenticate: jest.fn(),
  };

  const configService = {
    get: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminController],
      providers: [
        AdminService,
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: mockedAuthService,
        },
        {
          provide: ConfigService,
          useValue: configService,
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
      ],
    })
      .overrideProvider(AdminService)
      .useValue(mockedAdminService)
      .compile();
    controller = module.get<AdminController>(AdminController);
  });

  describe('getAdmins', () => {
    it('success', async () => {
      mockedAdminService.getAdmins.mockResolvedValue([]);
      const result = await controller.getAdmins('8cd11700-f520-47e2-ac0d-67fe1ebad559');
      expect(result).toBeTruthy();
    });
    it('fail', async () => {
      await expect(controller.getAdmins('id')).rejects.toThrow();
    });
    it('success without query teamId', async () => {
      mockedAdminService.getAdmins.mockResolvedValue([]);
      const result = await controller.getAdmins('');
      expect(result).toBeTruthy();
    });
  });
});
