import { CURAMA_AUTHORIZATION_SERVICE, CURAMA_AUTH_SERVICE } from '@curama-auth/constants';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { PAGINATOR } from '@src/common/constants/paginator';
import { MessageTemplateDto } from '@src/modules/shared/message-template/dtos/MessageTemplate.dto';
import { PaginateMessageTemplateDto } from '@src/modules/shared/message-template/dtos/paginate-message-template.dto';
import { MessageTemplateService } from '@src/modules/shared/message-template/message-template.service';
import { Py3Service } from '@vietnam/curama-py3-api';
import { MessageTemplateController } from '@src/modules/terry/controllers/message-template.controller';

describe('MessageTemplateController', () => {
  let controller: MessageTemplateController;
  const mockedMessageTemplateService = {
    getSubCategories: jest.fn(),
    getTemplates: jest.fn(),
    getAll: jest.fn(),
    save: jest.fn(),
    getById: jest.fn(),
    delete: jest.fn(),
  };
  const mockedAuthService = {
    getOauth2Url: jest.fn(),
    signOut: jest.fn(),
    authenticate: jest.fn(),
  };

  const configService = {
    get: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MessageTemplateController],
      providers: [
        MessageTemplateService,
        {
          provide: Py3Service,
          useValue: {},
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: mockedAuthService,
        },
        {
          provide: ConfigService,
          useValue: configService,
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
      ],
    })
      .overrideProvider(MessageTemplateService)
      .useValue(mockedMessageTemplateService)
      .compile();
    controller = module.get<MessageTemplateController>(MessageTemplateController);
  });

  describe('getSubCategories', () => {
    it('success', async () => {
      mockedMessageTemplateService.getSubCategories.mockResolvedValue([]);
      const result = await controller.getSubCategories('id');
      expect(result).toBeTruthy();
    });
  });
  describe('getTemplates', () => {
    it('success', async () => {
      mockedMessageTemplateService.getTemplates.mockResolvedValue([]);
      const result = await controller.getMessageTemplate('8cd11700-f520-47e2-ac0d-67fe1ebad559', 'search');
      expect(result).toBeTruthy();
    });
  });
  it('fail', () => {
    expect(() => controller.getMessageTemplate('id', '')).toThrowError();
  });
  describe('getAll()', () => {
    it('success', async () => {
      const searchQueryMessageTemplate = <PaginateMessageTemplateDto>{
        page: undefined,
        search: '',
        pageNumber: 1,
        perPage: PAGINATOR.TERRY,
      };

      mockedMessageTemplateService.getAll.mockResolvedValue([]);
      const result = await controller.getAll(searchQueryMessageTemplate, {} as any);
      expect(result).toBeTruthy();
    });
  });
  describe('store()', () => {
    it('create template message success', async () => {
      const resultExpect = {
        _csrf: '9qtFZ9OM-nFAIMxqYDT6L502T9ZtFJgA6-SY',
        status: 200,
        success: true,
        data: {
          id: 'ef16ea98-5374-4936-9139-4fda025a77d4',
          name: 'name',
          subject: 'subject',
          content: 'content',
          groupId: 'ef16ea98-5374-4936-9139-4fda025a77d5',
          sort: 1,
        },
      };
      const messageTemplateRequest = <MessageTemplateDto>{
        name: 'name',
        subject: 'subject',
        content: 'content',
        subCategoryId: 'ef16ea98-5374-4936-9139-4fda025a77d5',
        sort: 1,
      };

      mockedMessageTemplateService.save.mockResolvedValue(resultExpect);
      const result = await controller.store(messageTemplateRequest);
      expect(result).toEqual(resultExpect);
    });
  });

  describe('edit()', () => {
    it('success', async () => {
      mockedMessageTemplateService.getById.mockResolvedValue([]);
      const result = await controller.edit('id');
      expect(result).toBeTruthy();
    });
  });

  describe('delete()', () => {
    it('success', async () => {
      mockedMessageTemplateService.delete.mockResolvedValue([]);
      const result = await controller.delete('id');
      expect(result).toBeTruthy();
    });
  });
});
