import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { PAGINATOR } from '@src/common/constants/paginator';
import { MessageTemplateEntity, MessageTemplateGroupEntity } from '@src/entities';
import { PaginateMessageTemplateDto } from '@src/modules/shared/message-template/dtos/paginate-message-template.dto';
import { MessageTemplateService } from '@src/modules/shared/message-template/message-template.service';
import { Repository } from 'typeorm';
import { Request } from 'express';
import { HttpResponseFormatter } from '@src/common/response/response';

describe('MessageTemplateService', () => {
  let messageTemplateService: MessageTemplateService;
  let messageTemplateRepo: Repository<MessageTemplateEntity>;
  let messageTemplateGroupRepo: Repository<MessageTemplateGroupEntity>;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MessageTemplateService,
        {
          provide: getRepositoryToken(MessageTemplateGroupEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(MessageTemplateEntity),
          useClass: Repository,
        },
      ],
    }).compile();

    messageTemplateService = module.get<MessageTemplateService>(MessageTemplateService);
    messageTemplateRepo = module.get<Repository<MessageTemplateEntity>>(getRepositoryToken(MessageTemplateEntity));
    messageTemplateGroupRepo = module.get<Repository<MessageTemplateGroupEntity>>(
      getRepositoryToken(MessageTemplateGroupEntity),
    );
  });

  describe('getLargeCategories', () => {
    it('success', async () => {
      jest.spyOn(messageTemplateGroupRepo, 'find').mockResolvedValueOnce([]);
      const result = await messageTemplateService.getLargeCategories();
      expect(result).toBeTruthy();
    });

    it('fail', async () => {
      jest.spyOn(messageTemplateGroupRepo, 'find').mockRejectedValueOnce('error');
      const result = await messageTemplateService.getLargeCategories();
      expect(result).toBeTruthy();
    });
  });
  describe('getSubCategories', () => {
    it('success', async () => {
      jest.spyOn(messageTemplateGroupRepo, 'find').mockResolvedValueOnce([]);
      const result = await messageTemplateService.getSubCategories('id');
      expect(result).toBeTruthy();
    });

    it('fail', async () => {
      jest.spyOn(messageTemplateGroupRepo, 'find').mockRejectedValueOnce('error');
      const result = await messageTemplateService.getSubCategories('id');
      expect(result).toBeTruthy();
    });
  });
  describe('getMessageTemplateGroupByCategory', () => {
    it('should return the tree structure of message templates by category', async () => {
      const largeCategory = {
        id: 'largeCategoryId',
        name: 'Large Category',
        parentId: null,
        sort: 1,
      } as MessageTemplateGroupEntity;
      const subCategory = {
        id: 'subCategoryId',
        name: 'Sub Category',
        parentId: 'largeCategoryId',
        sort: 1,
      } as MessageTemplateGroupEntity;
      const template = {
        id: 'templateId',
        name: 'Template',
        groupId: 'subCategoryId',
        sort: 1,
      } as MessageTemplateEntity;

      jest.spyOn(messageTemplateGroupRepo, 'find').mockResolvedValueOnce([largeCategory]);
      jest.spyOn(messageTemplateGroupRepo, 'find').mockResolvedValueOnce([subCategory]);
      jest.spyOn(messageTemplateRepo, 'find').mockResolvedValueOnce([template]);

      const result = await messageTemplateService.getMessageTemplateGroupByCategory();

      expect(result).toEqual(
        HttpResponseFormatter.responseOK({
          data: [
            {
              ...largeCategory,
              subCategories: [
                {
                  ...subCategory,
                  templates: [template],
                },
              ],
            },
          ],
        }),
      );
    });

    it('should return an empty tree if no categories are found', async () => {
      jest.spyOn(messageTemplateGroupRepo, 'find').mockResolvedValueOnce([]);
      jest.spyOn(messageTemplateGroupRepo, 'find').mockResolvedValueOnce([]);
      jest.spyOn(messageTemplateRepo, 'find').mockResolvedValueOnce([]);

      const result = await messageTemplateService.getMessageTemplateGroupByCategory();

      expect(result).toEqual(HttpResponseFormatter.responseOK({ data: [] }));
    });
  });
  describe('getAllSubCategories', () => {
    it('success', async () => {
      jest.spyOn(messageTemplateGroupRepo, 'find').mockResolvedValueOnce([]);
      const result = await messageTemplateService.getAllSubCategories();
      expect(result).toBeTruthy();
    });

    it('fail', async () => {
      jest.spyOn(messageTemplateGroupRepo, 'find').mockRejectedValueOnce('error');
      const result = await messageTemplateService.getAllSubCategories();
      expect(result).toBeTruthy();
    });
  });
  describe('getTemplates', () => {
    it('success', async () => {
      jest.spyOn(messageTemplateRepo, 'find').mockResolvedValueOnce([]);
      const result = await messageTemplateService.getTemplates('id', 'search');
      expect(result).toBeTruthy();
    });

    it('fail', async () => {
      jest.spyOn(messageTemplateRepo, 'find').mockRejectedValueOnce('error');
      const result = await messageTemplateService.getTemplates('id');
      expect(result).toBeTruthy();
    });
  });
  describe('getAll()', () => {
    it('should return the list message template list paginated without search', async () => {
      const request = <Request>{ path: 'current-path' };
      const paginateMessageTemplateDto = <PaginateMessageTemplateDto>{
        page: undefined,
        search: '',
        pageNumber: 1,
        perPage: PAGINATOR.TERRY,
      };
      const perPage = PAGINATOR.SHOP;
      const pageNumber = 1;
      const list = <MessageTemplateEntity[]>(<unknown>[
        {
          id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          createdAt: '2023-09-02T23:07:00',
          updatedAt: '2023-09-02T23:07:00',
          name: 'msg3',
          content: 'content3',
          groupId: '1d8ce73e-5b6d-44d7-8a13-7cec1ccda4c7',
          displayUpdatedAt: '2023年7月24日 23:07',
          formatUpdatedAt: '2023年7月24日',
        },
        {
          id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          createdAt: '2023-09-02T23:07:00',
          updatedAt: '2023-09-02T23:07:00',
          name: 'msg3',
          content: 'content3',
          groupId: '1d8ce73e-5b6d-44d7-8a13-7cec1ccda4c7',
          displayUpdatedAt: '2023年7月24日 23:07',
          formatUpdatedAt: '2023年7月24日',
        },
      ]);
      const total = 2;

      // Mock the findAndCount method of the messageTemplateRepo
      jest.spyOn(messageTemplateRepo, 'findAndCount').mockResolvedValue([list, total]);

      const result = await messageTemplateService.getAll(paginateMessageTemplateDto, request);

      expect(result.messageTemplate).toEqual(list);
      expect(result.pagination).toEqual({
        pageNumber,
        total,
        perPage,
        request,
      });
      expect(messageTemplateRepo.findAndCount).toHaveBeenCalled();
    });

    it('should return the list message template list paginated with search', async () => {
      const request = <Request>{ path: 'current-path' };
      const searchQueryMessageTemplate = <PaginateMessageTemplateDto>{
        page: undefined,
        search: 'title1',
        pageNumber: 1,
        perPage: PAGINATOR.TERRY,
      };
      const perPage = PAGINATOR.TERRY;
      const pageNumber = 1;
      const list = <MessageTemplateEntity[]>(<unknown>[
        {
          id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          createdAt: '2023-09-02T23:07:00',
          updatedAt: '2023-09-02T23:07:00',
          name: 'msg2',
          content: 'content2',
          groupId: '1d8ce73e-5b6d-44d7-8a13-7cec1ccda4c7',
          displayUpdatedAt: '2023年7月24日 23:07',
          formatUpdatedAt: '2023年7月24日',
        },
        {
          id: 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778',
          createdAt: '2023-09-02T23:07:00',
          updatedAt: '2023-09-02T23:07:00',
          name: 'msg3',
          content: 'content3',
          groupId: '1d8ce73e-5b6d-44d7-8a13-7cec1ccda4c7',
          displayUpdatedAt: '2023年7月24日 23:07',
          formatUpdatedAt: '2023年7月24日',
        },
      ]);
      const total = 2;

      // Mock the findAndCount method of the inquiryThreadEntity
      jest.spyOn(messageTemplateRepo, 'findAndCount').mockResolvedValue([list, total]);

      const result = await messageTemplateService.getAll(searchQueryMessageTemplate, request);

      expect(result.messageTemplate).toEqual(list);
      expect(result.pagination).toEqual({
        pageNumber,
        total,
        perPage,
        request,
      });
      expect(messageTemplateRepo.findAndCount).toHaveBeenCalled();
    });
  });
  describe('save', () => {
    it('should create a new message template', async () => {
      const messageTemplateDto = {
        name: 'Test Template',
        content: 'Hello, {{name}}!',
        subCategoryId: '1',
      };

      const messageTemplateEntity = new MessageTemplateEntity();
      messageTemplateEntity.name = messageTemplateDto.name;
      messageTemplateEntity.content = messageTemplateDto.content;
      messageTemplateEntity.groupId = messageTemplateDto.subCategoryId;

      jest.spyOn(messageTemplateRepo, 'save').mockResolvedValue(messageTemplateEntity);

      const result = await messageTemplateService.save(messageTemplateDto);

      expect(result).toBeTruthy();
      expect(messageTemplateRepo.save).toHaveBeenCalledWith(messageTemplateEntity);
    });

    it('should update an existing message template', async () => {
      const messageTemplateDto = {
        id: '1',
        name: 'Test Template',
        content: 'Hello, {{name}}!',
        subCategoryId: '1',
      };

      const messageTemplateEntity = new MessageTemplateEntity();
      messageTemplateEntity.id = messageTemplateDto.id;
      messageTemplateEntity.name = messageTemplateDto.name;
      messageTemplateEntity.content = messageTemplateDto.content;
      messageTemplateEntity.groupId = messageTemplateDto.subCategoryId;

      jest.spyOn(messageTemplateRepo, 'findOne').mockResolvedValue(messageTemplateEntity);
      jest.spyOn(messageTemplateRepo, 'save').mockResolvedValue(messageTemplateEntity);

      const result = await messageTemplateService.save(messageTemplateDto);

      expect(result).toBeTruthy();
      expect(messageTemplateRepo.findOne).toHaveBeenCalledWith({ where: { id: messageTemplateDto.id } });
      expect(messageTemplateRepo.save).toHaveBeenCalledWith(messageTemplateEntity);
    });
  });

  describe('update', () => {
    it('should update a message template', async () => {
      const messageTemplateDto = {
        id: '1',
        name: 'Test Template',
        content: 'Hello, {{name}}!',
        subCategoryId: '1',
      };

      const messageTemplateEntity = new MessageTemplateEntity();
      messageTemplateEntity.id = messageTemplateDto.id;
      messageTemplateEntity.name = messageTemplateDto.name;
      messageTemplateEntity.content = messageTemplateDto.content;
      messageTemplateEntity.groupId = messageTemplateDto.subCategoryId;

      jest.spyOn(messageTemplateRepo, 'findOne').mockResolvedValue(messageTemplateEntity);
      jest.spyOn(messageTemplateRepo, 'save').mockResolvedValue(messageTemplateEntity);

      const result = await messageTemplateService.update(messageTemplateDto);

      expect(result).toBeTruthy();
      expect(messageTemplateRepo.findOne).toHaveBeenCalledWith({ where: { id: messageTemplateDto.id } });
      expect(messageTemplateRepo.save).toHaveBeenCalledWith(messageTemplateEntity);
    });
  });

  describe('getById()', () => {
    it('should return the message template', async () => {
      const messageTemplate = new MessageTemplateEntity();
      messageTemplate.id = 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778';
      messageTemplate.name = 'name';
      messageTemplate.content = 'content';
      messageTemplate.groupId = 'ef16ea98-5374-4936-9139-4fda025a77d5';

      jest.spyOn(messageTemplateRepo, 'findOne').mockResolvedValue(messageTemplate);

      const result = await messageTemplateService.getById('aff4241e-d4ac-4e08-aaeb-4b6d4220a778');
      expect(result).toEqual(messageTemplate);
      expect(messageTemplateRepo.findOne).toHaveBeenCalled();
    });
  });

  describe('delete()', () => {
    it('success', async () => {
      jest.spyOn(messageTemplateRepo, 'delete').mockResolvedValue({} as any);
      const result = await messageTemplateService.delete('id');

      expect(result).toBeTruthy();
    });

    it('fail', async () => {
      jest.spyOn(messageTemplateRepo, 'delete').mockRejectedValue('error');
      const result = await messageTemplateService.delete('id');
      expect(result).toBeTruthy();
    });
  });
});
