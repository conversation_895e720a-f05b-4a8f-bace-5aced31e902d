import { PutObjectCommand } from '@aws-sdk/client-s3';
import { AttachmentType } from '@src/common/enums/attachment-type';
import { AttachmentService } from '@src/modules/shared/attachment/attachment.service';
import * as crypto from 'crypto';

const s3ClientSendMock = jest.fn();
jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn().mockImplementation(() => ({
    send: s3ClientSendMock,
  })),
  PutObjectCommand: jest.fn(),
}));

describe('AttachmentService', () => {
  let attachmentService: AttachmentService;
  const mockUUID = crypto.randomUUID();
  const mockEnv = process.env.CONSULTING_MESSAGE_S3_BUCKET_NAME;
  beforeEach(() => {
    attachmentService = new AttachmentService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should upload a file and return the correct response', async () => {
    const file = {
      mimetype: 'image/jpg',
      buffer: Buffer.from('file buffer'),
    } as Express.Multer.File;
    const relationId = 'relation-id';
    const threadId = 'thread-id';
    const expectedPath = `consulting-message/${threadId}/${mockUUID}.jpg`;
    const expectedS3Path = `consulting-message/${threadId}/${mockUUID}.jpg`;

    jest.spyOn(crypto, 'randomUUID').mockReturnValue(mockUUID);
    const response = await attachmentService.uploadFile(file, relationId, threadId);

    expect(PutObjectCommand).toHaveBeenCalledWith({
      Bucket: mockEnv,
      Key: expectedS3Path,
      Body: file.buffer,
      ContentType: file.mimetype,
    });

    expect(response).toEqual({
      id: mockUUID,
      path: expectedPath,
      relationId,
      attachmentType: AttachmentType.Image,
    });
  });

  it('should be return InternalServerErrorException when upload file error', async () => {
    const file = {
      mimetype: 'image/jpg',
      buffer: Buffer.from('file buffer'),
    } as Express.Multer.File;
    const relationId = 'relation-id';
    const threadId = 'thread-id';
    jest.spyOn(crypto, 'randomUUID').mockReturnValue(mockUUID);
    s3ClientSendMock.mockImplementation(() => {
      throw new Error('error');
    });
    try {
      await attachmentService.uploadFile(file, relationId, threadId);
    } catch (error) {
      expect(error).toBeTruthy();
    }
  });

  it('should be return Error when mimetype empty', async () => {
    const file = {
      mimetype: '',
      buffer: Buffer.from('file buffer'),
    } as Express.Multer.File;
    const relationId = 'relation-id';
    const threadId = 'thread-id';
    jest.spyOn(crypto, 'randomUUID').mockReturnValue(mockUUID);
    try {
      await attachmentService.uploadFile(file, relationId, threadId);
    } catch (error) {
      expect(error).toBeTruthy();
    }
  });
});
