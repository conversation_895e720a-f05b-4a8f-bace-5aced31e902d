import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { TeamEntity } from '@src/entities';
import { TeamService } from '@src/modules/shared/team/team.service';
import { Repository } from 'typeorm';

describe('TeamService', () => {
  let teamService: TeamService;
  let teamRepo: Repository<TeamEntity>;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TeamService,
        {
          provide: getRepositoryToken(TeamEntity),
          useClass: Repository,
        },
      ],
    }).compile();

    teamService = module.get<TeamService>(TeamService);
    teamRepo = module.get<Repository<TeamEntity>>(getRepositoryToken(TeamEntity));
  });

  describe('find', () => {
    it('success', async () => {
      jest.spyOn(teamRepo, 'find').mockResolvedValueOnce([]);
      const result = await teamService.find();
      expect(result).toBeTruthy();
    });

    it('fail', async () => {
      jest.spyOn(teamRepo, 'find').mockRejectedValueOnce('error');
      const result = await teamService.find();
      expect(result).toEqual([]);
      expect(result).toBeTruthy();
    });
  });

  describe('findOne', () => {
    it('success', async () => {
      jest.spyOn(teamRepo, 'findOne').mockResolvedValueOnce({} as TeamEntity);
      const result = await teamService.findOne();
      expect(result).toBeTruthy();
    });
  });
});
