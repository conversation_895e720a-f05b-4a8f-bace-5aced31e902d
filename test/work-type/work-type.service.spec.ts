import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { WorkTypeEntity } from '@src/entities/work-type.entity';
import { WorkTypeService } from '@src/modules/shared/work-type/work-type.service';
import { Repository } from 'typeorm';

describe('WorkTypeService', () => {
  let service: WorkTypeService;
  let workTypeRepository: Repository<WorkTypeEntity>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkTypeService,
        {
          provide: getRepositoryToken(WorkTypeEntity),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<WorkTypeService>(WorkTypeService);
    workTypeRepository = module.get<Repository<WorkTypeEntity>>(getRepositoryToken(WorkTypeEntity));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('all', () => {
    it('should return an array of work types', async () => {
      jest.spyOn(workTypeRepository, 'find').mockResolvedValueOnce([]);
      const result = await service.find();
      expect(result).toBeTruthy();
    });

    it('should return an empty array if an error occurs', async () => {
      jest.spyOn(workTypeRepository, 'find').mockRejectedValueOnce(new Error());
      const result = await service.find();
      expect(result).toEqual([]);
    });
  });

  describe('getWorkTypeById', () => {
    it('should return the message template', async () => {
      const workType = new WorkTypeEntity();
      workType.id = 'aff4241e-d4ac-4e08-aaeb-4b6d4220a778';
      workType.name = 'name';

      jest.spyOn(workTypeRepository, 'findOne').mockResolvedValue(workType);

      const result = await service.getById('aff4241e-d4ac-4e08-aaeb-4b6d4220a778');
      expect(result).toEqual(workType);
      expect(workTypeRepository.findOne).toHaveBeenCalled();
    });
  });
});
