import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { TeamsStoresEntity } from '@src/entities';
import { TeamStoreService } from '@src/modules/shared/sync/services/team-store.service';

describe('TeamStoreService', () => {
  let teamStoreService: TeamStoreService;
  let teamsStoresEntityRepository: Repository<TeamsStoresEntity>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TeamStoreService,
        {
          provide: getRepositoryToken(TeamsStoresEntity),
          useClass: Repository,
        },
      ],
    }).compile();

    teamStoreService = module.get<TeamStoreService>(TeamStoreService);
    teamsStoresEntityRepository = module.get<Repository<TeamsStoresEntity>>(getRepositoryToken(TeamsStoresEntity));
  });

  describe('save', () => {
    it('should save the teamsStoresEntity', async () => {
      const teamsStoresEntity = <TeamsStoresEntity>{
        teamId: 'e50adce1-5936-446c-9e1c-199ec6a0c072',
        storeId: 'c50adce1-5936-446c-9e1c-199ec6a0c070',
      };

      jest.spyOn(teamsStoresEntityRepository, 'save').mockResolvedValueOnce(teamsStoresEntity);

      const result = await teamStoreService.save(teamsStoresEntity);

      expect(result).toBe(teamsStoresEntity);
      expect(teamsStoresEntityRepository.save).toBeCalledWith(teamsStoresEntity);
    });
  });

  describe('getByFields', () => {
    it('should return teamsStoresEntity by adminId and teamId', async () => {
      const teamId = 'e50adce1-5936-446c-9e1c-199ec6a0c072';
      const storeId = 'c50adce1-5936-446c-9e1c-199ec6a0c070';
      const teamsStoresEntity = <TeamsStoresEntity>{
        teamId,
        storeId,
      };

      jest.spyOn(teamsStoresEntityRepository, 'findOne').mockResolvedValueOnce(teamsStoresEntity);

      const result = await teamStoreService.getByFields(storeId);

      expect(result).toBe(teamsStoresEntity);
      expect(teamsStoresEntityRepository.findOne).toBeCalledWith({ where: { storeId } });
    });

    it('should return undefined if teamsStoresEntity not found', async () => {
      const storeId = 'c50adce1-5936-446c-9e1c-199ec6a0c070';

      jest.spyOn(teamsStoresEntityRepository, 'findOne').mockResolvedValueOnce(undefined);

      const result = await teamStoreService.getByFields(storeId);

      expect(result).toBeUndefined();
      expect(teamsStoresEntityRepository.findOne).toBeCalledWith({ where: { storeId } });
    });
  });
});
