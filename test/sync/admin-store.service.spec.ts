import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AdminStoreService } from '@src/modules/shared/sync/services/admin-store.service';
import { AdminsStoresEntity } from '@src/entities';

describe('AdminStoreService', () => {
  let adminStoreService: AdminStoreService;
  let adminsStoresEntityRepository: Repository<AdminsStoresEntity>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminStoreService,
        {
          provide: getRepositoryToken(AdminsStoresEntity),
          useClass: Repository,
        },
      ],
    }).compile();

    adminStoreService = module.get<AdminStoreService>(AdminStoreService);
    adminsStoresEntityRepository = module.get<Repository<AdminsStoresEntity>>(getRepositoryToken(AdminsStoresEntity));
  });

  describe('save', () => {
    it('should save the adminsStoresEntity', async () => {
      const adminsStoresEntity = <AdminsStoresEntity>{
        adminId: 'e50adce1-5936-446c-9e1c-199ec6a0c072',
        storeId: 'c50adce1-5936-446c-9e1c-199ec6a0c070',
      };

      jest.spyOn(adminsStoresEntityRepository, 'save').mockResolvedValueOnce(adminsStoresEntity);

      const result = await adminStoreService.save(adminsStoresEntity);

      expect(result).toBe(adminsStoresEntity);
      expect(adminsStoresEntityRepository.save).toBeCalledWith(adminsStoresEntity);
    });
  });

  describe('getByFields', () => {
    it('should return adminsStoresEntity by adminId and teamId', async () => {
      const adminId = 'e50adce1-5936-446c-9e1c-199ec6a0c072';
      const storeId = 'c50adce1-5936-446c-9e1c-199ec6a0c070';
      const adminsStoresEntity = <AdminsStoresEntity>{
        adminId,
        storeId,
      };

      jest.spyOn(adminsStoresEntityRepository, 'findOne').mockResolvedValueOnce(adminsStoresEntity);

      const result = await adminStoreService.getByFields(storeId);

      expect(result).toBe(adminsStoresEntity);
      expect(adminsStoresEntityRepository.findOne).toBeCalledWith({ where: { storeId } });
    });

    it('should return undefined if adminsStoresEntity not found', async () => {
      const storeId = 'c50adce1-5936-446c-9e1c-199ec6a0c070';

      jest.spyOn(adminsStoresEntityRepository, 'findOne').mockResolvedValueOnce(undefined);

      const result = await adminStoreService.getByFields(storeId);

      expect(result).toBeUndefined();
      expect(adminsStoresEntityRepository.findOne).toBeCalledWith({ where: { storeId } });
    });
  });
});
