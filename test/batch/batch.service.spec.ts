import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { Repository, FindOperator } from 'typeorm';
import { BroadcastMessageEntity } from '@src/entities/broadcast-message.entity';
import { BroadcastMessageStatus, CONCURRENT_MESSAGE_MQ_NAME } from '@common/constants';
import { RabbitMQEventType } from '@modules/shared/worker/enums/rabbitmq-event-type';
import { BatchService } from '@modules/shared/batch/batch.service';
import { getRedisToken } from '@liaoliaots/nestjs-redis';

describe('BatchService', () => {
  let batchService: BatchService;
  let mockBroadcastMessagesRepo: jest.Mocked<Repository<BroadcastMessageEntity>>;
  let mockAmqpConnection: Partial<AmqpConnection>;

  beforeEach(async () => {
    mockBroadcastMessagesRepo = {
      find: jest.fn(),
      update: jest.fn(),
    } as unknown as jest.Mocked<Repository<BroadcastMessageEntity>>;

    mockAmqpConnection = {
      publish: jest.fn(),
    };

    const mockRedis = {
      set: jest.fn(),
      get: jest.fn(),
      del: jest.fn(),
      zadd: jest.fn(),
      keys: jest.fn(),
      zrevrangebyscore: jest.fn(),
      zrem: jest.fn(),
      zcard: jest.fn(),
      scan: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BatchService,
        { provide: getRepositoryToken(BroadcastMessageEntity), useValue: mockBroadcastMessagesRepo },
        { provide: AmqpConnection, useValue: mockAmqpConnection },
        { provide: getRedisToken('default'), useValue: mockRedis },
      ],
    }).compile();

    batchService = module.get<BatchService>(BatchService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('run', () => {
    it('should log start and end of the batch process', async () => {
      const loggerLogSpy = jest.spyOn(batchService['logger'], 'log');
      const publishScheduledMessagesSpy = jest
        .spyOn(batchService, 'publishScheduledMessages')
        .mockResolvedValue(undefined);

      await batchService.run();

      expect(loggerLogSpy).toHaveBeenCalledWith('Running CronJob Batchserver');
      expect(publishScheduledMessagesSpy).toHaveBeenCalled();
      expect(loggerLogSpy).toHaveBeenCalledWith('Finished CronJob Batchserver');
    });
  });

  describe('publishScheduledMessages', () => {
    it('should publish messages scheduled at the current time', async () => {
      const mockMessages = [
        { id: 'msg1', status: BroadcastMessageStatus.SendingScheduled } as BroadcastMessageEntity,
        { id: 'msg2', status: BroadcastMessageStatus.SendingScheduled } as BroadcastMessageEntity,
      ];

      mockBroadcastMessagesRepo.find.mockResolvedValue(mockMessages);

      const publishMessageSpy = jest.spyOn(batchService as any, 'publishMessage').mockResolvedValue(undefined);

      await batchService.publishScheduledMessages();

      expect(mockBroadcastMessagesRepo.find).toHaveBeenCalledWith({
        where: {
          scheduledSendTime: expect.any(FindOperator), // Match any FindOperator instance
          status: BroadcastMessageStatus.SendingScheduled,
        },
      });
      expect(publishMessageSpy).toHaveBeenCalledTimes(2);
      expect(publishMessageSpy).toHaveBeenCalledWith(mockMessages[0]);
      expect(publishMessageSpy).toHaveBeenCalledWith(mockMessages[1]);
    });

    it('should log an error if something goes wrong', async () => {
      mockBroadcastMessagesRepo.find.mockRejectedValue(new Error('DB Error'));

      await expect(batchService.publishScheduledMessages()).rejects.toThrow('DB Error');
    });
  });

  describe('publishMessage', () => {
    it('should publish a message to RabbitMQ and update its status', async () => {
      const message = { id: 'msg1', status: BroadcastMessageStatus.SendingScheduled } as BroadcastMessageEntity;

      await (batchService as any)['publishMessage'](message);

      expect(mockAmqpConnection.publish).toHaveBeenCalledWith('taskManager', CONCURRENT_MESSAGE_MQ_NAME, {
        event: RabbitMQEventType.ScheduledBroadcastMessage,
        content: { broadcastMessageId: message.id },
      });
      expect(mockBroadcastMessagesRepo.update).toHaveBeenCalledWith(message.id, {
        status: BroadcastMessageStatus.Sending,
      });
    });

    it('should log an error if message publishing fails', async () => {
      const loggerErrorSpy = jest.spyOn(batchService['logger'], 'error');
      const message = { id: 'msg1', status: BroadcastMessageStatus.SendingScheduled } as BroadcastMessageEntity;

      (mockAmqpConnection.publish as jest.Mock).mockImplementation(() => {
        throw new Error('RabbitMQ Error');
      });

      await (batchService as any)['publishMessage'](message);

      expect(loggerErrorSpy).toHaveBeenCalledWith(`Error publishing message with ID: ${message.id}`, expect.any(Error));
      expect(mockBroadcastMessagesRepo.update).not.toHaveBeenCalled();
    });
  });
});
