import { CURAMA_AUTHORIZATION_SERVICE, CURAMA_AUTH_SERVICE } from '@curama-auth/constants';
import { Test, TestingModule } from '@nestjs/testing';
import { DraftService } from '@src/modules/shared/draft/draft.service';
import { Py3Service } from '@vietnam/curama-py3-api';
import { DraftController } from '@src/modules/terry/controllers/draft.controller';
jest.mock('@src/main', () => ({
  FileTypeModule: {
    fileTypeFromBuffer: jest.fn(),
  },
}));
describe('DraftController', () => {
  let controller: DraftController;
  const mockedDraftService = {
    delete: jest.fn(),
    saveDraftToMessage: jest.fn(),
    upsertDraft: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DraftController],
      providers: [
        {
          provide: DraftService,
          useValue: mockedDraftService,
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: {},
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
        {
          provide: Py3Service,
          useValue: {},
        },
      ],
    }).compile();
    controller = module.get<DraftController>(DraftController);
  });

  describe('delete', () => {
    it('success', () => {
      mockedDraftService.delete.mockResolvedValueOnce({});
      const result = controller.delete('');
      expect(result).toBeTruthy();
    });
  });

  describe('saveDraftToMessage', () => {
    it('success', async () => {
      mockedDraftService.saveDraftToMessage.mockResolvedValue({} as any);
      const res = await controller.saveDraftToMessage('id', {} as any, {} as any);
      expect(res).toBeTruthy();
    });
  });

  describe('upsertDraft', () => {
    it('success', async () => {
      mockedDraftService.upsertDraft.mockResolvedValue({} as any);
      const result = await controller.upsertDraft(
        [
          {
            mimetype: 'application/pdf',
          },
        ] as any,
        {} as any,
        {} as any,
        {} as any,
      );
      expect(result).toBeTruthy();
    });
  });
});
