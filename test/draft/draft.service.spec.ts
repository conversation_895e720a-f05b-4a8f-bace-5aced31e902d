import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DraftEntity } from '@src/entities/draft.entity';
import { AttachmentService } from '@src/modules/shared/attachment/attachment.service';
import { DraftService } from '@src/modules/shared/draft/draft.service';
import { InquiryThreadService } from '@src/modules/shared/inquiry-thread/inquiry-thread.service';
import { NgWordService } from '@src/modules/shared/ng-word/ng-word.service';
import { NotificationService } from '@src/modules/shared/notification/notification.service';
import { DataSource, Repository } from 'typeorm';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { getRedisToken } from '@liaoliaots/nestjs-redis';

declare global {
  interface String {
    escape(): string;
    urlize(): string;
    nl2br(): string;
  }
}
String.prototype.escape = jest.fn().mockReturnValue('');
String.prototype.urlize = jest.fn().mockReturnValue('');
String.prototype.nl2br = jest.fn().mockReturnValue('');

describe('DraftService', () => {
  let draftService: DraftService;
  let draftRepository: Repository<DraftEntity>;
  const mockDataSource = {
    transaction: jest.fn(),
  };
  const mockAttachmentService = { uploadFile: jest.fn() };
  const mockNgWordService = { checkNGWord: jest.fn() };
  const mockInquiryThreadService = {
    getOrCreateThread: jest.fn(),
    getByCondition: jest.fn(),
    saveThread: jest.fn(),
    updateThreadById: jest.fn(),
  };
  const mockNotificationService = {
    pushNotiNewMessageToStore: jest.fn(),
  };
  const mockedAmqpConnection = {
    publish: jest.fn(),
  };
  const mockRedis = { set: jest.fn(), get: jest.fn(), del: jest.fn() };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DraftService,
        {
          provide: getRepositoryToken(DraftEntity),
          useClass: Repository,
        },
        {
          provide: AmqpConnection,
          useValue: mockedAmqpConnection,
        },
        { provide: AttachmentService, useValue: mockAttachmentService },
        { provide: InquiryThreadService, useValue: mockInquiryThreadService },
        { provide: DataSource, useValue: mockDataSource },
        { provide: NgWordService, useValue: mockNgWordService },
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: getRedisToken('default'), useValue: mockRedis },
      ],
    }).compile();

    draftService = module.get<DraftService>(DraftService);
    draftRepository = module.get<Repository<DraftEntity>>(getRepositoryToken(DraftEntity));
  });

  describe('delete()', () => {
    const mockEntityManager = {
      create: jest.fn().mockReturnValue({}),
      update: jest.fn().mockResolvedValue({}),
      delete: jest.fn().mockResolvedValue({}),
      save: jest.fn().mockResolvedValue({}),
      findOne: jest.fn().mockResolvedValue({}),
      findOneBy: jest.fn().mockResolvedValue({}),
    };
    it('success', async () => {
      mockInquiryThreadService.getByCondition.mockResolvedValueOnce({ updatedAt: '2021-01-01' });
      mockDataSource.transaction.mockImplementation(async (callback) => {
        await callback(mockEntityManager);
        return true;
      });
      const result = await draftService.delete('id');
      expect(result).toBeTruthy();
    });
    it('fail', async () => {
      mockDataSource.transaction.mockRejectedValueOnce('error');
      const result = await draftService.delete('id');
      expect(result).toBeTruthy();
    });
  });
  describe('upsertDraft()', () => {
    const mockEntityManager = {
      create: jest.fn().mockResolvedValueOnce({}),
      update: jest.fn().mockResolvedValueOnce({}),
      delete: jest.fn().mockResolvedValueOnce({}),
      save: jest.fn().mockResolvedValueOnce({}),
      findOne: jest.fn().mockResolvedValueOnce({}),
      findOneBy: jest.fn().mockResolvedValueOnce({}),
      createQueryBuilder: jest.fn(() => ({
        delete: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValueOnce({}),
        getOne: jest.fn().mockResolvedValueOnce({ content: 'abc' }),
      })),
    };
    it('ngword', async () => {
      mockNgWordService.checkNGWord.mockResolvedValueOnce(['a']);
      const result = await draftService.upsertDraft([], { uploadedFile: '[]', content: 'a' } as any, 'id', {
        status_id: 3,
      } as any);
      expect(result).toBeTruthy();
    });
    it('success', async () => {
      mockInquiryThreadService.getOrCreateThread.mockResolvedValueOnce({ id: '1', updatedAt: '2021-01-01' });
      mockInquiryThreadService.updateThreadById.mockResolvedValueOnce({ id: '1' });
      mockNgWordService.checkNGWord.mockResolvedValueOnce([]);
      mockDataSource.transaction.mockImplementationOnce(async (callback) => {
        return await callback(mockEntityManager);
      });
      mockAttachmentService.uploadFile.mockResolvedValueOnce([{}]);
      const result = await draftService.upsertDraft(
        [{ mimetype: 'image/png', buffer: '' } as any],
        { uploadedFile: '[{"id":"123"},{"id":"456"}]', draftId: 'id' } as any,
        'id',
        { status_id: 3 } as any,
      );
      expect(result).toBeTruthy();
    });
    it('success', async () => {
      jest.spyOn(draftRepository, 'query').mockResolvedValueOnce([{ uuid_generate_v4: '123' }]);
      mockInquiryThreadService.getOrCreateThread.mockResolvedValueOnce({ id: '1', updatedAt: '2021-01-01' });
      mockInquiryThreadService.updateThreadById.mockResolvedValueOnce({ id: '1' });
      mockNgWordService.checkNGWord.mockResolvedValueOnce([]);
      mockDataSource.transaction.mockImplementationOnce(async (callback) => {
        return await callback(mockEntityManager);
      });
      const result = await draftService.upsertDraft(
        [{ mimetype: 'image/png', buffer: '' } as any],
        { uploadedFile: '[{"id":"123"},{"id":"456"}]' } as any,
        'id',
        { status_id: 3 } as any,
      );
      expect(result).toBeTruthy();
    });
    it('fail', async () => {
      mockDataSource.transaction.mockRejectedValueOnce('error');
      const result = await draftService.upsertDraft([], { uploadedFile: '[]' } as any, 'id', { status_id: 3 } as any);
      expect(result).toBeTruthy();
    });
    it('fail for stopped store', async () => {
      const result = await draftService.upsertDraft([], { uploadedFile: '[]' } as any, 'id', { status_id: 6 } as any);
      expect(result).toBeTruthy();
    });
  });
  describe('saveDraftToMessage()', () => {
    const mockEntityManager = {
      create: jest.fn().mockReturnValueOnce({}),
      update: jest.fn().mockResolvedValueOnce({}),
      delete: jest.fn().mockResolvedValueOnce({}),
      save: jest.fn().mockResolvedValueOnce({}),
      findOne: jest.fn().mockResolvedValueOnce({ status: 2, content: 'abc' }),
      findOneBy: jest.fn().mockResolvedValueOnce({ status: 1 }),
    };
    mockNotificationService.pushNotiNewMessageToStore.mockResolvedValueOnce({});
    it('success', async () => {
      mockDataSource.transaction.mockImplementationOnce(async (callback) => {
        return await callback(mockEntityManager);
      });
      const result = await draftService.saveDraftToMessage('id', 'id', {} as any);
      expect(result).toBeTruthy();
    });
    it('fail', async () => {
      mockDataSource.transaction.mockRejectedValueOnce('error');
      const result = await draftService.saveDraftToMessage('id', 'id', {} as any);
      expect(result).toBeTruthy();
    });
  });
});
