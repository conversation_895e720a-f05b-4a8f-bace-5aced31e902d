import { CURAMA_AUTHORIZATION_SERVICE, CURAMA_AUTH_SERVICE } from '@curama-auth/constants';
import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON><PERSON>roller } from '@src/modules/terry/terry.controller';
import { TerryService } from '@src/modules/terry/terry.service';
import { ConfigService } from '@nestjs/config';
import { Py3Service, StoreDetailDto } from '@vietnam/curama-py3-api';
import { ThreadTypeKey } from '@src/common/enums/thread-type';
import { I18nContext } from 'nestjs-i18n';
import { TeamService } from '@src/modules/shared/team/team.service';
import { AdminService } from '@src/modules/shared/admin/admin.service';
import { NotFoundException } from '@nestjs/common';
import { QUALITY_TEAM_NAME } from '@common/constants';

describe('TerryController', () => {
  let controller: Terry<PERSON>ontroller;
  let mockI18nContext: jest.Mocked<I18nContext>;

  const mockedTerryService = {
    getStoreThreadDetails: jest.fn(),
    getPagingMessages: jest.fn(),
    getInquiryThreads: jest.fn(),
    getThreadsByStore: jest.fn(),
    storeAssignment: jest.fn(),
  };

  const mockAdminService = {
    find: jest.fn(),
    findOneWithRelation: jest.fn().mockImplementation(() => ({
      teams: [{ name: QUALITY_TEAM_NAME }],
    })),
  };

  const mockTeamService = {
    find: jest.fn(),
  };

  const mockedAuthService = {
    getOauth2Url: jest.fn(),
    signOut: jest.fn(),
    authenticate: jest.fn(),
  };

  const configService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TerryController],
      providers: [
        {
          provide: TerryService,
          useValue: mockedTerryService,
        },
        {
          provide: AdminService,
          useValue: mockAdminService,
        },
        {
          provide: TeamService,
          useValue: mockTeamService,
        },
        {
          provide: Py3Service,
          useValue: {},
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: mockedAuthService,
        },
        {
          provide: ConfigService,
          useValue: configService,
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
      ],
    }).compile();
    controller = module.get<TerryController>(TerryController);
    mockI18nContext = {
      t: jest.fn().mockReturnValue('Translated string'),
      lang: 'en',
      service: {} as any,
      translate: jest.fn(),
      validate: jest.fn(),
    } as any;
  });

  describe('getThreads', () => {
    it('success', async () => {
      mockedTerryService.getInquiryThreads.mockResolvedValue([]);
      mockI18nContext.validate.mockResolvedValue([]);
      const result = await controller.getThreads({} as any, mockI18nContext);
      expect(result).toBeTruthy();
    });

    it('invalid UUID', async () => {
      const query = { storeId: 'invalidUUID' };
      mockedTerryService.getInquiryThreads.mockResolvedValue({});
      mockI18nContext.validate.mockResolvedValue([
        {
          property: 'storeId',
          constraints: { isUUID: 'storeId must be an UUID' },
        },
      ]);

      const result = await controller.getThreads(query as any, mockI18nContext);
      expect(result.errors).toBeTruthy();
    });
  });

  describe('getStoreThreadMessageByStoreId', () => {
    it('success', async () => {
      const result = await controller.getStoreThreadMessageByStoreId('id', { teams: [] } as any);
      expect(result).toHaveProperty('url');
    });
  });

  describe('getStoreThreadDetails', () => {
    it('get success messages in consultant thread', async () => {
      mockedTerryService.getStoreThreadDetails.mockResolvedValue([]);
      const result = await controller.getStoreThreadMessageByStoreIdAndType(
        'id',
        ThreadTypeKey.CuramaThread,
        {
          can: () => true,
        } as any,
        { store: { id: 'id' } as StoreDetailDto },
      );
      expect(result).toBeTruthy();
    });

    it('get success messages in quality thread', async () => {
      mockedTerryService.getStoreThreadDetails.mockResolvedValue([]);
      const result = await controller.getStoreThreadMessageByStoreIdAndType(
        'id',
        ThreadTypeKey.QualityThread,
        {
          teams: [
            {
              name: '品質管理',
            },
          ],
          can: () => true,
        } as any,
        { store: { id: 'id' } as StoreDetailDto },
      );
      expect(result).toBeTruthy();
    });

    it('not found when thread type is invalid', async () => {
      mockedTerryService.getStoreThreadDetails.mockResolvedValue([]);
      const result = await controller
        .getStoreThreadMessageByStoreIdAndType(
          'id',
          'invalidType' as any,
          {
            teams: [
              {
                name: '品質管理',
              },
            ],
            can: () => true,
          } as any,
          { store: { id: 'id' } as StoreDetailDto },
        )
        .catch((e) => e);

      expect(result).toBeInstanceOf(NotFoundException);
    });
  });
});
