import { Test, TestingModule } from '@nestjs/testing';
import { TerryService } from '@src/modules/terry/terry.service';
import { ConfigService } from '@nestjs/config';
import { StoreAssignmentDto } from '@modules/terry/dtos/store-assignment.dto';
import { I18nContext } from 'nestjs-i18n';
import { InternalApiController } from '@src/modules/terry/controllers/internal-api.controller';
import { MessageTemplateService } from '../../src/modules/shared/message-template/message-template.service';
import { SelectMessageTemplateDto } from '../../src/modules/terry/dtos/select-message-template.dto';
import { AdminService } from '@src/modules/shared/admin/admin.service';
import { TeamService } from '@src/modules/shared/team/team.service';

describe('InternalApiController', () => {
  let controller: InternalApiController;
  let mockI18nContext: jest.Mocked<I18nContext>;

  const mockedTerryService = {
    storeAssignment: jest.fn(),
    selectPatrolMessageTemplate: jest.fn(),
  };

  const mockedMessageTemplateService = {
    getMessageTemplateGroupByCategory: jest.fn(),
    getTeamAndAdminData: jest.fn(),
    getLargeCategories: jest.fn(),
    getSubCategories: jest.fn(),
    getTemplates: jest.fn(),
  };
  const mockedAdminService = {
    getAdmins: jest.fn(),
    find: jest.fn(),
  };
  const mockedTeamService = {
    find: jest.fn(),
  };

  const configService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [InternalApiController],
      providers: [
        {
          provide: TerryService,
          useValue: mockedTerryService,
        },
        {
          provide: MessageTemplateService,
          useValue: mockedMessageTemplateService,
        },
        {
          provide: AdminService,
          useValue: mockedAdminService,
        },
        {
          provide: TeamService,
          useValue: mockedTeamService,
        },
        {
          provide: ConfigService,
          useValue: configService,
        },
      ],
    }).compile();
    controller = module.get<InternalApiController>(InternalApiController);
    mockI18nContext = {
      t: jest.fn().mockReturnValue('Translated string'),
      lang: 'en',
      service: {} as any,
      translate: jest.fn(),
      validate: jest.fn(),
    } as any;
  });

  describe('getMessageTemplateGroupByCategory()', () => {
    it('success', async () => {
      mockedMessageTemplateService.getMessageTemplateGroupByCategory.mockResolvedValue({});
      const result = await controller.getMessageTemplateGroupByCategory('search');
      expect(result).toBeTruthy();
    });
  });

  describe('storeAssignment()', () => {
    it('success', async () => {
      const storeId = '0d69147a-fd9b-4149-ad88-54539f8ff86e';
      const storeAssignmentDto: StoreAssignmentDto = {
        adminId: 'b5ac1ddb-ef5a-47c1-9fa3-b9c6b4671889',
        teamId: 'ad987e85-193d-4cca-bacb-555ebc75f031',
        qualityAdminId: '22250c83-c7dc-432f-99ff-4bac59180e02',
      };
      mockedTerryService.storeAssignment.mockResolvedValue(true);
      const result = await controller.storeAssignment(storeId, storeAssignmentDto, mockI18nContext);
      expect(mockedTerryService.storeAssignment).toHaveBeenCalledWith(storeId, storeAssignmentDto);
      expect(mockI18nContext.t).toHaveBeenCalledWith('general.storeSuccessAssignment');
      expect(result).toHaveProperty('message');
    });
  });

  describe('selectPatrolMessageTemplate', () => {
    it('success', async () => {
      mockedTerryService.selectPatrolMessageTemplate.mockResolvedValueOnce({});
      const result = await controller.selectPatrolMessageTemplate({} as SelectMessageTemplateDto, 'id');
      expect(result).toBeTruthy();
    });
  });

  describe('getAdmins()', () => {
    it('should return admins when teamId is not provided', async () => {
      const mockAdmins = 'mockAdmins';
      const select = ['id', 'name'];
      mockedAdminService.find.mockResolvedValue(mockAdmins);

      const result = await controller.getAdmins('', select);

      expect(mockedAdminService.find).toHaveBeenCalledWith({ select: ['id', 'name'] });
      expect(result).toHaveProperty('data');
    });

    it('should return admins when teamId is provided', async () => {
      const teamId = '0d69147a-fd9b-4149-ad88-54539f8ff86e';
      const select = ['id', 'name'];
      const mockAdmins = 'mockAdmins';
      mockedAdminService.getAdmins.mockResolvedValue(mockAdmins);

      const result = await controller.getAdmins(teamId, select);

      expect(mockedAdminService.getAdmins).toHaveBeenCalledWith(teamId, select);
      expect(result).toBe(mockAdmins);
    });

    it('should throw BadRequestException when teamId is not a valid uuid v4', async () => {
      const teamId = 'invalid-uuid';
      const select = ['id', 'name'];
      await expect(controller.getAdmins(teamId, select)).rejects.toThrow('Invalid UUID v4');
    });
  });

  describe('getTeams()', () => {
    it('success', async () => {
      const mockTeams = 'mockTeams';
      const select = ['id', 'name'];
      const relations = ['admins'];
      mockedTeamService.find.mockResolvedValue(mockTeams);

      const result = await controller.getTeams(select, relations);

      expect(result).toHaveProperty('data');
    });
  });
});
