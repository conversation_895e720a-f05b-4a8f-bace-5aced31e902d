import { Test, TestingModule } from '@nestjs/testing';
import { TerryService } from '@src/modules/terry/terry.service';
import { DataSource, Repository } from 'typeorm';
import { AdminService } from '@src/modules/shared/admin/admin.service';
import { InquiryThreadQueryRequest } from '@src/modules/terry/dtos/inquiry-thread-query-request.dto';
import { PAGINATOR } from '@src/common/constants/paginator';
import { Request } from 'express';
import { getRepositoryToken } from '@nestjs/typeorm';
import { QualityAdminsStoresEntity } from '@src/entities/quality-admins-stores.entity';
import { AdminEntity, AdminsStoresEntity, InquiryThreadEntity } from '@src/entities';
import { TaskService } from '@src/modules/shared/task/task.service';
import { MessageTemplateService } from '@src/modules/shared/message-template/message-template.service';
import { TeamService } from '@src/modules/shared/team/team.service';
import { Py3Service, StoreDetailDto } from '@vietnam/curama-py3-api';
import { InquiryThreadService } from '@src/modules/shared/inquiry-thread/inquiry-thread.service';
import { MessageService } from '@src/modules/shared/message/message.service';
import { NgWordService } from '@src/modules/shared/ng-word/ng-word.service';
import { CanaryReleaseStoreService } from '@src/modules/shared/canary-release-store/canary-release-store.service';
import { SelectMessageTemplateDto } from '@src/modules/terry/dtos/select-message-template.dto';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';

declare global {
  interface String {
    escape(): string;
    urlize(): string;
    nl2br(): string;
  }
}
String.prototype.escape = jest.fn().mockReturnValue('');
String.prototype.urlize = jest.fn().mockReturnValue('');

String.prototype.nl2br = jest.fn().mockReturnValue('');
describe('TerryService', () => {
  let terryService: TerryService;
  let adminsStoresRepo, qualityAdminsStoresRepo, inquiryThreadRepo;
  const mockEntityManager = {
    create: () => {},
    delete: () => {},
    update: () => {},
    findOneBy: () => {
      return { id: 'id', name: 'name' };
    },
    save: () => {
      return {
        id: 'id',
      };
    },
  };
  const mockMessageTemplateService = {
    getLargeCategories: jest.fn(),
    getById: jest.fn(),
  };
  const mockTeamService = { find: jest.fn(), getQualityTeam: jest.fn() };
  const mockPy3Service = { getStoreAccountInfo: jest.fn() };
  const mockDataSource = {
    transaction: jest.fn().mockImplementation((cb) => {
      cb(mockEntityManager);
    }),
  };
  const mockMessageService = { findAndCount: jest.fn() };
  const mockTaskService = {
    findAndCount: jest.fn(),
    countTotal: jest.fn(),
    generateTaskNo: jest.fn(),
  };

  const mockNgWordService = { checkNGWord: jest.fn() };
  const mockInquiryThreadService = {
    getById: jest.fn(),
    getAll: jest.fn(),
    getByCondition: jest.fn(),
    getOrCreateThread: jest.fn(),
  };
  const mockAdminService = {
    all: jest.fn(),
  };
  const mockCanaryReleaseStoreService = {
    checkRelease: jest.fn(),
  };
  const mockedAmqpConnection = {
    publish: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TerryService,
        {
          provide: getRepositoryToken(QualityAdminsStoresEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(AdminsStoresEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(AdminEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(InquiryThreadEntity),
          useClass: Repository,
        },
        {
          provide: AmqpConnection,
          useValue: mockedAmqpConnection,
        },
        { provide: TaskService, useValue: mockTaskService },
        { provide: MessageTemplateService, useValue: mockMessageTemplateService },
        { provide: TeamService, useValue: mockTeamService },
        { provide: Py3Service, useValue: mockPy3Service },
        { provide: InquiryThreadService, useValue: mockInquiryThreadService },
        { provide: DataSource, useValue: mockDataSource },
        { provide: MessageService, useValue: mockMessageService },
        { provide: NgWordService, useValue: mockNgWordService },
        { provide: AdminService, useValue: mockAdminService },
        { provide: CanaryReleaseStoreService, useValue: mockCanaryReleaseStoreService },
      ],
    }).compile();

    terryService = module.get<TerryService>(TerryService);
    qualityAdminsStoresRepo = module.get<Repository<QualityAdminsStoresEntity>>(
      getRepositoryToken(QualityAdminsStoresEntity),
    );
    adminsStoresRepo = module.get<Repository<AdminsStoresEntity>>(getRepositoryToken(AdminsStoresEntity));
    inquiryThreadRepo = module.get<Repository<InquiryThreadEntity>>(getRepositoryToken(InquiryThreadEntity));
  });

  describe('getStoreThreadDetails()', () => {
    it('success', async () => {
      jest.spyOn(adminsStoresRepo, 'findOne').mockResolvedValueOnce({ admin: {} });
      mockInquiryThreadService.getByCondition.mockResolvedValueOnce({
        storeId: 'id',
        id: 'id',
        title: 'title',
        threadTypeId: '1',
        status: 1,
        draft: { content: '>' },
      });
      mockMessageTemplateService.getLargeCategories.mockResolvedValueOnce({});
      mockPy3Service.getStoreAccountInfo.mockResolvedValueOnce({ store: {} });
      mockTaskService.findAndCount.mockResolvedValueOnce([[{}], 1]);
      mockTaskService.countTotal.mockResolvedValueOnce(2);
      mockMessageService.findAndCount.mockResolvedValueOnce([[{ deletedAt: new Date(), content: '<ad>' }], 1]);
      mockCanaryReleaseStoreService.checkRelease.mockResolvedValueOnce(true);
      const result = await terryService.getStoreThreadDetails({ id: 'id' } as StoreDetailDto, 1, true);
      expect(result).toBeTruthy();
    });
    it('success not release', async () => {
      mockCanaryReleaseStoreService.checkRelease.mockResolvedValueOnce(false);
      const result = await terryService.getStoreThreadDetails({ id: 'id' } as StoreDetailDto, 1, true);
      expect(result).toBeTruthy();
    });
    it('fail', async () => {
      jest.spyOn(qualityAdminsStoresRepo, 'findOne').mockResolvedValueOnce({ admin: {} });
      mockInquiryThreadService.getByCondition.mockResolvedValueOnce(null);
      mockMessageTemplateService.getLargeCategories.mockResolvedValueOnce({});
      mockPy3Service.getStoreAccountInfo.mockResolvedValueOnce({ store: {} });
      mockCanaryReleaseStoreService.checkRelease.mockResolvedValueOnce(true);
      const result = await terryService.getStoreThreadDetails({ id: 'id' } as StoreDetailDto, 2, true);
      expect(result).toBeTruthy();
    });
  });
  describe('getInquiryThreads()', () => {
    const inquiryThreadQueryRequest = <InquiryThreadQueryRequest>{
      page: undefined,
      storeId: '123',
      assignmentTeamId: '',
      assignmentAdminId: '',
      status: 1,
      updatedAt: '',
      search: '',
      pageNumber: 1,
      perPage: PAGINATOR.TERRY,
    };

    it('success', async () => {
      mockInquiryThreadService.getAll.mockResolvedValueOnce({});
      mockTeamService.find.mockResolvedValueOnce([]);
      mockAdminService.all.mockResolvedValueOnce([]);
      const result = await terryService.getInquiryThreads(inquiryThreadQueryRequest, {
        path: 'current-path',
        query: {},
      } as Request);
      expect(result).toBeTruthy();
    });

    it('fail by exception', async () => {
      mockInquiryThreadService.getAll.mockRejectedValueOnce('error');
      mockTeamService.find.mockResolvedValueOnce([]);
      mockAdminService.all.mockResolvedValueOnce([]);
      const result = await terryService.getInquiryThreads(inquiryThreadQueryRequest, {
        path: 'current-path',
        query: {},
      } as Request);
      expect(result).toBeTruthy();
    });
  });

  describe('storeAssignment()', () => {
    it('should assign admins and teams to stores', async () => {
      const storeAssignmentDto = {
        adminId: 'b5ac1ddb-ef5a-47c1-9fa3-b9c6b4671889',
        teamId: 'ad987e85-193d-4cca-bacb-555ebc75f031',
        qualityAdminId: '22250c83-c7dc-432f-99ff-4bac59180e02',
      };
      await terryService.storeAssignment('0d69147a-fd9b-4149-ad88-54539f8ff86e', storeAssignmentDto);
      expect(mockDataSource.transaction).toHaveBeenCalled();
    });
  });

  describe('selectPatrolMessageTemplate()', () => {
    it('not exist message template', async () => {
      mockMessageTemplateService.getById.mockResolvedValueOnce(null);
      const result = await terryService.selectPatrolMessageTemplate({
        adminId: 'string',
        storeId: 'string',
        storeName: 'string',
        templateId: 'string',
        customerName: 'string',
        reservationCode: 'string',
        serviceName: 'string',
      } as SelectMessageTemplateDto);
      expect(result).toBeTruthy();
    });

    it('success with exist thread, has draft', async () => {
      mockMessageTemplateService.getById.mockResolvedValueOnce({ content: 'content' });
      jest.spyOn(inquiryThreadRepo, 'findOne').mockResolvedValueOnce({ id: 'id', draftId: {} });
      mockTaskService.generateTaskNo.mockResolvedValueOnce('string');
      mockTeamService.getQualityTeam.mockResolvedValueOnce({ id: 'id' });
      mockDataSource.transaction.mockImplementationOnce(async (callback) => {
        return await callback(mockEntityManager);
      });
      const result = await terryService.selectPatrolMessageTemplate({
        adminId: 'string',
        storeId: 'string',
        storeName: 'string',
        templateId: 'string',
        customerName: 'string',
        reservationCode: 'string',
        serviceName: 'string',
      } as SelectMessageTemplateDto);
      expect(result).toBeTruthy();
    });

    it('success with exist thread, not has draft', async () => {
      mockMessageTemplateService.getById.mockResolvedValueOnce({ content: 'content' });
      jest.spyOn(inquiryThreadRepo, 'findOne').mockResolvedValueOnce({ id: 'id', draftId: null });
      mockTaskService.generateTaskNo.mockResolvedValueOnce('string');
      mockTeamService.getQualityTeam.mockResolvedValueOnce({ id: 'id' });
      mockDataSource.transaction.mockImplementationOnce(async (callback) => {
        return await callback(mockEntityManager);
      });
      const result = await terryService.selectPatrolMessageTemplate({
        adminId: 'string',
        storeId: 'string',
        storeName: 'string',
        templateId: 'string',
        customerName: 'string',
        reservationCode: 'string',
        serviceName: 'string',
      } as SelectMessageTemplateDto);
      expect(result).toBeTruthy();
    });

    it('success create new thread', async () => {
      mockMessageTemplateService.getById.mockResolvedValueOnce({});
      jest.spyOn(inquiryThreadRepo, 'findOne').mockResolvedValueOnce(null);
      mockTaskService.generateTaskNo.mockResolvedValueOnce('string');
      mockTeamService.getQualityTeam.mockResolvedValueOnce(null);
      mockDataSource.transaction.mockImplementationOnce(async (callback) => {
        return await callback(mockEntityManager);
      });
      const result = await terryService.selectPatrolMessageTemplate({
        adminId: 'string',
        storeId: 'string',
        storeName: 'string',
        templateId: 'string',
        customerName: 'string',
        reservationCode: '',
        serviceName: 'string',
      } as SelectMessageTemplateDto);
      expect(result).toBeTruthy();
    });

    it('fail by exception', async () => {
      mockMessageTemplateService.getById.mockResolvedValueOnce({});
      jest.spyOn(inquiryThreadRepo, 'findOne').mockResolvedValueOnce({ id: 'id', draftId: null });
      mockTaskService.generateTaskNo.mockResolvedValueOnce('string');
      mockDataSource.transaction.mockRejectedValueOnce('error');
      const result = await terryService.selectPatrolMessageTemplate({
        adminId: 'string',
        storeId: 'string',
        storeName: 'string',
        templateId: 'string',
        customerName: 'string',
        reservationCode: 'string',
        serviceName: 'string',
      } as SelectMessageTemplateDto);
      expect(result).toBeTruthy();
    });
  });
});
