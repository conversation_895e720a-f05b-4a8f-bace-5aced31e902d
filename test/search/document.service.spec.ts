import { Test, TestingModule } from '@nestjs/testing';
import { DocumentService } from '@modules/shared/search/services/document.service';
import { CuramaOpenSearchClient } from '@vietnam/curama-opensearch';
import { Logger } from '@nestjs/common';
import { InquiryThreadOpenSearchQuery } from '@common/interface/open-search.interface';
import { CURAMA_SEARCH_INDEX_NAME } from '@src/common/constants/search';

const mockOpenSearchClient = {
  indices: {
    exists: jest.fn().mockResolvedValue({ body: false }),
    create: jest.fn().mockResolvedValue({ body: {} }),
  },
  search: jest.fn().mockResolvedValue({ body: { hits: { hits: [] } } }),
  deleteByQuery: jest.fn().mockResolvedValue({ body: { errors: false } }),
  bulk: jest.fn().mockResolvedValue({ body: { errors: false } }),
  count: jest.fn(),
  updateByQuery: jest.fn(),
  index: jest.fn(),
} as unknown as CuramaOpenSearchClient;

const mockLogger = {
  log: jest.fn(),
  warn: jest.fn(),
};

describe('DocumentService', () => {
  let service: DocumentService;
  let openSearchClient: CuramaOpenSearchClient;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentService,
        {
          provide: CuramaOpenSearchClient,
          useValue: mockOpenSearchClient,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<DocumentService>(DocumentService);
    openSearchClient = module.get<CuramaOpenSearchClient>(CuramaOpenSearchClient);

    jest.spyOn(Logger.prototype, 'warn').mockImplementation(jest.fn());
    jest.spyOn(Logger.prototype, 'log').mockImplementation(jest.fn());
  });

  describe('onModuleInit', () => {
    it('should create index if it does not exist', async () => {
      await service.onModuleInit();

      expect(openSearchClient.indices.exists).toHaveBeenCalledWith({ index: CURAMA_SEARCH_INDEX_NAME });
      expect(openSearchClient.indices.create).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: expect.any(Object),
      });
    });

    it('should log an error if index creation fails', async () => {
      jest.spyOn(openSearchClient.indices, 'create').mockImplementation(
        () =>
          Promise.resolve({
            body: {},
            statusCode: 200,
            headers: {},
            warnings: null,
            meta: {},
          }) as any,
      );

      await service.onModuleInit();

      expect(openSearchClient.indices.create).toHaveBeenCalled();
    });
  });

  describe('createIndex', () => {
    it('should create index successfully', async () => {
      await service.createIndex(CURAMA_SEARCH_INDEX_NAME);

      expect(openSearchClient.indices.create).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: expect.any(Object),
      });
    });

    it('should throw an error if index creation fails', async () => {
      jest.spyOn(openSearchClient.indices, 'create').mockImplementation(() => {
        throw new Error('Index creation error');
      });

      await expect(service.createIndex(CURAMA_SEARCH_INDEX_NAME)).rejects.toThrow('Index creation error');

      expect(openSearchClient.indices.create).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: expect.any(Object),
      });
    });
  });

  describe('queryIndex', () => {
    it('should return query result if index exists', async () => {
      const mockExists = jest.fn().mockResolvedValue({ body: true });
      jest.spyOn(openSearchClient.indices, 'exists').mockImplementation(mockExists);

      const mockSearch = jest.fn().mockResolvedValue({ body: { hits: { hits: [] } } });
      jest.spyOn(openSearchClient, 'search').mockImplementation(mockSearch);

      const result = await service.queryIndex(CURAMA_SEARCH_INDEX_NAME, {});

      expect(result).toEqual({ hits: { hits: [] } });
      expect(openSearchClient.indices.exists).toHaveBeenCalledWith({ index: CURAMA_SEARCH_INDEX_NAME });
      expect(openSearchClient.search).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: {},
      });
    });

    it('should return false if index does not exist', async () => {
      const mockExists = jest.fn().mockResolvedValue({ body: false });
      jest.spyOn(openSearchClient.indices, 'exists').mockImplementation(mockExists);

      const result = await service.queryIndex(CURAMA_SEARCH_INDEX_NAME, {});

      expect(result).toBe(false);
      expect(openSearchClient.indices.exists).toHaveBeenCalledWith({ index: CURAMA_SEARCH_INDEX_NAME });
    });

    it('should return false if search throws an error', async () => {
      jest.spyOn(openSearchClient, 'search').mockImplementation(() => {
        throw new Error('Search error');
      });

      jest.spyOn(service, 'existsIndex').mockResolvedValue(true);
      const result = await service.queryIndex(CURAMA_SEARCH_INDEX_NAME, {});

      expect(result).toBe(false);
      expect(Logger.prototype.log).toHaveBeenCalledWith(expect.stringContaining('Search error'));
    });
  });

  describe('deleteDocuments', () => {
    it('should delete documents successfully', async () => {
      const result = await service.deleteDocuments({ thread_id: ['123'] });

      expect(result).toBe(true);
      expect(openSearchClient.deleteByQuery).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: { query: { terms: { thread_id: ['123'] } } },
      });
    });

    it('should return false if deletion fails', async () => {
      jest
        .spyOn(openSearchClient, 'deleteByQuery')
        .mockImplementation(jest.fn().mockResolvedValue({ body: { errors: true } }));

      const result = await service.deleteDocuments({ thread_id: ['123'] });

      expect(result).toBe(false);
      expect(openSearchClient.deleteByQuery).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: { query: { terms: { thread_id: ['123'] } } },
      });
    });

    it('should return false if deleteByQuery throws an error', async () => {
      jest.spyOn(openSearchClient, 'deleteByQuery').mockImplementation(() => {
        throw new Error('Delete error');
      });

      const logSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation(jest.fn());
      const result = await service.deleteDocuments({ thread_id: ['123'] });

      expect(result).toBe(false);
      expect(logSpy).toHaveBeenCalledWith('Error deleting documents from OpenSearch by threadIds:', expect.any(Error));
    });
  });

  describe('deleteDocumentByField', () => {
    it('should delete the document successfully', async () => {
      const mockDelByQuery = jest.fn().mockResolvedValue({ body: { errors: false } });
      jest.spyOn(openSearchClient, 'deleteByQuery').mockImplementation(mockDelByQuery);

      const result = await service.deleteDocumentByField('thread_id', '123');

      expect(result).toBe(true);
      expect(openSearchClient.deleteByQuery).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: {
          query: {
            term: {
              thread_id: '123',
            },
          },
        },
      });
      expect(Logger.prototype.log).toHaveBeenCalledWith(`Successfully deleted document with thread_id: 123`);
    });

    it('should return false if deleteByQuery has errors', async () => {
      const mockDelByQuery = jest.fn().mockResolvedValue({
        body: { errors: true, failures: [{ message: 'Some error' }] },
      });
      jest.spyOn(openSearchClient, 'deleteByQuery').mockImplementation(mockDelByQuery);

      const result = await service.deleteDocumentByField('thread_id', '123');

      expect(result).toBe(false);
      expect(openSearchClient.deleteByQuery).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: {
          query: {
            terms: {
              thread_id: ['123'],
            },
          },
        },
      });
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        `Error deleting document with thread_id: 123`,
        JSON.stringify([{ message: 'Some error' }]),
      );
    });

    it('should return false and log error if deleteByQuery throws an error', async () => {
      jest.spyOn(openSearchClient, 'deleteByQuery').mockImplementation(() => {
        throw new Error('Delete error');
      });

      const result = await service.deleteDocumentByField('thread_id', '123');

      expect(result).toBe(false);
      expect(openSearchClient.deleteByQuery).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: {
          query: {
            term: {
              thread_id: '123',
            },
          },
        },
      });
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        `Error deleting document with thread_id: 123`,
        expect.any(Error),
      );
    });
  });

  describe('createDocuments', () => {
    it('should create documents in bulk successfully', async () => {
      const documents: InquiryThreadOpenSearchQuery[] = [{ thread_id: '123' } as InquiryThreadOpenSearchQuery];

      const result = await service.createDocuments(documents);

      expect(result).toBe(true);
      expect(openSearchClient.bulk).toHaveBeenCalledWith({
        body: expect.arrayContaining([{ index: { _index: CURAMA_SEARCH_INDEX_NAME } }, { thread_id: '123' }]),
      });
    });

    it('should return true if no documents are provided', async () => {
      const result = await service.createDocuments([]);
      expect(result).toBe(true);
    });

    it('should return false if bulk operation fails', async () => {
      jest.spyOn(openSearchClient, 'bulk').mockImplementation(jest.fn().mockResolvedValue({ body: { errors: true } }));
      const documents: InquiryThreadOpenSearchQuery[] = [{ thread_id: '123' } as InquiryThreadOpenSearchQuery];

      const result = await service.createDocuments(documents);

      expect(result).toBe(false);
    });
  });

  describe('createDocument', () => {
    it('should create the document successfully', async () => {
      const mockIndex = jest.fn().mockResolvedValue({ body: { result: 'created' } });
      jest.spyOn(openSearchClient, 'index').mockImplementation(mockIndex);

      const document = { thread_id: '123' } as InquiryThreadOpenSearchQuery;

      const result = await service.createDocument(document);

      expect(result).toBe(true);
      expect(openSearchClient.index).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: document,
      });
    });

    it('should return false if document creation fails', async () => {
      const mockIndex = jest.fn().mockResolvedValue({ body: { result: 'error' } });
      jest.spyOn(openSearchClient, 'index').mockImplementation(mockIndex);

      const document = { thread_id: '123' } as InquiryThreadOpenSearchQuery;

      const result = await service.createDocument(document);

      expect(result).toBe(false);
      expect(openSearchClient.index).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: document,
      });
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        'Failed to create document:',
        JSON.stringify({ result: 'error' }),
      );
    });

    it('should return false and log error if index throws an error', async () => {
      jest.spyOn(openSearchClient, 'index').mockImplementation(() => {
        throw new Error('Indexing error');
      });

      const document = { thread_id: '123' } as InquiryThreadOpenSearchQuery;

      const result = await service.createDocument(document);

      expect(result).toBe(false);
      expect(openSearchClient.index).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: document,
      });
      expect(Logger.prototype.log).toHaveBeenCalledWith('Error creating document:', expect.any(Error));
    });
  });

  describe('countDocuments', () => {
    it('should return the document count if index exists', async () => {
      jest.spyOn(service, 'existsIndex').mockResolvedValue(true);

      const mockCount = jest.fn().mockResolvedValue({ body: { count: 10 } });
      jest.spyOn(openSearchClient, 'count').mockImplementation(mockCount);

      const result = await service.countDocuments(CURAMA_SEARCH_INDEX_NAME);

      expect(result).toBe(10);
      expect(service.existsIndex).toHaveBeenCalledWith(CURAMA_SEARCH_INDEX_NAME);
      expect(openSearchClient.count).toHaveBeenCalledWith({ index: CURAMA_SEARCH_INDEX_NAME });
      expect(Logger.prototype.log).toHaveBeenCalledWith(`Document count in index ${CURAMA_SEARCH_INDEX_NAME}: 10`);
    });

    it('should return 0 if index does not exist', async () => {
      jest.spyOn(service, 'existsIndex').mockResolvedValue(false);

      const result = await service.countDocuments(CURAMA_SEARCH_INDEX_NAME);

      expect(result).toBe(0);
      expect(service.existsIndex).toHaveBeenCalledWith(CURAMA_SEARCH_INDEX_NAME);
    });

    it('should return 0 if count operation throws an error', async () => {
      jest.spyOn(service, 'existsIndex').mockResolvedValue(true);
      jest.spyOn(openSearchClient, 'count').mockImplementation(() => {
        throw new Error('Count error');
      });

      const result = await service.countDocuments(CURAMA_SEARCH_INDEX_NAME);

      expect(result).toBe(0);
      expect(service.existsIndex).toHaveBeenCalledWith(CURAMA_SEARCH_INDEX_NAME);
      expect(openSearchClient.count).toHaveBeenCalledWith({ index: CURAMA_SEARCH_INDEX_NAME });
    });
  });

  describe('existsIndex', () => {
    it('should return false if index exists', async () => {
      const result = await service.existsIndex(CURAMA_SEARCH_INDEX_NAME);

      expect(result).toBe(false);
      expect(openSearchClient.indices.exists).toHaveBeenCalledWith({ index: CURAMA_SEARCH_INDEX_NAME });
    });

    it('should return false if index does not exist', async () => {
      const mockExists = jest.fn().mockResolvedValue({ body: false });
      jest.spyOn(openSearchClient.indices, 'exists').mockImplementation(mockExists);

      const result = await service.existsIndex(CURAMA_SEARCH_INDEX_NAME);

      expect(result).toBe(false);
      expect(openSearchClient.indices.exists).toHaveBeenCalledWith({ index: CURAMA_SEARCH_INDEX_NAME });
    });
  });

  describe('updateMultipleDocuments', () => {
    it('should return true if no documents are provided', async () => {
      const result = await service.updateMultipleDocuments('thread_id', []);

      expect(result).toBe(true);
      expect(Logger.prototype.log).toHaveBeenCalledWith('No documents provided for upsert.');
    });

    it('should update documents successfully', async () => {
      const field = 'thread_id';
      const documents: InquiryThreadOpenSearchQuery[] = [
        { thread_id: '123' } as InquiryThreadOpenSearchQuery,
        { thread_id: '456' } as InquiryThreadOpenSearchQuery,
      ];

      const existingDocs = [{ _id: '1', thread_id: '123' }];

      jest.spyOn(service, 'getExistingDocuments').mockResolvedValue(existingDocs);

      jest.spyOn(service, 'bulkUpdateDocuments').mockResolvedValue(true);

      const result = await service.updateMultipleDocuments(field, documents);

      expect(result).toBe(true);
      expect(service.getExistingDocuments).toHaveBeenCalledWith(field, documents);
      expect(service.bulkUpdateDocuments).toHaveBeenCalledWith([{ _id: '1', doc: { thread_id: '123' } }]);
    });

    it('should return false if bulk update fails', async () => {
      const field = 'thread_id';
      const documents: InquiryThreadOpenSearchQuery[] = [{ thread_id: '123' } as InquiryThreadOpenSearchQuery];

      const existingDocs = [{ _id: '1', thread_id: '123' }];

      jest.spyOn(service, 'getExistingDocuments').mockResolvedValue(existingDocs);

      jest.spyOn(service, 'bulkUpdateDocuments').mockResolvedValue(false);

      const result = await service.updateMultipleDocuments(field, documents);

      expect(result).toBe(false);
      expect(service.getExistingDocuments).toHaveBeenCalledWith(field, documents);
      expect(service.bulkUpdateDocuments).toHaveBeenCalledWith([{ _id: '1', doc: { thread_id: '123' } }]);
    });

    it('should return false if getExistingDocuments throws an error', async () => {
      const field = 'thread_id';
      const documents: InquiryThreadOpenSearchQuery[] = [{ thread_id: '123' } as InquiryThreadOpenSearchQuery];

      jest.spyOn(service, 'getExistingDocuments').mockRejectedValue(new Error('Get documents error'));

      const result = await service.updateMultipleDocuments(field, documents);

      expect(result).toBe(false);
      expect(service.getExistingDocuments).toHaveBeenCalledWith(field, documents);
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        'Error in upsert operation for multiple documents:',
        expect.any(Error),
      );
    });
  });

  describe('getExistingDocuments', () => {
    it('should return existing documents mapped with their _id and source fields', async () => {
      const field = 'thread_id';
      const documents: InquiryThreadOpenSearchQuery[] = [
        { thread_id: '123' } as InquiryThreadOpenSearchQuery,
        { thread_id: '456' } as InquiryThreadOpenSearchQuery,
      ];

      const mockResponse = {
        hits: {
          total: { value: 2 },
          hits: [
            { _id: '1', _source: { thread_id: '123' } },
            { _id: '2', _source: { thread_id: '456' } },
          ],
        },
      };

      jest.spyOn(service, 'queryIndex').mockResolvedValue(mockResponse as any);

      const result = await service.getExistingDocuments(field, documents);

      expect(result).toEqual([
        { _id: '1', thread_id: '123' },
        { _id: '2', thread_id: '456' },
      ]);
      expect(service.queryIndex).toHaveBeenCalledWith(CURAMA_SEARCH_INDEX_NAME, {
        query: { terms: { [field]: ['123', '456'] } },
        _source: [field],
        size: 10000,
      });
    });

    it('should return an empty array if no documents are found', async () => {
      const field = 'thread_id';
      const documents: InquiryThreadOpenSearchQuery[] = [{ thread_id: '123' } as InquiryThreadOpenSearchQuery];

      const mockResponse = {
        hits: {
          total: { value: 0 },
          hits: [],
        },
      };

      jest.spyOn(service, 'queryIndex').mockResolvedValue(mockResponse as any);

      const result = await service.getExistingDocuments(field, documents);

      expect(result).toEqual([]);
      expect(Logger.prototype.log).toHaveBeenCalledWith('No documents found to update. Proceeding with inserts only.');
    });

    it('should return an empty array if queryIndex returns null', async () => {
      const field = 'thread_id';
      const documents: InquiryThreadOpenSearchQuery[] = [{ thread_id: '123' } as InquiryThreadOpenSearchQuery];

      jest.spyOn(service, 'queryIndex').mockResolvedValue(null);

      const result = await service.getExistingDocuments(field, documents);

      expect(result).toEqual([]);
      expect(Logger.prototype.log).toHaveBeenCalledWith('No documents found to update. Proceeding with inserts only.');
    });

    it('should throw an error if queryIndex throws an error', async () => {
      const field = 'thread_id';
      const documents: InquiryThreadOpenSearchQuery[] = [{ thread_id: '123' } as InquiryThreadOpenSearchQuery];

      jest.spyOn(service, 'queryIndex').mockRejectedValue(new Error('Query error'));

      await expect(service.getExistingDocuments(field, documents)).rejects.toThrow('Query error');
    });
  });

  describe('bulkUpdateDocuments', () => {
    it('should return true if no documents are provided', async () => {
      const result = await service.bulkUpdateDocuments([]);
      expect(result).toBe(true);
    });

    it('should update documents successfully and log the operation', async () => {
      const documentsToUpdate = [
        { _id: '1', doc: { thread_id: '123' } as InquiryThreadOpenSearchQuery },
        { _id: '2', doc: { thread_id: '456' } as InquiryThreadOpenSearchQuery },
      ];

      const mockBulk = jest.fn().mockResolvedValue({ body: { errors: false } });
      jest.spyOn(openSearchClient, 'bulk').mockImplementation(mockBulk);

      const result = await service.bulkUpdateDocuments(documentsToUpdate);

      const expectedBody = [
        { update: { _index: CURAMA_SEARCH_INDEX_NAME, _id: '1' } },
        { doc: { thread_id: '123' } },
        { update: { _index: CURAMA_SEARCH_INDEX_NAME, _id: '2' } },
        { doc: { thread_id: '456' } },
      ];

      expect(result).toBe(true);
      expect(openSearchClient.bulk).toHaveBeenCalledWith({ body: expectedBody });
      expect(Logger.prototype.log).toHaveBeenCalledWith('Updated 2 documents in OpenSearch.');
    });

    it('should return false and log errors if bulk operation fails', async () => {
      const documentsToUpdate = [{ _id: '1', doc: { thread_id: '123' } as InquiryThreadOpenSearchQuery }];
      const mockFailedItem = {
        update: { _id: '1', error: { type: 'version_conflict', reason: 'Document update conflict' } },
      };

      const mockBulk = jest.fn().mockResolvedValue({ body: { errors: true, items: [mockFailedItem] } });
      jest.spyOn(openSearchClient, 'bulk').mockImplementation(mockBulk);

      const result = await service.bulkUpdateDocuments(documentsToUpdate);

      const expectedBody = [{ update: { _index: CURAMA_SEARCH_INDEX_NAME, _id: '1' } }, { doc: { thread_id: '123' } }];

      expect(result).toBe(false);
      expect(openSearchClient.bulk).toHaveBeenCalledWith({ body: expectedBody });
      expect(Logger.prototype.log).toHaveBeenCalledWith('Error updating documents:', JSON.stringify([mockFailedItem]));
    });
  });

  describe('updateDocumentsByCondition', () => {
    it('should update documents by condition successfully', async () => {
      const condition = { thread_id: '123' };
      const updates = { admin_id: 'new_admin' };
      const mockUpdateByQuery = jest.fn().mockResolvedValue({ body: { errors: false } });
      jest.spyOn(openSearchClient, 'updateByQuery').mockImplementation(mockUpdateByQuery);

      const result = await service.updateDocumentsByCondition(condition, updates);

      expect(result).toBe(true);
      expect(openSearchClient.updateByQuery).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: {
          query: {
            bool: {
              must: [{ term: { thread_id: '123' } }],
            },
          },
          script: {
            source: 'ctx._source.admin_id = params.admin_id;',
            params: updates,
          },
        },
      });
    });

    it('should return false if update fails', async () => {
      jest
        .spyOn(openSearchClient, 'updateByQuery')
        .mockImplementation(jest.fn().mockResolvedValue({ body: { errors: true } }));
      const condition = { thread_id: '123' };
      const updates = { admin_id: 'new_admin' };

      const result = await service.updateDocumentsByCondition(condition, updates);

      expect(result).toBe(false);
    });
  });
});
