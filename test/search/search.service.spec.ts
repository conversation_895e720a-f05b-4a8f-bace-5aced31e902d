import { Test, TestingModule } from '@nestjs/testing';
import { CuramaOpenSearchClient } from '@vietnam/curama-opensearch';
import { CURAMA_SEARCH_INDEX_NAME } from '@common/constants/search';
import { Logger } from '@nestjs/common';
import { SearchService } from '@modules/shared/search/services/search.service';
import { SearchServiceDto } from '@modules/shared/search/dtos/search-service.dto';

describe('SearchService', () => {
  let service: SearchService;
  let openSearchClient: CuramaOpenSearchClient;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SearchService,
        {
          provide: CuramaOpenSearchClient,
          useValue: {
            search: jest.fn(),
          },
        },
        {
          provide: Logger,
          useValue: {
            error: jest.fn(),
          },
        },
      ],
    }).compile();
    service = module.get<SearchService>(SearchService);
    openSearchClient = module.get<CuramaOpenSearchClient>(CuramaOpenSearchClient);
  });

  describe('getThreadsByConditions', () => {
    it('should return search results if OpenSearch responds successfully', async () => {
      const mockResponse = { body: { hits: { hits: [] } } } as unknown as any;
      const searchQueryDto: SearchServiceDto = {
        page: 1,
        perPage: 10,
        sort: undefined,
      };
      jest.spyOn(service, 'buildQueryConditions').mockResolvedValue({});
      (openSearchClient.search as jest.Mock).mockResolvedValue(mockResponse);

      const result = await service.getThreadsByConditions(searchQueryDto);

      expect(service.buildQueryConditions).toHaveBeenCalledWith(searchQueryDto);
      expect(openSearchClient.search).toHaveBeenCalledWith({
        index: CURAMA_SEARCH_INDEX_NAME,
        body: expect.any(Object),
      });
      expect(result).toEqual(mockResponse.body);
    });

    it('should return null and log error if OpenSearch throws an error', async () => {
      const searchQueryDto: SearchServiceDto = {
        page: 1,
        perPage: 10,
        sort: undefined,
      };

      jest.spyOn(service, 'buildQueryConditions').mockResolvedValue({});
      (openSearchClient.search as jest.Mock).mockRejectedValue(new Error('OpenSearch error'));
      const result = await service.getThreadsByConditions(searchQueryDto);

      expect(result).toBeNull();
    });
  });

  describe('buildQueryConditions', () => {
    it('should add storeId to the query if provided', async () => {
      const searchQueryDto = { storeId: 'store1', page: 1, perPage: 10, sort: { thread_updated_at: 'asc' } };
      const query = await service.buildQueryConditions(searchQueryDto as unknown as SearchServiceDto);

      const mustClause = Array.isArray(query.query.bool.must) ? query.query.bool.must : [query.query.bool.must];

      expect(mustClause).toEqual(
        expect.arrayContaining([expect.objectContaining({ match_phrase: { store_id: 'store1' } })]),
      );
    });

    it('should add adminId to the query if provided', async () => {
      const searchQueryDto = { adminId: 'admin1', page: 1, perPage: 10, sort: { thread_updated_at: 'asc' } };
      const query = await service.buildQueryConditions(searchQueryDto as unknown as SearchServiceDto);

      const mustClause = Array.isArray(query.query.bool.must) ? query.query.bool.must : [query.query.bool.must];

      expect(mustClause).toEqual(expect.arrayContaining([expect.objectContaining({ term: { admin_id: 'admin1' } })]));
    });

    it('should add teamId to the query if provided', async () => {
      const searchQueryDto = { teamId: 'team1', page: 1, perPage: 10, sort: { thread_updated_at: 'asc' } };
      const query = await service.buildQueryConditions(searchQueryDto as unknown as SearchServiceDto);

      const mustClause = Array.isArray(query.query.bool.must) ? query.query.bool.must : [query.query.bool.must];

      expect(mustClause).toEqual(expect.arrayContaining([expect.objectContaining({ term: { team_id: 'team1' } })]));
    });

    it('should add updatedAt to the query if provided', async () => {
      const searchQueryDto = { updatedAt: '2023-10-01', page: 1, perPage: 10, sort: { thread_updated_at: 'asc' } };
      const query = await service.buildQueryConditions(searchQueryDto as unknown as SearchServiceDto);

      const mustClause = Array.isArray(query.query.bool.must) ? query.query.bool.must : [query.query.bool.must];

      expect(mustClause).toEqual(
        expect.arrayContaining([expect.objectContaining({ term: { thread_updated_at: '2023-10-01' } })]),
      );
    });

    it('should add content search as wildcard query if content array is provided', async () => {
      const searchQueryDto = { content: ['test', 'message'], page: 1, perPage: 10, sort: { thread_updated_at: 'asc' } };
      const query = await service.buildQueryConditions(searchQueryDto as unknown as SearchServiceDto);

      const mustClause = Array.isArray(query.query.bool.must) ? query.query.bool.must : [query.query.bool.must];

      expect(mustClause).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            bool: {
              minimum_should_match: 1,
              should: [
                expect.objectContaining({
                  match: { message: { query: 'test message', operator: 'and' } },
                }),
                expect.objectContaining({
                  bool: {
                    must: [
                      expect.objectContaining({ wildcard: { 'message.keyword': '*test*' } }),
                      expect.objectContaining({ wildcard: { 'message.keyword': '*message*' } }),
                    ],
                  },
                }),
              ],
            },
          }),
        ]),
      );
    });

    it('should log a warning if total documents exceed maxLimit in buildQueryConditions', async () => {
      const searchQueryDto = { page: 200, perPage: 100, sort: { thread_updated_at: 'asc' } }; // Exceeds maxLimit (10,000)
      const loggerSpy = jest.spyOn(service['logger'], 'warn');

      await service.buildQueryConditions(searchQueryDto as unknown as SearchServiceDto);

      expect(loggerSpy).toHaveBeenCalledWith(
        `Cannot retrieve more than 10000 documents in a single request. Use scroll for larger datasets.`,
      );
    });

    it('should add storeId as termQuery if it is a UUID', async () => {
      const searchQueryDto = { storeId: '123e4567-e89b-12d3-a456-************', page: 1, perPage: 10 };
      const query = await service.buildQueryConditions(searchQueryDto as unknown as SearchServiceDto);

      const mustClause = Array.isArray(query.query.bool.must) ? query.query.bool.must : [query.query.bool.must];

      expect(mustClause).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ term: { 'store_id.keyword': '123e4567-e89b-12d3-a456-************' } }),
        ]),
      );
    });

    it('should return default query if no parameters are provided', async () => {
      const searchQueryDto = { page: 1, perPage: 10 };
      const query = await service.buildQueryConditions(searchQueryDto as unknown as SearchServiceDto);

      expect(query.query.bool.must).toBeUndefined(); // Không có điều kiện
    });

    it('should add only updatedAt to the query if content is missing', async () => {
      const searchQueryDto = { updatedAt: '2023-10-01', page: 1, perPage: 10 };
      const query = await service.buildQueryConditions(searchQueryDto as unknown as SearchServiceDto);

      const mustClause = Array.isArray(query.query.bool.must) ? query.query.bool.must : [query.query.bool.must];

      expect(mustClause).toEqual(
        expect.arrayContaining([expect.objectContaining({ term: { thread_updated_at: '2023-10-01' } })]),
      );
    });

    it('should escape special characters in the keyword', () => {
      const keyword = 'test?*\\';
      const escaped = service.escapeSpecialCharacters(keyword);

      expect(escaped).toBe('test\\?\\*\\\\');
    });
  });
});
