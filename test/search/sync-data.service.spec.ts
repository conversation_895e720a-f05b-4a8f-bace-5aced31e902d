import { SyncDataService } from '@modules/shared/search/services/sync-data.service';
import { DocumentService } from '@modules/shared/search/services/document.service';
import { InquiryThreadEntity, MessageEntity } from '@src/entities';
import { Repository, DataSource } from 'typeorm';
import { Logger } from '@nestjs/common';
import { ThreadType } from '@common/enums/thread-type';

describe('SyncDataService', () => {
  let syncDataService: SyncDataService;
  let inquiryThreadRepository: Repository<InquiryThreadEntity>;
  let messageRepository: Repository<MessageEntity>;
  let dataSource: DataSource;
  let documentService: DocumentService;

  beforeEach(() => {
    inquiryThreadRepository = {
      findBy: jest.fn(),
      createQueryBuilder: jest.fn(),
    } as unknown as Repository<InquiryThreadEntity>;

    messageRepository = {
      findOneBy: jest.fn(),
    } as unknown as Repository<MessageEntity>;

    dataSource = {
      query: jest.fn(),
    } as unknown as DataSource;

    documentService = {
      updateMultipleDocuments: jest.fn(),
      updateDocumentsByCondition: jest.fn(),
      createDocument: jest.fn(),
    } as unknown as DocumentService;

    syncDataService = new SyncDataService(inquiryThreadRepository, messageRepository, dataSource, documentService);
  });

  describe('syncStoreAssignments', () => {
    it('should handle multiple store IDs and call updateMultipleDocuments', async () => {
      const storeIds = ['store-1', 'store-2'];
      const threadsMock = [
        {
          thread_id: '1',
          store_id: 'store-1',
          thread_status_id: 2,
          thread_updated_at: '2023-10-01T09:20:00Z',
        },
        {
          thread_id: '2',
          store_id: 'store-2',
          thread_status_id: 3,
          thread_updated_at: '2023-10-01T09:30:00Z',
        },
      ];

      jest.spyOn(syncDataService, 'buildQueryForQualityThread').mockReturnValue({
        getQuery: jest.fn(() => 'QUERY_FOR_QUALITY_THREAD'),
        andWhere: jest.fn().mockReturnThis(),
      } as any);

      jest.spyOn(syncDataService, 'buildQueryForCuramaThread').mockReturnValue({
        getQuery: jest.fn(() => 'QUERY_FOR_CURAMA_THREAD'),
        andWhere: jest.fn().mockReturnThis(),
      } as any);

      jest.spyOn(syncDataService, 'buildFinalQuery').mockReturnValue('FINAL_QUERY');

      jest.spyOn(dataSource, 'query').mockResolvedValue(threadsMock);

      const result = await syncDataService.syncMultipleStoreAssignments(storeIds);

      expect(dataSource.query).toHaveBeenCalledWith('FINAL_QUERY', [storeIds]);
      expect(documentService.updateMultipleDocuments).toHaveBeenCalledWith('thread_id', threadsMock);
      expect(result).toBe(true);
    });

    it('should handle single store ID and call updateDocumentsByCondition', async () => {
      const storeId = 'store-1';

      const qualityThreadsMock = [
        {
          thread_id: 'thread-1',
          store_id: 'store-1',
          admin_id: 'admin-123',
          team_id: 'team-456',
        },
      ];

      const curamaThreadsMock = [
        {
          thread_id: 'thread-2',
          store_id: 'store-1',
          admin_id: 'admin-123',
          team_id: 'quality-admin-789',
        },
      ];

      jest.spyOn(syncDataService, 'buildQueryForQualityThread').mockReturnValue({
        getQuery: jest.fn(() => 'QUERY_FOR_QUALITY_THREAD'),
        andWhere: jest.fn().mockReturnThis(),
      } as any);

      jest.spyOn(syncDataService, 'buildQueryForCuramaThread').mockReturnValue({
        getQuery: jest.fn(() => 'QUERY_FOR_CURAMA_THREAD'),
        andWhere: jest.fn().mockReturnThis(),
      } as any);

      const querySpy = jest.spyOn(dataSource, 'query');
      querySpy.mockResolvedValueOnce(qualityThreadsMock).mockResolvedValueOnce(curamaThreadsMock);

      const result = await syncDataService.syncStoreAssignments(storeId);

      expect(documentService.updateDocumentsByCondition).toHaveBeenCalledWith(
        { thread_id: 'thread-1' },
        { admin_id: 'admin-123', team_id: 'team-456' },
      );
      expect(documentService.updateDocumentsByCondition).toHaveBeenCalledWith(
        { thread_id: 'thread-2' },
        { admin_id: 'admin-123', team_id: 'quality-admin-789' },
      );
      expect(result).toBe(true);
    });

    it('should log error and return false when an error occurs', async () => {
      const storeId = 'store-1';

      jest.spyOn(inquiryThreadRepository, 'findBy').mockRejectedValue(new Error('Database error'));
      jest.spyOn(Logger.prototype, 'error').mockImplementation();

      const result = await syncDataService.syncStoreAssignments(storeId);

      expect(Logger.prototype.error).toHaveBeenCalledWith('Error in syncStoreAssignments:', expect.any(Object));
      expect(result).toBe(false);
    });

    it('should return true when storeId is an empty array', async () => {
      const storeIds: string[] = [];
      jest.spyOn(dataSource, 'query').mockResolvedValue([]);

      const result = await syncDataService.syncMultipleStoreAssignments(storeIds);

      expect(dataSource.query).not.toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should return false when no threads are found for the given storeId', async () => {
      const storeIds = ['store-1'];
      jest.spyOn(dataSource, 'query').mockResolvedValue([]);

      const result = await syncDataService.syncMultipleStoreAssignments(storeIds);

      expect(documentService.updateMultipleDocuments).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });

    it('should log an error and return false when documentService.updateMultipleDocuments throws an error', async () => {
      const storeIds = ['store-1'];
      jest.spyOn(dataSource, 'query').mockResolvedValue([{ thread_id: 'thread-1' }]);
      jest.spyOn(documentService, 'updateMultipleDocuments').mockRejectedValue(new Error('OpenSearch error'));
      jest.spyOn(Logger.prototype, 'error').mockImplementation();

      const result = await syncDataService.syncMultipleStoreAssignments(storeIds);

      expect(Logger.prototype.error).toHaveBeenCalledWith('Error in syncStoreAssignments:', expect.any(Object));
      expect(result).toBe(false);
    });
  });

  describe('syncMessageCreation', () => {
    it('should create a document for a valid message and thread', async () => {
      const messageId = 'message-1';
      const threadId = 'thread-1';
      const threadType = ThreadType.CuramaThread;
      const threadStatusId = 2;
      const threadUpdatedAt = '2023-10-01T09:20:00Z';

      const threadMock = {
        thread_id: 'thread-1',
        store_id: 'store-1',
        team_id: 'team-1',
        admin_id: 'admin-1',
        thread_status_id: 2,
        thread_updated_at: '2023-10-01T09:20:00Z',
      };

      const messageMock = {
        id: 'message-1',
        content: 'Message content',
      } as unknown as MessageEntity;

      jest.spyOn(syncDataService, 'buildQueryForCuramaThread').mockReturnValue({
        getQuery: jest.fn(() => 'QUERY_FOR_CURAMA_THREAD'),
        andWhere: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
      } as any);

      jest.spyOn(dataSource, 'query').mockResolvedValue([threadMock]);
      jest.spyOn(messageRepository, 'findOneBy').mockResolvedValue(messageMock);

      const result = await syncDataService.syncMessageCreation(
        messageId,
        threadId,
        threadType,
        threadStatusId,
        threadUpdatedAt,
      );

      expect(dataSource.query).toHaveBeenCalledWith('QUERY_FOR_CURAMA_THREAD', [threadId]);
      expect(messageRepository.findOneBy).toHaveBeenCalledWith({ id: messageId });
      expect(documentService.createDocument).toHaveBeenCalledWith(
        expect.objectContaining({
          thread_id: threadMock.thread_id,
          message_id: messageMock.id,
          message: messageMock.content,
        }),
      );
      expect(result).toBe(true);
    });

    it('should return false if no threads are found', async () => {
      const messageId = 'message-1';
      const threadId = 'thread-1';
      const threadType = ThreadType.CuramaThread;
      const threadStatusId = 2;
      const threadUpdatedAt = '2023-10-01T09:20:00Z';

      jest.spyOn(syncDataService, 'buildQueryForCuramaThread').mockReturnValue({
        getQuery: jest.fn(() => 'QUERY_FOR_CURAMA_THREAD'),
        andWhere: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
      } as any);

      jest.spyOn(dataSource, 'query').mockResolvedValue([]);

      const result = await syncDataService.syncMessageCreation(
        messageId,
        threadId,
        threadType,
        threadStatusId,
        threadUpdatedAt,
      );

      expect(result).toBe(false);
    });

    it('should return false if no message is found', async () => {
      const messageId = 'message-1';
      const threadId = 'thread-1';
      const threadType = ThreadType.CuramaThread;
      const threadStatusId = 2;
      const threadUpdatedAt = '2023-10-01T09:20:00Z';

      const threadMock = {
        thread_id: 'thread-1',
        store_id: 'store-1',
        team_id: 'team-1',
        admin_id: 'admin-1',
        thread_status_id: 2,
        thread_updated_at: '2023-10-01T09:20:00Z',
      };

      jest.spyOn(syncDataService, 'buildQueryForCuramaThread').mockReturnValue({
        getQuery: jest.fn(() => 'QUERY_FOR_CURAMA_THREAD'),
        andWhere: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
      } as any);

      jest.spyOn(dataSource, 'query').mockResolvedValue([threadMock]);
      jest.spyOn(messageRepository, 'findOneBy').mockResolvedValue(null);

      const result = await syncDataService.syncMessageCreation(
        messageId,
        threadId,
        threadType,
        threadStatusId,
        threadUpdatedAt,
      );

      expect(Logger.prototype.error).toHaveBeenCalledWith(`No message found for messageId: ${messageId}`);
      expect(result).toBe(false);
    });
  });
});
