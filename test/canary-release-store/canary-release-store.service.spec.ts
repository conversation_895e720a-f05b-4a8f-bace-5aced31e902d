import { Repository } from 'typeorm';
import { CanaryReleaseStoreEntity } from '@src/entities';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { CanaryReleaseStoreService } from '@src/modules/shared/canary-release-store/canary-release-store.service';

describe('CanaryReleaseStoreService', () => {
  let canaryReleaseStoreService: CanaryReleaseStoreService;
  let canaryReleaseStoreRepository: Repository<CanaryReleaseStoreEntity>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CanaryReleaseStoreService,
        {
          provide: getRepositoryToken(CanaryReleaseStoreEntity),
          useClass: Repository,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue({ isEnable: true }),
          },
        },
      ],
    }).compile();

    canaryReleaseStoreService = module.get<CanaryReleaseStoreService>(CanaryReleaseStoreService);
    canaryReleaseStoreRepository = module.get<Repository<CanaryReleaseStoreEntity>>(
      getRepositoryToken(CanaryReleaseStoreEntity),
    );
  });

  describe('checkRelease()', () => {
    it('true', async () => {
      const storeId = 'ef16ea98-5374-4936-9139-4fda025a77d5';
      const canaryRelease = <CanaryReleaseStoreEntity>(<unknown>{
        id: '23b60975-547c-4c1a-a65b-cbf3d0522b99',
        createdAt: '2023-07-07T09:54:40.933Z',
        updatedAt: '2023-07-07T09:54:40.933Z',
        storeId: 'ef16ea98-5374-4936-9139-4fda025a77d5',
      });

      jest.spyOn(canaryReleaseStoreRepository, 'findOneBy').mockResolvedValue(canaryRelease);

      const result = await canaryReleaseStoreService.checkRelease(storeId);

      expect(result).toEqual(true);
      expect(canaryReleaseStoreRepository.findOneBy).toHaveBeenCalledWith({ storeId });
    });
  });
});
