import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CURAMA_AUTHORIZATION_SERVICE, CURAMA_AUTH_SERVICE } from '@curama-auth/constants';
import { AdminEntity, AdminStatus } from '@curama-auth/entities/admin.entity';
import { randomUUID } from 'crypto';
import { ConfigService } from '@nestjs/config';
import { NotFoundException } from '@nestjs/common';
import { AuthorizationController } from '@src/modules/terry/controllers/authorization.controller';
import { Py3Service } from '@vietnam/curama-py3-api';
import { Request, Response } from 'express';
import { REDIRECT_METADATA } from '@nestjs/common/constants';

describe('AuthorizationController', () => {
  let controller: AuthorizationController;

  const mockCuramaAuthorizationService = {
    getAllPoliciesResourcesWithActions: jest.fn(),
    getUserPoliciesResourcesWithActions: jest.fn(),
    updateUserPolicies: jest.fn(),
  };

  let adminRepository;

  const mockCuramaAuthService = {
    getSessionAdmin: jest.fn(),
    getOauth2Url: jest.fn(),
    signOut: jest.fn(),
    authenticate: jest.fn(),
  };
  const configService = {
    get: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthorizationController],
      providers: [
        {
          provide: getRepositoryToken(AdminEntity),
          useClass: Repository,
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: mockCuramaAuthorizationService,
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: mockCuramaAuthService,
        },
        {
          provide: Py3Service,
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: configService,
        },
      ],
    }).compile();

    controller = module.get<AuthorizationController>(AuthorizationController);
    adminRepository = module.get<Repository<AdminEntity>>(getRepositoryToken(AdminEntity));
  });

  describe('index', () => {
    it('view authorization index page', async () => {
      jest.spyOn(adminRepository, 'findBy').mockResolvedValue([]);
      jest.spyOn(mockCuramaAuthorizationService, 'getAllPoliciesResourcesWithActions').mockResolvedValue([]);
      const result = await controller.index();
      expect(result).toBeTruthy();
    });
  });

  describe('getUserPolicies', () => {
    it('should get user policies', async () => {
      const jestMock = jest.fn();
      const id = randomUUID();
      jest.spyOn(adminRepository, 'findOneBy').mockResolvedValue({
        id: jestMock.mockReturnValue(id),
        name: jestMock.mockReturnValue('test'),
        email: jestMock.mockReturnValue('<EMAIL>'),
        sub: jestMock.mockReturnValue(randomUUID()),
        status: jestMock.mockReturnValue(AdminStatus.Active),
      } as unknown as Partial<AdminEntity>);

      jest.spyOn(mockCuramaAuthorizationService, 'getUserPoliciesResourcesWithActions').mockResolvedValue([]);
      const result = await controller.getUserPolicies(id);
      expect(result).toBeTruthy();
    });

    it('should throw exception if admin not found', async () => {
      const id = randomUUID();
      jest.spyOn(adminRepository, 'findOneBy').mockResolvedValue(null);

      jest.spyOn(mockCuramaAuthorizationService, 'getUserPoliciesResourcesWithActions').mockResolvedValue([]);
      try {
        await controller.getUserPolicies(id);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toEqual('申し訳ありませんが、指定されたユーザーは見つかりませんでした。');
      }
    });
  });

  describe('updateUserPolicies', () => {
    it('should updated policies', async () => {
      const jestMock = jest.fn();
      const id = randomUUID();
      jest.spyOn(adminRepository, 'findOneBy').mockResolvedValue({
        id: jestMock.mockReturnValue(id),
        name: jestMock.mockReturnValue('test'),
        email: jestMock.mockReturnValue('<EMAIL>'),
        sub: jestMock.mockReturnValue(randomUUID()),
        status: jestMock.mockReturnValue(AdminStatus.Active),
      } as unknown as Partial<AdminEntity>);

      const result = await controller.updateUserPolicies(id, []);
      expect(result).toBeTruthy();
    });

    it('should throw exception if admin not found', async () => {
      const id = randomUUID();
      jest.spyOn(adminRepository, 'findOneBy').mockResolvedValue(null);

      jest.spyOn(mockCuramaAuthorizationService, 'getUserPoliciesResourcesWithActions').mockResolvedValue([]);
      try {
        await controller.updateUserPolicies(id, []);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toEqual('申し訳ありませんが、指定されたユーザーは見つかりませんでした。');
      }
    });
  });
  describe('showLoginForm', () => {
    it('success', () => {
      const result = controller.showLoginForm();
      expect(result).toBeTruthy();
    });
  });

  describe('handle oauth2 callback', () => {
    it('success', async () => {
      const res = {} as unknown as Response;
      res.redirect = jest.fn();
      res.clearCookie = jest.fn();
      jest.spyOn(configService, 'get').mockReturnValue('/admin/consulting-message/messages');
      await controller.oauth2Callback(jest.fn(), res);
      expect(res.redirect).toBeCalled();
    });
  });

  describe('logout', () => {
    it('logout success', () => {
      mockCuramaAuthService.signOut.mockResolvedValue({});
      controller.logout(jest.fn() as unknown as Request);
      const metadata = Reflect.getMetadata(REDIRECT_METADATA, controller.logout);
      expect(metadata.url).toBe('/admin/consulting-message/login');
    });
  });
});
