import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AttachmentEntity, InquiryThreadEntity, MessageEntity } from '@src/entities';
import { MessageService } from '@src/modules/shared/message/message.service';
import { Repository } from 'typeorm';
import { DocumentService } from '@modules/shared/search/services/document.service';
declare global {
  interface String {
    escape(): string;
    urlize(): string;
    nl2br(): string;
  }
}
String.prototype.escape = jest.fn().mockReturnValue('');
String.prototype.urlize = jest.fn().mockReturnValue('');

String.prototype.nl2br = jest.fn().mockReturnValue('');
const mockInquiryThreadRepo = {
  createQueryBuilder: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    addSelect: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    getSql: jest.fn().mockReturnValue('SQL_QUERY'),
  })),
  query: jest.fn(),
  findOne: jest.fn(),
  findOneBy: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  findAndCount: jest.fn(),
};

describe('MessageService', () => {
  let messageService: MessageService;
  let documentService: DocumentService;
  let messageRepo: Repository<MessageEntity>;
  let inquiryThreadRepo: Repository<InquiryThreadEntity>;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [],
      providers: [
        {
          provide: getRepositoryToken(MessageEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(AttachmentEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(InquiryThreadEntity),
          useValue: mockInquiryThreadRepo,
        },
      ],
    }).compile();
    messageRepo = module.get<Repository<MessageEntity>>(getRepositoryToken(MessageEntity));
    inquiryThreadRepo = module.get<Repository<InquiryThreadEntity>>(getRepositoryToken(InquiryThreadEntity));
    messageService = new MessageService(messageRepo, inquiryThreadRepo, documentService);
  });

  describe('delete', () => {
    it('success', async () => {
      jest.spyOn(messageRepo, 'update').mockResolvedValue({} as any);
      const result = await messageService.delete('id', '');

      expect(result).toBeTruthy();
    });

    it('fail', async () => {
      jest.spyOn(messageRepo, 'update').mockRejectedValue('error');
      const result = await messageService.delete('id', '');
      expect(result).toBeTruthy();
    });
  });

  describe('findByOptions', () => {
    it('success', async () => {
      jest.spyOn(messageRepo, 'find').mockResolvedValue({} as any);
      const result = await messageService.findByOptions({});

      expect(result).toBeTruthy();
    });
  });
  describe('findAndCount', () => {
    it('success', async () => {
      jest.spyOn(messageRepo, 'findAndCount').mockResolvedValue({} as any);
      const result = await messageService.findAndCount({});
      expect(result).toBeTruthy();
    });
  });

  describe('getByInquiryThread', () => {
    it('success', async () => {
      const threadId = 'ef16ea98-5374-4936-9139-4fda025a77d4';
      const perPage = 2;
      const page = 3;
      const total = 7;
      const list = <MessageEntity[]>[
        {
          id: '123',
          content: 'content1',
        },
        {
          id: '345',
          content: 'content2',
        },
        {
          id: '123',
          content: 'content1',
        },
        {
          id: '345',
          content: 'content2',
        },
        {
          id: '123',
          content: "<script>alert('welcome to xss')</script>",
        },
        {
          id: '345',
          content: 'content2',
        },
        {
          id: '345',
          content: "<script>alert('welcome to xss')</script>",
        },
      ];

      jest.spyOn(messageRepo, 'findAndCount').mockResolvedValue([list, total]);
      const result = await messageService.getByInquiryThread(threadId, perPage, page);

      expect(messageRepo.findAndCount).toHaveBeenCalled();
      expect(result).toEqual({
        list: list.reverse(),
        pagination: { pageNumber: page, total, perPage },
      });
    });

    it('success with undefined perPage', async () => {
      const threadId = 'ef16ea98-5374-4936-9139-4fda025a77d4';
      const perPage = undefined;
      const page = 1;
      const total = 2;
      const list = <MessageEntity[]>[
        {
          id: '123',
          content: 'content1',
        },
        {
          id: '345',
          content: 'content2',
        },
      ];

      jest.spyOn(messageRepo, 'findAndCount').mockResolvedValue([list, total]);
      const result = await messageService.getByInquiryThread(threadId, perPage, page);

      expect(messageRepo.findAndCount).toHaveBeenCalled();
      expect(result).toEqual({
        list: list.reverse(),
        pagination: { pageNumber: page, total, perPage: 10 },
      });
    });
  });

  describe('getPagingMessages()', () => {
    it('success', async () => {
      jest.spyOn(messageService, 'findAndCount').mockResolvedValueOnce([[{ deletedAt: new Date() } as any], 1]);
      const result = await messageService.getPagingMessages({} as any);
      expect(result).toBeTruthy();
    });

    it('fail', async () => {
      jest.spyOn(messageService, 'findAndCount').mockRejectedValueOnce('error');
      const result = await messageService.getPagingMessages({} as any);
      expect(result).toBeTruthy();
    });
  });
});
