import { CURAMA_AUTHORIZATION_SERVICE, CURAMA_AUTH_SERVICE } from '@curama-auth/constants';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CanaryReleaseStoreEntity } from '@src/entities';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { MessageController } from '@src/modules/terry/controllers/message.controller';
import { MessageService } from '@src/modules/shared/message/message.service';
import { CanaryReleaseStoreService } from '@src/modules/shared/canary-release-store/canary-release-store.service';

describe('MessageController', () => {
  let controller: MessageController;
  const mockedMessageService = {
    delete: jest.fn(),
    getPagingMessages: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MessageController],
      providers: [
        MessageService,
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: {},
        },
        {
          provide: getRepositoryToken(CanaryReleaseStoreEntity),
          useClass: Repository,
        },
        {
          provide: CanaryReleaseStoreService,
          useClass: CanaryReleaseStoreService,
        },
        {
          provide: ConfigService,
          useValue: {},
        },
      ],
    })
      .overrideProvider(MessageService)
      .useValue(mockedMessageService)
      .compile();
    controller = module.get<MessageController>(MessageController);
  });

  describe('deleteMessage', () => {
    it('success', async () => {
      mockedMessageService.delete.mockResolvedValue({});
      const result = await controller.deleteMessage('id', {} as any);
      expect(result).toBeTruthy();
    });
  });
  describe('getPagingMessages', () => {
    it('success', async () => {
      mockedMessageService.getPagingMessages.mockResolvedValue([]);
      const result = await controller.getPagingMessages({} as any);
      expect(result).toBeTruthy();
    });
  });
});
