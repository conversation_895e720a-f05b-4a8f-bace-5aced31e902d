const shopAccount = {
  store: {
    id: 'df06ea98-5374-4936-9139-4fda025a77d5',
    teamId: 'df06ea98-5374-4936-9139-4fda025a77d5',
    adminId: 'df06ea98-5374-4936-9139-4fda025a77d5',
    name: 'Clear houseキレイエ',
    name_kana: 'クリアハウスキレイエ',
    staff_name: 'スタッフさん',
    store_code: '*********',
    status_id: 3,
    calling_status_id: 2,
    is_phone_user: true,
    avatarPath: 'store/*********/262d1d62-2bbd-488c-a618-cc529bea18e8.jpg',
    attachment: {
      id: '96e8a88d-86a4-4445-baaa-1dbf43f9a8bf',
      title: null,
      path: 'store/*********/262d1d62-2bbd-488c-a618-cc529bea18e8.jpg',
      ins_date: '2022-02-13T11:34:49.721308+00:00',
      is_published: true,
      sort: 999,
      upd_date: '2022-02-13T23:28:39.451365+00:00',
      attachment_type_id: 1,
      attachment_kind_id: 1,
    },
    subscription: {
      enabled: true,
      id: 'da6da987-a5e7-43f1-8aba-fdc35599c423',
    },
  },
  newReservation: { count: 0 },
  quote: { unreadMessageCount: 0, enabled: false },
  unreadMessages: {
    count: 2,
    results: [
      {
        id: 'fda02500-9feb-4573-812e-88b0449251bf',
        store_id: '307cbfe5-ec06-41bf-82cd-39c331fa78ba',
        user_id: 'd0f77e38-2096-4460-b771-a1181cba3ce4',
        reservation_id: null,
        service_id: 'bf5e165f-e130-48ae-9a67-7bd4677c4b13',
        other_service: false,
        admin_id: null,
        upd_date: '2023-02-27T09:36:08.665669+00:00',
        url: '/shop/inbox/detail/SER484314851/d0f77e38-2096-4460-b771-a1181cba3ce4/',
        label: 'お問い合わせ',
      },
      {
        id: 'c03d2a88-3b51-41bf-9506-ebe4a69f0115',
        store_id: '307cbfe5-ec06-41bf-82cd-39c331fa78ba',
        user_id: '9ae25a6e-cb46-4d35-8665-ebeb823ee2b7',
        reservation_id: 'b42fdfa6-a5c6-464b-891a-66afc23bfb28',
        service_id: null,
        other_service: false,
        admin_id: null,
        upd_date: '2023-02-27T05:37:46.442948+00:00',
        url: '/shop/inbox/detail/RES507209054/',
        label: 'くらマユーザーさん',
      },
    ],
  },
  todoList: {
    isShowTodoDropdown: false,
    title: 'はじめにやること',
    remainingTodos: 1,
    todos: [
      {
        type: 'LoginWithMobileApp',
        name: 'アプリでログインしましょう',
        label: null,
        href: '#shopApp',
        isComplete: false,
        sort: 1,
      },
      {
        type: 'StartUsingCalendar',
        name: 'カレンダーの利用を開始しよう',
        label: null,
        href: '/shop/calendar/#/',
        isComplete: false,
        sort: 2,
      },
      {
        type: 'UploadManagerImage',
        name: '店長写真を登録しよう',
        label: null,
        href: '#editImage',
        isComplete: false,
        sort: 3,
      },
      {
        type: 'UploadServiceImage',
        name: 'サービス写真を3枚登録しよう',
        label: null,
        href: '/shop/management/service/',
        isComplete: false,
        sort: 4,
      },
      {
        type: 'AcquireExternalReview',
        name: '応援口コミを投稿してもらおう',
        label: null,
        href: '/shop/externalreview/',
        isComplete: false,
        sort: 5,
      },
    ],
  },
  isCalendarMenuVisible: true,
};

const categoryDetail = {
  serviceType: {
    id: '18ce42a6-8b62-42ca-af6f-bc1f0a0e71e9',
    name: '家事代行・家政婦',
    slug: 'housekeeping',
    sort: 1,
    status_id: 3,
    is_large_category: false,
    css_name: 'housekeeping',
    pattern_id: 3,
    search_duration: 2,
    use_price_filter: false,
    billing_type_id: 101,
    is_order: false,
    has_product: false,
    is_emergency: false,
    parent_id: '3df7cfb8-b400-4862-b0e7-f0f6084d1aee',
    parent: {},
    service_type_detail: {
      id: 'f07db095-3405-4b5f-827d-619a9bec074e',
      request_to_stores: '掃除 / 洗濯 / 女性スタッフ',
      default_request_to_customers:
        '・お客様のご自宅の清掃用具を使用させていただきます。\r\n・ベビーシッターおよび未就学児の対応はいたしておりません。\r\n\r\n＜ご予約に関する注意事項＞\r\n・直前でのご予約には対応できません。最低3日以上の余裕をもって、ご予約ください。\r\n・ご予約の日時が、当社の営業時間内であることをご確認ください。\r\n・予約の日程候補は、なるべく日時をずらしてください。\r\n・予約が集中し、ご希望の日時に伺えない場合がございます。その場合は、[メッセージ]にて改めて日時をお伺いします。',
      about:
        '家事代行・家政婦を料金や相場、口コミで比較し、オンラインで予約することができます。<br>忙しいあなたにも、たまにはちょっと楽したいあなたにも。掃除や洗濯、料理などの家事を、プロの家政婦さんにお任せしましょう！',
      about_detail:
        '専門のスタッフがお忙しいあなたをお手伝い。お掃除やお洗濯、お買い物からお食事の準備など家事全般の代行をします。',
      description: '',
      merit:
        'お仕事などで普段なかなか家事に手が回らない方、子育てや介護など家事の人手が足りない方などに人気です。一人暮らしで家事が苦手な若い方からのご利用も多いです。女性スタッフをご指定できるサービスもあるので、一人暮らしの女性の方でもご安心してご利用いただけます。掃除代行だけでなく料理を作ってくれる業者もあります。いつも頑張っている面倒な家事を忘れ、心もお部屋もリフレッシュしてみませんか？',
      reservation_point:
        '依頼できる最短や最長の作業時間が決められているサービスがあります。ご依頼される作業の規模や量が、時間内で完了するか事前にご確認ください。 1.5時間から依頼可能で、以降2時間、3時間と1時間単位で依頼できます。依頼内容によって必要な時間を店舗に確認することが大切です。店舗ごとに、お掃除可能な箇所など、ご提供可能なサービスの内容に違いがあります。 事前にサービス内容を確認したうえで正式に依頼するのがおすすめです。',
      service_details:
        '＜家事代行例＞\r\n■掃除・・掃除機がけ、風呂掃除、トイレ掃除、キッチン掃除、床拭き、窓拭きなど\r\n■料理・・買い物、料理など ■洗濯・・洗濯、アイロン、裁縫、布団干しなど\r\n■その他・・整理整頓、ゴミ出し、クリーニング（引渡し、引取）、靴磨き、草むしりなど\r\nご希望の家事を代行します。\r\n上記内容に含まれないものでも対応可能なものもございますので、お気軽にお問い合わせください。',
      default_change_and_cancel:
        '予約成立後の変更・キャンセルは、作業の6日前まで承ります。\n期限を過ぎてのキャンセルは、5日前から2日前までは予約金額の25%、前日までは50%、それ以降は100%のキャンセル料が発生することがございますので、ご了承ください。',
      fixed_request_to_customers: null,
    },
    service_type_actions: {
      flowsteps: { is_editable_by_store: true },
      request_to_customers: { is_editable_by_store: true },
    },
    questions: [
      {
        id: 'd8617885-aa45-4302-82bc-69de8c41a4a9',
        question: '追加料金がかかる場合がありますか？',
        answer: '当日の急な追加料金は発生しません。必ず、お客様の状況を事前におしらせください。',
        sort: 1,
      },
      {
        id: '5e21e5ac-5c8c-4ae2-bb98-9f0bf43b3057',
        question: '無料で駐車できる場所がない場合はどうしたらいいですか？',
        answer: '付近の有料パーキングを使用しますが、その場合のパーキング代のご負担をお願いします。',
        sort: 2,
      },
    ],
    flowsteps: [
      {
        id: '0e2276bb-f608-42ec-a842-3eb844108b09',
        title: '予約確認',
        text: '当社から予約確認の連絡をさせて頂きます。',
        sort: 2,
      },
      {
        id: '12efcbb5-5a6b-4748-a577-3e0daf181a6e',
        title: '打ち合わせ',
        text: '作業前に作業に関してお話を聞かせていただきます。',
        sort: 3,
      },
      {
        id: '8d1188bf-1c47-4b37-9763-1e83c87abcc8',
        title: '作業',
        text: '予約日時にお客様のところに伺い、作業いたします。',
        sort: 4,
      },
      {
        id: 'a921dcd9-6156-4eb3-b4c1-9aec8c6c62dd',
        title: 'お支払い',
        text: '予約時に選択された方法で作業料金をお支払いください。お支払い完了後は領収書などを受け取り、金額をご確認ください。',
        sort: 5,
      },
    ],
    features: [
      {
        id: '52b393b5-0119-4801-ad50-4091e7fe3d37',
        name: 'オンラインカード決済可',
        description: 'オンラインカード決済が利用出来る場合',
        sort: 1,
        is_boolean: true,
        is_editable_by_store: false,
      },
      {
        id: '69667039-8ff2-49b5-ba10-08262ab86fc5',
        name: '最低料金保証',
        description: 'このサイトの料金が自社ホームページなどの料金と同じ金額以下の場合',
        sort: 2,
        is_boolean: true,
        is_editable_by_store: true,
      },
      {
        id: '9228ed81-ca75-411d-9845-37c01a59c226',
        name: '追加料金一切なし',
        description: '駐車場代などの追加料金を、お客様に一切請求しない場合',
        sort: 3,
        is_boolean: true,
        is_editable_by_store: true,
      },
      {
        id: 'f1e150a5-7abe-43b5-b792-3fec4c9ac15e',
        name: '作業外注一切なし',
        description: '他社に一切外注せず、必ず自社スタッフが作業できる場合',
        sort: 4,
        is_boolean: true,
        is_editable_by_store: true,
      },
    ],
    components: [
      {
        id: '2e1f9b6e-289e-4831-a647-f2b3ede846da',
        enable: true,
        has_working_time: false,
        is_default: true,
        component_type_id: null,
        units: [],
        sort: 1,
        is_option: false,
        description: '時間',
        quantity: {
          id: 'fdacb0ce-0a7a-4335-8262-27dd8bef59cc',
          denomination: '時間',
          upperlimit: 8,
          lowerlimit: 1.5,
          default_price: 2000,
          min_price: null,
          default_count: 1.5,
          step: 1,
        },
      },
    ],
    licenses: [],
  },
};

const serviceDetail = {
  serviceType: {
    id: '18ce42a6-8b62-42ca-af6f-bc1f0a0e71e9',
    name: '家事代行・家政婦',
    slug: 'housekeeping',
    sort: 1,
    status_id: 3,
    is_large_category: false,
    css_name: 'housekeeping',
    pattern_id: 3,
    search_duration: 2,
    use_price_filter: false,
    billing_type_id: 101,
    is_order: false,
    has_product: false,
    is_emergency: false,
    parent_id: '3df7cfb8-b400-4862-b0e7-f0f6084d1aee',
    parent: {},
    service_type_detail: {
      id: 'f07db095-3405-4b5f-827d-619a9bec074e',
      request_to_stores: '掃除 / 洗濯 / 女性スタッフ',
      default_request_to_customers:
        '・お客様のご自宅の清掃用具を使用させていただきます。\r\n・ベビーシッターおよび未就学児の対応はいたしておりません。\r\n\r\n＜ご予約に関する注意事項＞\r\n・直前でのご予約には対応できません。最低3日以上の余裕をもって、ご予約ください。\r\n・ご予約の日時が、当社の営業時間内であることをご確認ください。\r\n・予約の日程候補は、なるべく日時をずらしてください。\r\n・予約が集中し、ご希望の日時に伺えない場合がございます。その場合は、[メッセージ]にて改めて日時をお伺いします。',
      about:
        '家事代行・家政婦を料金や相場、口コミで比較し、オンラインで予約することができます。<br>忙しいあなたにも、たまにはちょっと楽したいあなたにも。掃除や洗濯、料理などの家事を、プロの家政婦さんにお任せしましょう！',
      about_detail:
        '専門のスタッフがお忙しいあなたをお手伝い。お掃除やお洗濯、お買い物からお食事の準備など家事全般の代行をします。',
      description: '',
      merit:
        'お仕事などで普段なかなか家事に手が回らない方、子育てや介護など家事の人手が足りない方などに人気です。一人暮らしで家事が苦手な若い方からのご利用も多いです。女性スタッフをご指定できるサービスもあるので、一人暮らしの女性の方でもご安心してご利用いただけます。掃除代行だけでなく料理を作ってくれる業者もあります。いつも頑張っている面倒な家事を忘れ、心もお部屋もリフレッシュしてみませんか？',
      reservation_point:
        '依頼できる最短や最長の作業時間が決められているサービスがあります。ご依頼される作業の規模や量が、時間内で完了するか事前にご確認ください。 1.5時間から依頼可能で、以降2時間、3時間と1時間単位で依頼できます。依頼内容によって必要な時間を店舗に確認することが大切です。店舗ごとに、お掃除可能な箇所など、ご提供可能なサービスの内容に違いがあります。 事前にサービス内容を確認したうえで正式に依頼するのがおすすめです。',
      service_details:
        '＜家事代行例＞\r\n■掃除・・掃除機がけ、風呂掃除、トイレ掃除、キッチン掃除、床拭き、窓拭きなど\r\n■料理・・買い物、料理など ■洗濯・・洗濯、アイロン、裁縫、布団干しなど\r\n■その他・・整理整頓、ゴミ出し、クリーニング（引渡し、引取）、靴磨き、草むしりなど\r\nご希望の家事を代行します。\r\n上記内容に含まれないものでも対応可能なものもございますので、お気軽にお問い合わせください。',
      default_change_and_cancel:
        '予約成立後の変更・キャンセルは、作業の6日前まで承ります。\n期限を過ぎてのキャンセルは、5日前から2日前までは予約金額の25%、前日までは50%、それ以降は100%のキャンセル料が発生することがございますので、ご了承ください。',
      fixed_request_to_customers: null,
    },
    service_type_actions: {
      flowsteps: { is_editable_by_store: true },
      request_to_customers: { is_editable_by_store: true },
    },
    features: [
      {
        id: '52b393b5-0119-4801-ad50-4091e7fe3d37',
        name: 'オンラインカード決済可',
        description: 'オンラインカード決済が利用出来る場合',
        sort: 1,
        is_boolean: true,
        is_editable_by_store: false,
      },
      {
        id: '69667039-8ff2-49b5-ba10-08262ab86fc5',
        name: '最低料金保証',
        description: 'このサイトの料金が自社ホームページなどの料金と同じ金額以下の場合',
        sort: 2,
        is_boolean: true,
        is_editable_by_store: true,
      },
      {
        id: '9228ed81-ca75-411d-9845-37c01a59c226',
        name: '追加料金一切なし',
        description: '駐車場代などの追加料金を、お客様に一切請求しない場合',
        sort: 3,
        is_boolean: true,
        is_editable_by_store: true,
      },
      {
        id: 'f1e150a5-7abe-43b5-b792-3fec4c9ac15e',
        name: '作業外注一切なし',
        description: '他社に一切外注せず、必ず自社スタッフが作業できる場合',
        sort: 4,
        is_boolean: true,
        is_editable_by_store: true,
      },
    ],
    components: [
      {
        id: '2e1f9b6e-289e-4831-a647-f2b3ede846da',
        enable: true,
        has_working_time: false,
        is_default: true,
        component_type_id: null,
        units: [],
        sort: 1,
        is_option: false,
        description: '時間',
        quantity: {
          id: 'fdacb0ce-0a7a-4335-8262-27dd8bef59cc',
          denomination: '時間',
          upperlimit: 8,
          lowerlimit: 1.5,
          default_price: 2000,
          min_price: null,
          default_count: 1.5,
          step: 1,
        },
      },
    ],
    licenses: [],
  },
  service: {
    id: '2c1f738e-88ca-4d33-823a-66d91c563636',
    store_id: '307cbfe5-ec06-41bf-82cd-39c331fa78ba',
    service_name: '【迅速・丁寧に対応します】100%自社対応♪損害保険加入済みで安心◎',
    service_code: 'SER484314851',
    business_type_id: 1,
    service_type_id: '18ce42a6-8b62-42ca-af6f-bc1f0a0e71e9',
    status_id: 1,
    base_price: 5250,
    total_price: 5250,
    headcount_id: null,
    is_reservable: true,
    is_publishable: true,
    service_store_catalogue: {
      store_status_id: '3',
      store_code: '495422678',
      store_name: '整理収納お片付けマイスターMOGI',
    },
    catch_copy:
      '花と雑貨のお店開業、病院勤務経験有り\r\n\r\n整理収納アドバイザー1級保有！\r\n【整理収納や片付け代行、断捨離】に対応しています',
    sales_point:
      'ご覧いただきありがとうございます。\r\n\r\n★一人暮らしの女性も安心してお任せください\r\n★これまでに培った経験と実績でお客様のご期待に必ずお応えします！\r\n★営業時間外・対応地域外でもご要望お聞きします！\r\n\r\nまずはお気軽にご相談ください。',
    change_and_cancel:
      '予約成立後の変更・キャンセルは、作業の6日前まで承ります。\r\n期限を過ぎてのキャンセルは、5日前から2日前までは予約金額の25%、前日までは50%、それ以降は100%のキャンセル料が発生することがございますので、ご了承ください。',
    request_to_customers:
      '・お客様のご自宅の清掃用具を使用させていただきます。\r\n・ベビーシッターおよび未就学児の対応はいたしておりません。\r\n\r\n＜ご予約に関する注意事項＞\r\n・直前でのご予約には対応できません。最低3日以上の余裕をもって、ご予約ください。\r\n・ご予約の日時が、当社の営業時間内であることをご確認ください。\r\n・予約の日程候補は、なるべく日時をずらしてください。\r\n・予約が集中し、ご希望の日時に伺えない場合がございます。その場合は、[メッセージ]にて改めて日時をお伺いします。',
    service_details:
      '（店長のご挨拶）\r\n初めまして！整理収納お片付けマイスターMOGIの茂木です。\r\n宜しくお願い致します。\r\nこの度は数ある整理収納・片付け代行の業者の中からこのページをご覧いただきまして誠にありがとうございます。\r\nお花と雑貨のお店開業・病院勤務等の経験を活かし、整理収納アドバイザー1級保有の私が、お客様の手に負えないお片付けをお手伝いいたします。\r\n\r\n（整理収納お片付けマイスターMOGIの思い）\r\n「あれ！お部屋がキレイになったんじゃない？」って言われてみたくはありませんか？かつてホームページのキャッチコピーに使ったものです。これはお客様がお部屋を片付け綺麗になさり、お友達を招いた時にお友達が思わず口から出た第一声です。\r\nその時一生懸命頑張ってお片付けをなさったお客様の喜ばしいお顔が目に浮かび、それがお手伝いをした私の何よりのご褒美でもあります。\r\n約10年前に始めた整理収納アドバイザーのお仕事、何とかお片付けの苦手なお客様の力になりたい一心で続けてまいりました。これからもお客様に寄り添うお仕事をしていきたいと思っていますので宜しくお願い致します。\r\n\r\n（初めて利用なされるお客様へ）\r\n整理収納はお片付けをするお部屋の広さやアイテムの量によって大幅に変わってきます。\r\n目安としますと、玄関や洗面所は狭い範囲ですので、大体1か所30分位で終わります。クローゼットはお洋服が少ない場合でも1時間位、リビングの片づけは1～2時間位、。キッチンはクローゼットと同様、物の多さ広さによって1～3時間位かかります。お家全体は1日～複数日になります。\r\n大まかな目安としてお考え下さいませ。\r\n\r\n⁑以下の内容の整理収納・お片付け・断捨離等承ります。⁑\r\n\r\n＜整理収納、片付け代行例＞■整理収納・・整理収納終了時床が汚れていた場合掃除機がけ、トイレの小物の整理収納、キッチン整理収納（収納する時点で棚や戸棚の拭き掃除含む）、洗面所の小物の整理収納掃除、クローゼットの整理収納、押し入れ、玄関の整理収納、断捨離、お引っ越しのお手伝い\r\n ■洗濯・・洗濯物の畳収納\r\n■その他・・片付け全般、整理整頓、片付けで出たゴミ出し\r\n上記内容に含まれないものでも対応可能なものもございますので、お気軽にお問い合わせください。',
    attachments: [
      {
        id: '22a2550d-4b1d-4296-9104-8265866a6761',
        service_id: '2c1f738e-88ca-4d33-823a-66d91c563636',
        attachment_kind_id: 1,
        path: 'service/SER484314851/6df4ef41-d061-4617-a3e6-b66074935a39.jpg',
        sort: 1,
        attachment_type_id: 1,
        title: null,
        is_published: true,
      },
      {
        id: 'ea4951e0-e081-4be4-998e-8380848b65ff',
        service_id: '2c1f738e-88ca-4d33-823a-66d91c563636',
        attachment_kind_id: 2,
        path: 'service/SER484314851/a553e1c0-56ed-47f1-80de-f98c3d5932ab.jpg',
        sort: 2,
        attachment_type_id: 1,
        title: null,
        is_published: true,
      },
      {
        id: 'e534a420-d980-470f-a86f-fde1549749e5',
        service_id: '2c1f738e-88ca-4d33-823a-66d91c563636',
        attachment_kind_id: 2,
        path: 'service/SER484314851/c5ea6321-f2b5-46a4-867b-4296c47dece0.jpg',
        sort: 3,
        attachment_type_id: 1,
        title: null,
        is_published: true,
      },
      {
        id: 'f6c56ca2-1028-4e59-a3a7-ece8c5b75677',
        service_id: '2c1f738e-88ca-4d33-823a-66d91c563636',
        attachment_kind_id: 2,
        path: 'service/SER484314851/ba28e539-609d-4f53-8ea3-a1e538eccc63.jpg',
        sort: 4,
        attachment_type_id: 1,
        title: null,
        is_published: true,
      },
      {
        id: '3281d230-c232-4b31-94e3-d4779ca87378',
        service_id: '2c1f738e-88ca-4d33-823a-66d91c563636',
        attachment_kind_id: 2,
        path: 'service/SER484314851/152f278e-ab42-4978-b6d2-cf54af6198c3.jpg',
        sort: 5,
        attachment_type_id: 1,
        title: null,
        is_published: true,
      },
      {
        id: 'e77b33b6-6832-4a35-81b9-046bccc31319',
        service_id: '2c1f738e-88ca-4d33-823a-66d91c563636',
        attachment_kind_id: 2,
        path: 'service/SER484314851/d191fbba-62ba-4a36-8639-85f8596e854f.jpg',
        sort: 6,
        attachment_type_id: 1,
        title: null,
        is_published: true,
      },
      {
        id: '3ec2c6e0-446c-4736-8670-3695a90bec79',
        service_id: '2c1f738e-88ca-4d33-823a-66d91c563636',
        attachment_kind_id: 2,
        path: 'service/SER484314851/d2bfb02c-b2be-4c7c-95d6-810a8c2cc69d.jpg',
        sort: 1000,
        attachment_type_id: 1,
        title: null,
        is_published: false,
      },
      {
        id: '4fcca693-1f6c-4a7c-a3e5-ecf887c6b481',
        service_id: '2c1f738e-88ca-4d33-823a-66d91c563636',
        attachment_kind_id: 2,
        path: 'service/SER484314851/33c2b55e-4ae6-497e-9467-1ea87d288bf1.jpg',
        sort: 1000,
        attachment_type_id: 1,
        title: null,
        is_published: false,
      },
      {
        id: '50f5d894-0535-4515-a12a-17ab2ec1c2b8',
        service_id: '2c1f738e-88ca-4d33-823a-66d91c563636',
        attachment_kind_id: 2,
        path: 'service/SER484314851/40dda64d-d0cd-411e-8897-68ff62298285.jpg',
        sort: 1000,
        attachment_type_id: 1,
        title: null,
        is_published: false,
      },
      {
        id: 'bdedf471-73d4-434a-bc14-16877c4ab6c4',
        service_id: '2c1f738e-88ca-4d33-823a-66d91c563636',
        attachment_kind_id: 2,
        path: 'service/SER484314851/7601c310-8c25-4e59-a2e8-b057975dcfec.jpg',
        sort: 1000,
        attachment_type_id: 1,
        title: null,
        is_published: false,
      },
    ],
    locations: [
      '04152324-1bc8-44a4-ae42-1cf2aba18b72',
      '1ba09682-c668-468d-9e4a-d64c1c0ed907',
      '2f8b81de-13b2-4757-b291-43de4691bd2b',
      '30b9aee3-3242-4aa7-8395-0869e00cd8d7',
      '30d76134-0d50-4591-a167-aac64013d5dd',
      '3244ae9c-04e5-4b02-9a4e-92d5cc53d402',
      '3bacdcb5-10f0-411b-a706-60106a4f730c',
      '3cb227b0-d324-4160-9df8-bb940b1be6ee',
      '4909d5be-05b0-4835-8b4c-a80e835f78c1',
      '4eb38608-32ab-4341-8e47-e03a8b5cb51e',
      '55038c4c-7faa-4cc5-8d9d-9a13d095c8a2',
      '559d5347-3387-4d2a-b07b-b3e2bd28e69b',
      '59e414d8-80b1-4efa-bdbe-b5b74ffdbc14',
      '62070104-bcea-4046-8e5e-745b237bb965',
      '71d84d8e-b79a-4bab-a8af-3b9a1e1ebf35',
      '7d16cf5b-e318-4f55-8f78-12f2e858f21d',
      'be49633a-2a7e-4b96-9a58-2aa7a7fb5c60',
      'bffd5bcc-8130-45ef-861b-9d617c1fb550',
      'cf8d557d-8b93-4159-9d1f-cc4ee474d965',
      'd571589a-d2e1-4a44-a6f1-fbe830fd35f0',
      'd757056d-e800-4bd5-91b2-44be7c118203',
      'da233cfd-9aad-4e0e-a306-79d703982279',
      'e8ce0191-5a97-4296-99c6-9144fcdd98b9',
      'ee36df60-cb23-45cd-b6d9-5203b10d9b42',
      'f1927e67-952b-4e23-9b21-672a7927979a',
      'f3f07cb7-819c-4c0a-8670-1b9fb2f58a5e',
    ],
    average_price: null,
    questions: [
      {
        id: '0fd73c46-336a-4d6c-a503-37bb4ced2368',
        question: '追加料金がかかる場合がありますか',
        answer: '当日の急な追加料金は発生しません。必ず、お客様の状況を事前におしらせください。',
        sort: 1,
      },
      {
        id: '13d2c890-8dea-43f4-b975-e14da27f1705',
        question: '無料で駐車できる場所がない場合はどうしたらいいですか？',
        answer: '付近の有料パーキングを使用しますが、その場合のパーキング代のご負担をお願いします。',
        sort: 2,
      },
      {
        id: '1e5115c0-4902-49ab-b0da-fa62cb74911e',
        question: '保険に加入されていますか？',
        answer: '損害保険に加入しております。',
        sort: 3,
      },
      {
        id: 'df1ba652-5558-4e70-b6dd-9e080b100dbc',
        question: '八王子市ですが、どちらでも来て頂けますか?',
        answer: '只今電車移動になりますので伺えない地域もございます。予めお伝えくださいませ！宜しくお願い致します。',
        sort: 4,
      },
    ],
    flowsteps: [
      {
        id: '7be56213-f6f4-4802-9244-c57cde112b51',
        title: 'ネット予約',
        text: 'このページにあるくらしのマーケットの予約ボタンをクリックして\nネット予約をしてください。',
        sort: 1,
      },
      {
        text: '当社から予約確認の連絡をさせて頂きます。',
        title: '予約確認',
        id: '205be3f7-f311-4e61-bc9f-1a56cfe1a595',
        sort: 2,
      },
      {
        id: '95caf5bd-a23f-46ba-bcf5-91eb2c3b7713',
        title: '打ち合わせ',
        text: '作業前に作業に関してお話を聞かせていただきます。',
        sort: 3,
      },
      {
        id: '51a68f54-ccef-42cb-b222-074280202a4d',
        title: '作業',
        text: '予約日時にお客様のところに伺い、作業いたします。',
        sort: 4,
      },
      {
        id: 'fe54f495-919e-4511-bfc2-73f89a1187ba',
        title: 'お支払い',
        text: '予約時に選択された方法で作業料金をお支払いください。お支払い完了後は領収書などを受け取り、金額をご確認ください。',
        sort: 5,
      },
    ],
    features: [
      {
        id: '52b393b5-0119-4801-ad50-4091e7fe3d37',
        name: 'オンラインカード決済可',
        description: 'オンラインカード決済が利用出来る場合',
        sort: 1,
      },
      {
        id: '9228ed81-ca75-411d-9845-37c01a59c226',
        name: '追加料金一切なし',
        description: '駐車場代などの追加料金を、お客様に一切請求しない場合',
        sort: 3,
      },
      {
        id: 'f1e150a5-7abe-43b5-b792-3fec4c9ac15e',
        name: '作業外注一切なし',
        description: '他社に一切外注せず、必ず自社スタッフが作業できる場合',
        sort: 4,
      },
    ],
    option_component_ids: [],
    average_prices: [],
    campaign_id: null,
    discount_rate: null,
    campaign_service_features: [],
  },
};

const serviceDetailPost = {
  serviceId: '2c1f738e-88ca-4d33-823a-66d91c563636',
  serviceTypeId: '18ce42a6-8b62-42ca-af6f-bc1f0a0e71e9',
  storeId: 'df06ea98-5374-4936-9139-4fda025a77d5',
  serviceName: '【迅速・丁寧に対応します】100%自社対応♪損害保険加入済みで安心◎',
  catchCopy:
    '花と雑貨のお店開業、病院勤務経験有り\r\n\r\n整理収納アドバイザー1級保有！\r\n【整理収納や片付け代行、断捨離】に対応しています',
  salesPoint:
    'ご覧いただきありがとうございます。\r\n\r\n★一人暮らしの女性も安心してお任せください\r\n★これまでに培った経験と実績でお客様のご期待に必ずお応えします！\r\n★営業時間外・対応地域外でもご要望お聞きします！\r\n\r\nまずはお気軽にご相談ください。',
  requestToCustomers:
    '・お客様のご自宅の清掃用具を使用させていただきます。\r\n・ベビーシッターおよび未就学児の対応はいたしておりません。\r\n\r\n＜ご予約に関する注意事項＞\r\n・直前でのご予約には対応できません。最低3日以上の余裕をもって、ご予約ください。\r\n・ご予約の日時が、当社の営業時間内であることをご確認ください。\r\n・予約の日程候補は、なるべく日時をずらしてください。\r\n・予約が集中し、ご希望の日時に伺えない場合がございます。その場合は、[メッセージ]にて改めて日時をお伺いします。',
  serviceDetails: '（店長のご挨拶',
  basePrice: 5250,
  questions: [
    {
      question: '追加料金がかかる場合がありますか',
      answer: '当日の急な追加料金は発生しません。必ず、お客様の状況を事前におしらせください。',
      sort: 1,
    },
    {
      question: '無料で駐車できる場所がない場合はどうしたらいいですか？',
      answer: '付近の有料パーキングを使用しますが、その場合のパーキング代のご負担をお願いします。',
      sort: 2,
    },
    {
      question: '保険に加入されていますか？',
      answer: '損害保険に加入しております。',
      sort: 3,
    },
    {
      question: '八王子市ですが、どちらでも来て頂けますか?',
      answer: '只今電車移動になりますので伺えない地域もございます。予めお伝えくださいませ！宜しくお願い致します。',
      sort: 4,
    },
  ],
  flowsteps: [
    {
      title: 'ネット予約',
      text: 'このページにあるくらしのマーケットの予約ボタンをクリックして\nネット予約をしてください。',
      sort: 1,
    },
    {
      text: '当社から予約確認の連絡をさせて頂きます。',
      title: '予約確認',
      sort: 2,
    },
    {
      title: '打ち合わせ',
      text: '作業前に作業に関してお話を聞かせていただきます。',
      sort: 3,
    },
    {
      title: '作業',
      text: '予約日時にお客様のところに伺い、作業いたします。',
      sort: 4,
    },
    {
      title: 'お支払い',
      text: '予約時に選択された方法で作業料金をお支払いください。お支払い完了後は領収書などを受け取り、金額をご確認ください。',
      sort: 5,
    },
  ],
  features: [
    {
      id: '52b393b5-0119-4801-ad50-4091e7fe3d37',
    },
    {
      id: '9228ed81-ca75-411d-9845-37c01a59c226',
    },
    {
      id: 'f1e150a5-7abe-43b5-b792-3fec4c9ac15e',
    },
  ],
};

export const MockDataPy3Instance = {
  shopAccount,
  categoryDetail,
  serviceDetail,
  serviceDetailPost,
};
